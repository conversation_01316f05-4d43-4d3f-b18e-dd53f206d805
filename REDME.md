# 项目名称
运营管理平台
## 简介
运营后台管理系统，基于Spring生态技术栈。实现业务数据自定义配置，业务场景动态配置，指标自由采集。
构建后台管理系统，实现业务逻辑的分离，提高系统的可维护性。独创组件式项目架构设计，满足微服务与单体架构的兼容性。

## 项目结构
```
|── sdata-ops-backend   
├── pom.xml                     -- 项目总 pom 文件
├── sdata-ops-api               -- 项目rpc模块
│   ├── pom.xml     
│   ├── sdata-ops-flow-api
│   ├── sdata-ops-indicator-api
│   └── sdata-ops-system-api
├── sdata-ops-app               -- 项目主应用模块
│   ├── pom.xml
│   ├── sdata-ops-flow
│   ├── sdata-ops-indicator
│   └── sdata-ops-system
├── sdata-ops-base              -- 项目model模块   
│   ├── pom.xml
│   ├── sdata-ops-flow-model
│   ├── sdata-ops-indicator-model
│   └── sdata-ops-system-model
├── sdata-ops-common            -- 项目公共模块
│   ├── pom.xml
│   └── src
├── sdata-ops-micro             -- 项目微服务模块
│   ├── pom.xml
│   ├── sdata-ops-micro-flow
│   ├── sdata-ops-micro-gateway
│   └── sdata-ops-micro-system
└── sdata-ops-standalone        -- 项目独立应用模块
├── logs
├── pom.xml                     -- 项目根 pom 文件
└── src
```

## 特性
- 项目架构同时满足单体与微服务
- 系统支持在线配置数据接口，避免测试环境数据不全与迭代上线困扰
- 系统支持数据采集指标自定义采集，支持指标数据采集，支持指标数据展示

## 技术栈
- Spring、SpringCloudAlibaba、magic-api、anyLine
- graalvm-js

## 安装
详细说明如何安装和配置项目。包括但不限于：
- 环境要求 maven3.x jdk11.x springboot2.x graalvm-js 
- 依赖安装 
- 配置文件设置

### 环境要求
- Java 11 或更高版本
- MySQL 8.0 或更高版本

### 依赖安装
- 使用 Maven 或 Gradle 等构建工具管理依赖

### 配置文件设置
- 配置数据库连接信息
- 其他配置项

## 使用方法

- 启动项目
- 常用命令
- API 文档

### 启动项目
- 使用 IDE 启动项目
- ![img.png](img.png)

### 常用命令
- 构建项目  maven clean install
- 运行测试  maven test
- 打包项目  maven package

### API 文档
- 提供 API 文档链接或说明

### 代码规范
- 代码风格遵循 Java 语言规范
- 提交信息遵循 sonar 安全审查

## 许可证
说明项目的许可证类型。

## 联系方式
<EMAIL>

