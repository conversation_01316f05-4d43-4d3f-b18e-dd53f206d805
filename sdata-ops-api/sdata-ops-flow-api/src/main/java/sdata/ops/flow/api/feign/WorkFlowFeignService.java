package sdata.ops.flow.api.feign;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.flow.model.vo.FlowTestVO;
import sdata.ops.base.flow.model.vo.OpsAiWorkflowVO;
import sdata.ops.common.api.R;

import java.util.Map;

@FeignClient(contextId = "sdataflowTestFeignService",name = "sdata-flow-engine", url = "${micro-service.local-switch-url}")
public interface WorkFlowFeignService {


    @GetMapping("/workflow/getByForFeign")
    OpsAiWorkflow getFlowById(@RequestParam("id")String id);

    /**
     * 通过任务启动流程实例
     *
     * @param processDefinitionId 流程定义ID
     * @param userId 用户ID
     * @param variables 流程变量
     * @return 流程实例ID
     */
    @PostMapping("/bpm/process/startByTask")
    R<String> startProcessByTask(
        @RequestParam String processDefinitionId,
        @RequestParam String userId,
        @RequestBody Map<String, Object> variables
    );

}
