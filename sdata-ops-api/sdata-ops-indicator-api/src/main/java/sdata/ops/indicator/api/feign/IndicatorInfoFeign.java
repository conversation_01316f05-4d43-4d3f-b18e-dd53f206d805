package sdata.ops.indicator.api.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.common.api.R;

import java.util.Map;

@FeignClient(url = "${micro-service.local-switch-url}",contextId = "indicatorInfoFeign",name = "sdata-indicator")
public interface IndicatorInfoFeign {



    @PostMapping("/indicator/rpc")
    R<Object>  rpc(@RequestBody IndicatorInfoDTO dto);

    @GetMapping("/xxxxx")
    R<Object>  onceExeGenSpecTask(String replicaId);


    @GetMapping("/metric-basic/metric-relation")
    R<Object>  getMetricRelation(String flowId);


    @GetMapping("/metric-basic/metric-perm")
    R<Object>  getMetricRelationForPerm(String permId);

    /**
     * 根据指标ID查询指标详情
     * @param id 指标ID
     * @return 指标详情
     */
    @GetMapping("/metric-basic/getById")
    R<Object> getMetricById(@RequestParam("id") String id);

    /**
     * 执行指标查询（监控中心调用）
     * @param metricId 指标ID
     * @param input 输入参数
     * @return 指标执行结果
     */
    @PostMapping("/metric-basic/execute")
    R<Object> executeMetric(@RequestParam("metricId") String metricId, @RequestBody Map<String, Object> input);

    /**
     * 执行工作流用于触发器调用
     * 工作流必须在返回的Map中包含"triggerResult"字段（Boolean类型）
     * 可选包含"taskInfo"字段（Map类型），用于传递任务创建信息
     *
     * @param flowId 工作流ID
     * @param variables 执行参数
     * @return 触发器执行结果
     */
    @PostMapping("/indicator/workflow/executeForTrigger")
    R<WorkflowTriggerResultVO> executeWorkflowForTrigger(@RequestParam("flowId") String flowId, @RequestBody Map<String, Object> variables);
}
