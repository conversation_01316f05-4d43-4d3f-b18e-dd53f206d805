<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>sdata-ops-api</artifactId>
        <groupId>sdata.ops.platform</groupId>
        <version>24.3.1</version>
    </parent>

    <artifactId>sdata-ops-indicator-api</artifactId>
    <version>24.3.1</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-model</artifactId>
            <version>24.3.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>