stages:
  - build
  - deploy

variables:
  PROJECT_DIR: "/usr/local/sdata/ops_product/backend"
  BUILD_DIR: "build_${CI_PIPELINE_ID}"
  TARGET_JAR_NAME: "sdata-ops-standalone-company.jar"

workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev-com"'
      when: always
    - when: never

build:
  stage: build
  tags:
    - ops
  script:
    - |
      if [ ! -d "$BUILD_DIR" ]; then
        echo "Creating build directory and copying files..."
        mkdir -p "$BUILD_DIR"
        # 使用find+cp复制文件，排除.git目录和已有的构建目录
        find . -maxdepth 1 -not -name '.' -not -name '.git' -not -name 'build_*' -exec cp -r {} "$BUILD_DIR/" \;
      fi
    - cd "$BUILD_DIR"
    - echo "Building with Maven..."
    - mvn clean package -DskipTests
    - cd sdata-ops-standalone/target
  artifacts:
    paths:
      - $BUILD_DIR/sdata-ops-standalone/target/$TARGET_JAR_NAME
    expire_in: 1 hour

deploy:
  stage: deploy
  tags:
    - ops
  script:
    - echo "Deploying application..."
    - echo "Cleaning old JAR files..."
    - sudo find "$PROJECT_DIR" -maxdepth 1 -type f -name "*.jar" -delete
    - echo "Copying new JAR..."
    - sudo cp -v $BUILD_DIR/sdata-ops-standalone/target/$TARGET_JAR_NAME $PROJECT_DIR/$TARGET_JAR_NAME
    - echo "Updating permissions..."
    - sudo chown -v gitlab-runner:gitlab-runner $PROJECT_DIR/$TARGET_JAR_NAME
    - echo "Restarting service..."
    - cd $PROJECT_DIR
    - sudo -u gitlab-runner ./service.sh restart
    - echo "Verifying service is running..."
    - sleep 10
    - ps -ef | grep "$TARGET_JAR_NAME" | grep -v grep || { echo "Service not running!"; exit 1; }
  needs:
    - build