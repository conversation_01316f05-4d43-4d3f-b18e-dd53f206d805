-- 任务中心@Scheduled任务迁移到调度中心OpsQuartzJob配置数据
-- 执行前请确保已备份相关数据
-- 执行后需要通过调度中心管理界面启动对应任务

-- 1. 每日任务清单生成
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression, 
    misfire_policy, concurrent, status, job_type, 
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '每日任务清单生成', 
    'TASK_CENTER_GROUP',
    'systemTaskBridge.creatTask()',
    '0 0 1 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '每日凌晨1点生成任务清单，含工作日检查和分布式锁1001',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 2. 三方系统数据抓取
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '三方系统数据抓取',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.obtThirdSystemDataForSch()',
    '0 0 6-22 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '每小时执行，抓取OA与邮件数据，含工作日检查',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 3. 任务可见性轮询更新
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '任务可见性轮询更新',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer()',
    '0 0 6-22 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '轮询执行脚本更新任务可见性，含工作日检查',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 4. 增量任务创建
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '增量任务创建',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.insertTaskForOaSystemOrMailServer()',
    '0 0 6-22 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '基于OA/邮件命中增量创建任务，含工作日检查和分布式锁1002',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 5. 特殊明细任务更新
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '特殊明细任务更新',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.updateSpecDetailTask()',
    '0 0 6-22 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '特殊明细任务更新抓取数据写入明细表',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 6. 特殊时间类型脚本执行
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '特殊时间类型脚本执行',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.updateTaskOnlyOnce()',
    '0 0 2 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '每日凌晨2点执行一次，特殊时间周期类型脚本，含工作日检查',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 7. 延期任务状态扫描
INSERT INTO ops_quartz_job (
    job_name, job_group, invoke_target, cron_expression,
    misfire_policy, concurrent, status, job_type,
    market, time_type, remark, create_by, create_time, update_by, update_time
) VALUES (
    '延期任务状态扫描',
    'TASK_CENTER_GROUP',
    'systemTaskBridge.timerEverDayScannerDelayTaskStatus()',
    '0 0 13,15,17,22 * * ?',
    '0',
    '1',
    '1',
    'dynamicTask',
    '0',
    'custom',
    '每天下午1点、3点、5点、10点扫描延期任务状态',
    'system',
    NOW(),
    'system',
    NOW()
);

-- 执行说明：
-- 1. 执行上述SQL后，7个任务将以"暂停"状态(status='1')插入到调度中心
-- 2. 需要通过调度中心管理界面(/indicator/scheduler)逐个启动任务
-- 3. 启动前请确保SystemTaskBridge已部署并可正常调用SystemFeignService
-- 4. 建议先启动一个任务进行测试，确认无误后再启动其他任务
-- 5. 原@Scheduled任务可以通过注释@EnableScheduling或@Scheduled注解来禁用

-- 测试验证步骤：
-- 1. 访问调度中心管理界面：http://localhost:8080/ops/indicator/scheduler/page
-- 2. 找到TASK_CENTER_GROUP组的任务
-- 3. 选择一个任务点击"启动"
-- 4. 点击"执行一次"测试任务是否正常运行
-- 5. 查看执行日志确认任务执行结果
-- 6. 确认无误后启动其他任务

-- 回滚方案（如需回滚）：
-- DELETE FROM ops_quartz_job WHERE job_group = 'TASK_CENTER_GROUP';
-- 然后恢复原@Scheduled任务的@EnableScheduling注解
