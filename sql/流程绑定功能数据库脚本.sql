-- 流程绑定功能数据库脚本
-- 创建时间：2024-09-28
-- 说明：为任务单元和任务表添加流程相关字段

-- =====================================================
-- 1. 任务单元基础表 (ops_task_attr_basic)
-- =====================================================
ALTER TABLE `ops_task_attr_basic` 
ADD COLUMN `process_definition_id` VARCHAR(64) NULL COMMENT '流程定义ID，关联Flowable的ProcessDefinition.id' AFTER `access_level`,
ADD COLUMN `process_instance_id` VARCHAR(64) NULL COMMENT '流程实例ID，关联Flowable的ProcessInstance.id' AFTER `process_definition_id`;

-- =====================================================
-- 2. 任务单元副本表 (ops_task_attr_basic_replica)
-- =====================================================
ALTER TABLE `ops_task_attr_basic_replica` 
ADD COLUMN `process_definition_id` VARCHAR(64) NULL COMMENT '流程定义ID，关联Flowable的ProcessDefinition.id' AFTER `access_level`,
ADD COLUMN `process_instance_id` VARCHAR(64) NULL COMMENT '流程实例ID，关联Flowable的ProcessInstance.id' AFTER `process_definition_id`;


-- =====================================================
-- 3. 任务生成信息表 (ops_task_gen_info)
-- =====================================================
ALTER TABLE `ops_task_gen_info` 
ADD COLUMN `process_definition_id` VARCHAR(64) NULL COMMENT '流程定义ID，关联Flowable的ProcessDefinition.id' AFTER `task_gen_time`,
ADD COLUMN `process_instance_id` VARCHAR(64) NULL COMMENT '流程实例ID，关联Flowable的ProcessInstance.id' AFTER `process_definition_id`;
