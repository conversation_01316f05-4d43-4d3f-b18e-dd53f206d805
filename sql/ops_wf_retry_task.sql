
CREATE TABLE `ops_wf_retry_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务id',
  `task_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务名称',
  `process_instance_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流程实例id',
  `execution_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行id',
  `task_flow_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作流id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='重试任务表';