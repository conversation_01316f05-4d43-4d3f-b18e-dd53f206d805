package sdata.ops.flowable.utils;

import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.query.Query;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.history.NativeHistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.task.api.NativeTaskQuery;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import sdata.ops.base.flow.model.vo.ProcessQuery;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.flow.utils.DateUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程工具类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public class ProcessUtils {
    private static final String END_TIME_FIELD = "endTime";
    private static final String BEGIN_TIME_FIELD = "beginTime";

    private ProcessUtils() {
        throw new UnsupportedOperationException("ProcessUtils is a utility class and cannot be instantiated");
    }

    public static void buildProcessSearch(Query<?, ?> query, ProcessQuery process) {
        if (query instanceof ProcessDefinitionQuery) {
            buildProcessDefinitionSearch((ProcessDefinitionQuery) query, process);
        } else if (query instanceof TaskQuery) {
            buildTaskSearch((TaskQuery) query, process);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            buildHistoricTaskInstanceSearch((HistoricTaskInstanceQuery) query, process);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            buildHistoricProcessInstanceSearch((HistoricProcessInstanceQuery) query, process);
        }
    }


    /**
     * 构建流程定义搜索
     */
    public static void buildProcessDefinitionSearch(ProcessDefinitionQuery query, ProcessQuery process) {
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        // 流程状态
        if (StringUtils.isNotBlank(process.getState())) {
            if (SuspensionState.ACTIVE.toString().equals(process.getState())) {
                query.active();
            } else if (SuspensionState.SUSPENDED.toString().equals(process.getState())) {
                query.suspended();
            }
        }
    }

    /**
     * 构建任务搜索
     */
    public static void buildTaskSearch(TaskQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        if (params.get(BEGIN_TIME_FIELD) != null && params.get(END_TIME_FIELD) != null) {
            query.taskCreatedAfter(DateUtils.parseDate(params.get(BEGIN_TIME_FIELD)));
            query.taskCreatedBefore(DateUtils.parseDate(params.get(END_TIME_FIELD)));
        }
    }

    private static void buildHistoricTaskInstanceSearch(HistoricTaskInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        if (params.get(BEGIN_TIME_FIELD) != null && params.get(END_TIME_FIELD) != null) {
            query.taskCompletedAfter(DateUtils.parseDate(params.get(BEGIN_TIME_FIELD)));
            query.taskCompletedBefore(DateUtils.parseDate(params.get(END_TIME_FIELD)));
        }
    }

    /**
     * 构建历史流程实例搜索
     */
    public static void buildHistoricProcessInstanceSearch(HistoricProcessInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKey(process.getProcessKey());
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processInstanceNameLike("%"+process.getProcessName()+"%");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        if (params.get(BEGIN_TIME_FIELD) != null && params.get(END_TIME_FIELD) != null) {
            query.startedAfter(DateUtils.parseDate(params.get(BEGIN_TIME_FIELD)));
            query.startedBefore(DateUtils.parseDate(params.get(END_TIME_FIELD)));
        }
    }



    public static NativeHistoricProcessInstanceQuery buildNativeProcessSearch(ProcessQuery process, List<SystemUser> allPermissionUser, HistoryService historyService) {

        String nativeSql = "SELECT\n" +
            "  RES.*,\n" +
            "  DEF.KEY_ AS PROC_DEF_KEY_,\n" +
            "  DEF.NAME_ AS PROC_DEF_NAME_,\n" +
            "  DEF.VERSION_ AS PROC_DEF_VERSION_,\n" +
            "  DEF.DEPLOYMENT_ID_ AS DEPLOYMENT_ID_\n" +
            "FROM\n" +
            "  ACT_HI_PROCINST RES\n" +
            "  LEFT OUTER JOIN ACT_RE_PROCDEF DEF\n" +
            "    ON RES.PROC_DEF_ID_ = DEF.ID_\n" +
            "WHERE RES.START_USER_ID_ in ( " + allPermissionUser.stream().map(SystemUser::getId).collect(Collectors.joining(",")) + ")\n";
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            nativeSql = nativeSql.concat(" AND DEF.KEY_ = '" + process.getProcessKey() + "'");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            nativeSql = nativeSql.concat(" AND RES.NAME_ like '%" + process.getProcessName() + "%'");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            nativeSql = nativeSql.concat(" AND DEF.CATEGORY_ = '" + process.getCategory() + "'");
        }
        if (process.getParams().get(BEGIN_TIME_FIELD) != null && process.getParams().get(END_TIME_FIELD) != null) {
            //RES.START_TIME_ <= '2025-07-12 23:59:59' and RES.START_TIME_ >= '2025-07-11 00:00:00'
            nativeSql = nativeSql.concat(" AND RES.START_TIME_ <= #{endTime} ");
            nativeSql = nativeSql.concat(" AND RES.START_TIME_ >= #{startTime} ");
        }
        nativeSql = nativeSql.concat("  ORDER BY RES.START_TIME_ DESC ");
        NativeHistoricProcessInstanceQuery query = historyService.createNativeHistoricProcessInstanceQuery().sql(nativeSql);
        if (process.getParams().get(BEGIN_TIME_FIELD) != null && process.getParams().get(END_TIME_FIELD) != null) {
            query.parameter(BEGIN_TIME_FIELD, DateUtils.parseDate(process.getParams().get(BEGIN_TIME_FIELD)));
            query.parameter(END_TIME_FIELD, DateUtils.parseDate(process.getParams().get(END_TIME_FIELD)));
        }
        return query;
    }

    public static NativeTaskQuery buildTaskQueryProcessSearch(ProcessQuery process, List<SystemUser> allPermissionUser, TaskService taskService) {
        String userIds = allPermissionUser.stream()
            .map(i -> "'" + i.getId() + "'")
            .collect(Collectors.joining(","));
        String nativeSql =
            "select\n" +
            "RES.*,\n" +
            "VAR.ID_ as VAR_ID_,\n" +
            "VAR.NAME_ as VAR_NAME_,\n" +
            "VAR.TYPE_ as VAR_TYPE_,\n" +
            "VAR.REV_ as VAR_REV_,\n" +
            "VAR.PROC_INST_ID_ as VAR_PROC_INST_ID_,\n" +
            "VAR.EXECUTION_ID_ as VAR_EXECUTION_ID_,\n" +
            "VAR.TASK_ID_ as VAR_TASK_ID_,\n" +
            "VAR.BYTEARRAY_ID_ as VAR_BYTEARRAY_ID_,\n" +
            "VAR.DOUBLE_ as VAR_DOUBLE_,\n" +
            "VAR.TEXT_ as VAR_TEXT_,\n" +
            "VAR.TEXT2_ as VAR_TEXT2_,\n" +
            "VAR.LONG_ as VAR_LONG_,\n" +
            "VAR.SCOPE_ID_ as VAR_SCOPE_ID_,\n" +
            "VAR.SUB_SCOPE_ID_ as VAR_SUB_SCOPE_ID_,\n" +
            "VAR.SCOPE_TYPE_ as VAR_SCOPE_TYPE_\n" +
            "from\n" +
            "(\n" +
            "select\n" +
            "RES.*\n" +
            "from\n" +
            "ACT_RU_TASK RES\n" +
            "where\n" +
            "RES.SUSPENSION_STATE_ = 1\n" +
            "and (RES.ASSIGNEE_  in (" + userIds + "))\n"+"order by\n" +
            "RES.CREATE_TIME_ desc ) RES\n" +
            "left outer join ACT_RU_VARIABLE VAR on\n" +
                "(RES.PROC_INST_ID_ = VAR.EXECUTION_ID_)\n" +
                "order by\n" +
                "RES.CREATE_TIME_ desc";
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            nativeSql = nativeSql.concat(" AND RES.PROC_DEF_ID_ = '" + process.getProcessKey() + "'");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            nativeSql = nativeSql.concat(" AND DEF.NAME_ like '%" + process.getProcessName() + "%'");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            nativeSql = nativeSql.concat(" AND DEF.CATEGORY_ = '" + process.getCategory() + "'");
        }
        NativeTaskQuery query = taskService.createNativeTaskQuery().sql(nativeSql);
        if (process.getParams().get(BEGIN_TIME_FIELD) != null && process.getParams().get(END_TIME_FIELD) != null) {
            query.parameter("CREATE_TIME", DateUtils.parseDate(process.getParams().get(BEGIN_TIME_FIELD)));
            query.parameter("CREATE_TIME", DateUtils.parseDate(process.getParams().get(END_TIME_FIELD)));
        }
        return query;
    }
}
