package sdata.ops.flowable.utils;

import lombok.extern.slf4j.Slf4j;
import sdata.ops.base.flow.model.vo.FormConf;
import sdata.ops.common.core.util.JsonUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 流程表单工具类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
public class ProcessFormUtils {

    private static final String FIELD = "field";
    public static final String DEFAULT_VALUE_KEY = "defaultValue";

    /**
     * 填充表单项内容
     *
     * @param formContent 表单配置信息
     * @param data        表单内容
     */
    public static FormConf fillFormData(String formContent, Map<String, Object> data) {
        FormConf vo = JsonUtils.parseObject(formContent, FormConf.class);
        if (vo == null) {
            return null;
        }
        for (Map<String, Object> field : vo.getFields()) {
            recursiveFillField(field, data);
        }
        return vo;
    }

    @SuppressWarnings("unchecked")
    private static void recursiveFillField(final Map<String, Object> field, final Map<String, Object> data) {
        // 表单name
        Object fieldValue = field.get(FIELD);
        if (fieldValue == null) {
            return;
        }
        if (!data.containsKey((String) fieldValue)) {
            return;
        }
        log.info("vo:{},variables:{}", field, data);
        Optional.of(field)
                .map(e -> (Map<String, Object>) e.get("component"))
                .map(e -> (Map<String, Object>) e.get("props"))
                .map(e -> {
                    Object value = data.get(fieldValue);
                    e.put(DEFAULT_VALUE_KEY, value);
                    return e;
                });
        // 上面的逻辑可以改用ognl来做，类似XPath指定字段直接赋值
//        try {
//            Map context = Ognl.createDefaultContext(field);
//            Ognl.setValue("#{field.component.props.defaultValue}", context, field, data.get(fieldValue));
//        } catch (OgnlException e) {
//            throw new RuntimeException(e);
//        }
    }

    /**
     * 解析表单数据
     *
     * @param content   提供key
     * @param variables 提供value
     * @return 整个表单的key-value
     */
    public static Map<String, Object> getFormData(String content, Map<String, Object> variables) {
        FormConf vo = JsonUtils.parseObject(content, FormConf.class);
        if (vo == null) {
            return Map.of();
        }
        Map<String, Object> data = new LinkedHashMap<>();
        for (Map<String, Object> field : vo.getFields()) {
            Object o = field.get(FIELD);
            data.put((String) o, variables.get(o));
        }
        return data;
    }
}
