package sdata.ops.flowable.utils;

import cn.dev33.satoken.stp.StpUtil;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.common.core.util.SaUtil;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.flowable.common.constant.TaskConstants;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 工作流任务工具类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface TaskUtils {

    static String getUserId() {
        return StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";
    }

    static String nickname() {
        return SaUtil.nickname();
    }

    static Collection<String> getCandidateGroup() {
        List<String> list = new ArrayList<>(16);
        String uid = StpUtil.getLoginIdAsString();
        SystemFeignService systemFeignService = SpringBeanUtil.getBean(SystemFeignService.class);
        // 所有角色
        for (String id : systemFeignService.selectMyRoleIds(uid)) {
            list.add(TaskConstants.ROLE_GROUP_PREFIX + id);
        }
        // 所有组织
        for (String id : systemFeignService.selectMyOrgIds(uid)) {
            list.add(TaskConstants.POST_GROUP_PREFIX + id);
        }
        return list;
    }

    /**
     * 当前登录人
     *
     * @return 当前登录人
     */
    static SystemUser currentUser() {
        Object obj = StpUtil.getSession().get("user");
        return ((SystemUser) obj);
    }
}
