package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.entity.WfCategory;
import sdata.ops.base.flow.model.vo.WfCategoryVo;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfCategoryService;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 流程分类Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/cat")
public class WfCategoryController {

    private final IWfCategoryService categoryService;

    /**
     * 查询流程分类列表
     */
    @SaCheckPermission("workflow:category:list")
    @ControllerAuditLog(value = "流程分类-分页", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/page")
    public R<Object> page(WfCategory category, PageQuery pageQuery) {
        Page<WfCategoryVo> voPage = categoryService.queryPageList(category, pageQuery);
        return R.data(voPage);
    }

    /**
     * 查询全部的流程分类列表
     */
    @SaCheckLogin
    @GetMapping("/list")
    @ControllerAuditLog(value = "流程分类-列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    public R<List<WfCategoryVo>> listAll(WfCategory category) {
        return R.data(categoryService.queryList(category));
    }

    /**
     * 获取流程分类详细信息
     *
     * @param categoryId 分类主键
     */
    @SaCheckPermission("workflow:category:query")
    @GetMapping("/{categoryId}")
    @ControllerAuditLog(value = "流程分类-详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    public R<WfCategoryVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("categoryId") Long categoryId) {
        return R.data(categoryService.queryById(categoryId));
    }

    /**
     * 新增流程分类
     */
    @SaCheckPermission("workflow:category:add")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程分类-新增", operateType = OperateType.INSERT)
    @PostMapping()
    public R<Object> add(@Validated @RequestBody WfCategory category) {
        return R.data(categoryService.insertCategory(category));
    }

    /**
     * 修改流程分类
     */
    @SaCheckPermission("workflow:category:edit")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程分类-修改", operateType = OperateType.UPDATE)
    @PutMapping()
    public R<Object> edit(@Validated @RequestBody WfCategory category) {
        return R.data(categoryService.updateCategory(category));
    }

    /**
     * 删除流程分类
     *
     * @param categoryIds 分类主键串
     */
    @SaCheckPermission("workflow:category:remove")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程分类-删除", operateType = OperateType.DELETE)
    @DeleteMapping("/{categoryIds}")
    public R<Object> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] categoryIds) {
        if (ArrayUtils.isEmpty(categoryIds)) {
            return R.fail("主键不能为空");
        }
        return R.data(categoryService.deleteWithValidByIds(Arrays.asList(categoryIds)));
    }
}
