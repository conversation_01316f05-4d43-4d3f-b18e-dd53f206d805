package sdata.ops.flow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.vo.ProcessQuery;
import sdata.ops.base.flow.model.vo.WfDeployVo;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022/6/30 9:03
 */
public interface IWfDeployService {

    Page<WfDeployVo> queryPageList(ProcessQuery processQuery, PageQuery pageQuery);

    Page<WfDeployVo> publishPage(String processKey, PageQuery pageQuery);

    void updateState(String definitionId, String stateCode);

    String queryBpmnXmlById(String definitionId);

    void deleteByIds(List<String> deployIds);
}
