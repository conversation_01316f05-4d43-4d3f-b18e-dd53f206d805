package sdata.ops.flow.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import sdata.ops.common.api.R;

@Slf4j
@RestControllerAdvice(basePackages = "sdata.ops.flow.controller")
public class OpsFlowExceptionHandler {

    /**
     * 捕捉流程中心异常，不展示错误信息
     *
     * @param e 异常
     * @return 异常信息
     */
    @ExceptionHandler(value = OpsFlowException.class)
    public R<String> flowException(final OpsFlowException e) {
        log.error(e.getMessage(), e);
        return R.fail(e.getResultCode(), e.getMessage());
    }
}
