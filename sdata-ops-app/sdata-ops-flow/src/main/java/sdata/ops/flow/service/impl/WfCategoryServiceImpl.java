package sdata.ops.flow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ModelQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.entity.WfCategory;
import sdata.ops.base.flow.model.vo.WfCategoryVo;
import sdata.ops.common.api.ResultCode;
import sdata.ops.flow.exception.OpsFlowException;
import sdata.ops.flow.mapper.WfCategoryMapper;
import sdata.ops.flow.service.IWfCategoryService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 流程分类Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@RequiredArgsConstructor
@Service
public class WfCategoryServiceImpl extends ServiceImpl<WfCategoryMapper, WfCategory> implements IWfCategoryService {

    private @Resource RepositoryService repositoryService;

    @Override
    public WfCategoryVo queryById(Long categoryId) {
        return baseMapper.selectVoById(categoryId);
    }

    @Override
    public Page<WfCategoryVo> queryPageList(WfCategory category, PageQuery pageQuery) {
        LambdaQueryWrapper<WfCategory> lqw = buildQueryWrapper(category);
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }

    @Override
    public List<WfCategoryVo> queryList(WfCategory category) {
        LambdaQueryWrapper<WfCategory> lqw = buildQueryWrapper(category);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WfCategory> buildQueryWrapper(WfCategory category) {
        LambdaQueryWrapper<WfCategory> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(category.getCategoryName()), WfCategory::getCategoryName, category.getCategoryName());
        lqw.like(StringUtils.isNotBlank(category.getCode()), WfCategory::getCode, category.getCode());
        lqw.orderByDesc(WfCategory::getCategoryId);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertCategory(WfCategory categoryBo) {
        checkUnique(categoryBo);
        WfCategory add = BeanUtil.toBean(categoryBo, WfCategory.class);
        if (super.save(add)) {
            return add.getCategoryId();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(WfCategory categoryBo) {
        checkUnique(categoryBo);
        WfCategory update = BeanUtil.toBean(categoryBo, WfCategory.class);
        return super.updateById(update);
    }

    @Override
    public boolean deleteWithValidByIds(Collection<String> ids) {
        List<String> catNames = new ArrayList<>(ids.size());
        for (String id : ids) {
            WfCategoryVo vo = baseMapper.selectVoById(id);
            ModelQuery query = repositoryService.createModelQuery();
            // 只查已经部署的
            query.deployed();
            query.modelCategory(vo.getCode());
            long count = query.count();
            if (0L < count) {
                catNames.add(vo.getCategoryName());
            }
        }
        if (!catNames.isEmpty()) {
            throw new OpsFlowException(ResultCode.FLOW_CATEGORY_USED, catNames);
        }
        return super.removeBatchByIds(ids);
    }

    /**
     * 校验分类编码/名称是否唯一
     *
     * @param category 流程分类
     */
    private void checkUnique(WfCategory category) {
        Optional<WfCategory> fromDb = super.lambdaQuery()
                .select(WfCategory::getCategoryId, WfCategory::getCategoryName, WfCategory::getCode)
                .ne(ObjectUtil.isNotNull(category.getCategoryId()), WfCategory::getCategoryId, category.getCategoryId())
                .and(wr -> wr
                        .eq(WfCategory::getCode, category.getCode())
                        .or()
                        .eq(WfCategory::getCategoryName, category.getCategoryName()))
                .last("LIMIT 1")
                .oneOpt();

        if (fromDb.isPresent()) {
            WfCategory cat = fromDb.get();
            throw new OpsFlowException(ResultCode.FLOW_CATEGORY_EXIST, cat.getCode(), cat.getCategoryName());
        }
    }
}
