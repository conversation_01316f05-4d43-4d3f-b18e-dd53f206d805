package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.bo.WfFormBo;
import sdata.ops.base.flow.model.entity.WfDeployForm;
import sdata.ops.base.flow.model.validate.QueryGroup;
import sdata.ops.base.flow.model.vo.WfFormVo;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfDeployFormService;
import sdata.ops.flow.service.IWfFormService;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * 流程表单Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/form")
public class WfFormController {

    private final IWfFormService formService;

    private final IWfDeployFormService deployFormService;

    /**
     * 查询流程表单列表
     */
    @ControllerAuditLog(value = "流程表单-分页查表单", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:form:page")
    @GetMapping("/page")
    public R<Object> list(@Validated(QueryGroup.class) WfFormBo bo, PageQuery pageQuery) {
        IPage<WfFormVo> voPage = formService.queryPageList(bo, pageQuery);
        return R.data(voPage);
    }

    @ControllerAuditLog(value = "流程表单-表单列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:form:list")
    @GetMapping("/list")
    public R<List<WfFormVo>> listAll(WfFormBo bo) {
        return R.data(formService.queryList(bo));
    }

    /**
     * 获取流程表单详细信息
     *
     * @param formId 主键
     */
    @ControllerAuditLog(value = "流程表单-详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:form:query")
    @GetMapping(value = "/{formId}")
    public R<WfFormVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("formId") String formId) {
        return R.data(formService.queryById(formId));
    }

    /**
     * 新增流程表单
     */
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程表单-新增", operateType = OperateType.INSERT)
    @SaCheckPermission("workflow:form:add")
    @PostMapping
    public R<Object> add(@RequestBody WfFormBo bo) {
        return R.data(formService.insertForm(bo));
    }

    /**
     * 修改流程表单
     */
    @SaCheckPermission("workflow:form:edit")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程表单-修改", operateType = OperateType.UPDATE)
    @PutMapping
    public R<Object> edit(@RequestBody WfFormBo bo) {
        return R.data(formService.updateForm(bo));
    }

    /**
     * 删除流程表单
     *
     * @param formIds 主键串
     */
    @SaCheckPermission("workflow:form:remove")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程表单-删除", operateType = OperateType.DELETE)
    @DeleteMapping("/{formIds}")
    public R<Object> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] formIds) {
        return R.data(formService.deleteWithValidByIds(Arrays.asList(formIds)) ? 1 : 0);
    }


    /**
     * 挂载流程表单
     */
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程表单-部署", operateType = OperateType.UPDATE)
    @PostMapping("/addDeployForm")
    public R<Object> addDeployForm(@RequestBody WfDeployForm deployForm) {
        return R.data(deployFormService.insertWfDeployForm(deployForm));
    }
}
