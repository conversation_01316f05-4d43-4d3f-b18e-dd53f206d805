package sdata.ops.flow.exception;

import lombok.Getter;
import sdata.ops.common.api.IResultCode;

import javax.validation.constraints.NotNull;

/**
 * 流程中心业务异常
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Getter
public class OpsFlowException extends RuntimeException {
    /**
     * 异常代码
     */
    private final IResultCode resultCode;

    public OpsFlowException(@NotNull IResultCode resultCode, Object... args) {
        super(String.format(resultCode.getMessage(), args));
        this.resultCode = resultCode;
    }

    public OpsFlowException(@NotNull IResultCode resultCode, Throwable cause, Object... args) {
        super(String.format(resultCode.getMessage(), args), cause);
        this.resultCode = resultCode;
    }
}
