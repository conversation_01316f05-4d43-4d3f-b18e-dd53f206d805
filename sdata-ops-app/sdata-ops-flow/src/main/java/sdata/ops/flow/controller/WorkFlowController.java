package sdata.ops.flow.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dtflys.forest.annotation.Post;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.flow.model.vo.OpsAiWorkflowVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.OpsWorkflowService;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/workflow")
@RequiredArgsConstructor
public class WorkFlowController extends PageCustomController {

    private final OpsWorkflowService workflowService;


    private final SystemFeignService systemFeignService;

    private final IndicatorInfoFeign indicatorInfoFeign;

    //流程列表查询接口
    @ControllerAuditLog(value = "流程列表查询接口", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/list")
    public R<Object> list(@RequestParam(value = "title", required = false) String title,
                          @RequestParam(value = "type", required = false) Integer type,
                          @RequestParam(value = "category", required = false) String category) {
        LambdaQueryWrapper<OpsAiWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(title), OpsAiWorkflow::getTitle, title)
                .eq(type != null, OpsAiWorkflow::getType, type)
                .eq(StringUtils.hasText(category), OpsAiWorkflow::getCategory, category);
        //查询执行
        List<OpsAiWorkflow> list = workflowService.list(queryWrapper);
        return R.data(list.stream().map(i -> new OpsAiWorkflowVO().convertFromEntity(i, systemFeignService.idNameMapper())).collect(Collectors.toList()));
    }

    //流程列表查询分页接口
    @ControllerAuditLog(value = "流程列表查询分页接口", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/page")
    public R<Object> listPage(@RequestParam(value = "title", required = false) String title,
                              @RequestParam(value = "type", required = false) Integer type,
                              @RequestParam(value = "category", required = false) String category,
                              @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        LambdaQueryWrapper<OpsAiWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(title), OpsAiWorkflow::getTitle, title)
                .eq(type != null, OpsAiWorkflow::getType, type)
                .eq(StringUtils.hasText(category), OpsAiWorkflow::getCategory, category);
        return R.data(customPage(workflowService.page(new Page<>(pageNo, pageSize), queryWrapper), i -> new OpsAiWorkflowVO().convertFromEntity(i, systemFeignService.idNameMapper())));
    }

    //单条流程获取
    @ControllerAuditLog(value = "单条流程获取", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/getById")
    public R<Object> get(@RequestParam(value = "id") String id) {
        OpsAiWorkflow workflow = workflowService.getById(id);
        if (workflow == null) {
            return R.fail(MessageConstant.DATA_NOT_EXISTS);
        }
        return R.data(new OpsAiWorkflowVO().convertSingle(workflow));
    }

    //流程保存或者修改
    @ControllerAuditLog(value = "流程保存或者修改", operateType = OperateType.INSERT, moduleName = ModuleName.PROCESS)
    @PostMapping("/save")
    public R<Object> saveOrUpdate(@RequestBody OpsAiWorkflowVO workflow) {
        if (workflow.getId() != null) {
            workflow.setVersion(workflow.getVersion() + 1);
        }
        workflowService.saveOrUpdate(workflow.convertToEntity());
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    //流程保存或者修改
    @ControllerAuditLog(value = "流程保存或者修改", operateType = OperateType.INSERT, moduleName = ModuleName.PROCESS)
    @PostMapping("/copy")
    public R<Object> copySave(@RequestBody OpsAiWorkflowVO workflow) {
        if (workflow.getId() == null) {
            return R.fail(MessageConstant.PARAM_FAIL);
        }
        OpsAiWorkflow entity = workflowService.getById(workflow.getId());
        entity.setId(null);
        entity.setTitle(workflow.getTitle());
        entity.setPublished(0);
        entity.setVersion(1);
        workflowService.save(entity);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    //流程发布操作
    @ControllerAuditLog(value = "流程发布操作", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @GetMapping("/publish")
    public R<Object> publish(@RequestParam(value = "id") String id) {
        LambdaUpdateWrapper<OpsAiWorkflow> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsAiWorkflow::getPublished, 1);
        updateWrapper.eq(OpsAiWorkflow::getId, id);
        workflowService.update(updateWrapper);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    //流程取消发布操作
    @ControllerAuditLog(value = "流程取消发布操作", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @GetMapping("/cancelPublish")
    public R<Object> cancelPublish(@RequestParam(value = "id") String id) {
        LambdaUpdateWrapper<OpsAiWorkflow> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsAiWorkflow::getPublished, 0);
        updateWrapper.eq(OpsAiWorkflow::getId, id);
        workflowService.update(updateWrapper);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    //流程删除操作
    @ControllerAuditLog(value = "流程删除操作", operateType = OperateType.DELETE, moduleName = ModuleName.PROCESS)
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam(value = "id") String id) {
        //TODO 流程已发布且被引用的情况下禁止删除
        R<Object> res = indicatorInfoFeign.getMetricRelation(id);
        if (res.getData() != null && (Long) res.getData() > 0) {
            return R.fail("流程已发布且被引用,禁止删除");
        }
        workflowService.removeById(Long.parseLong(id));
        return R.success(MessageConstant.DELETE_SUCCESS);
    }


    @ControllerAuditLog(value = "单条流程获取（feign）", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/getByForFeign")
    public OpsAiWorkflow getByForFeign(@RequestParam(value = "id") String id) {
        return workflowService.getById(id);
    }

}
