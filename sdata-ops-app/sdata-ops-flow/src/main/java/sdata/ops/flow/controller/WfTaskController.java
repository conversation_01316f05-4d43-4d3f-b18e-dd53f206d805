package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.WfTaskBo;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfTaskService;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 工作流任务管理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/task")
public class WfTaskController {

    private final IWfTaskService flowTaskService;

    /**
     * 取消流程
     */
    @ControllerAuditLog(value = "流程任务-取消", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/stopProcess")
    @SaCheckPermission("workflow:process:cancel")
    public R stopProcess(@RequestBody WfTaskBo bo) {
        flowTaskService.stopProcess(bo);
        return sdata.ops.common.api.R.success("");
    }

    /**
     * 撤回流程
     */
    @ControllerAuditLog(value = "流程任务-撤回", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/revokeProcess")
    @SaCheckPermission("workflow:process:revoke")
    public R revokeProcess(@RequestBody WfTaskBo bo) {
        flowTaskService.revokeProcess(bo);
        return R.success("");
    }

    /**
     * 获取流程变量
     * @param taskId 流程任务Id
     */
    @ControllerAuditLog(value = "流程任务-获取流程变量", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/processVariables/{taskId}")
    @SaCheckPermission("workflow:process:query")
    public R processVariables(@PathVariable(value = "taskId") String taskId) {
        return R.data(flowTaskService.getProcessVariables(taskId));
    }

    /**
     * 审批任务
     */
    @ControllerAuditLog(value = "流程任务-审批任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/complete")
    @SaCheckPermission("workflow:process:approval")
    public R complete(@RequestBody WfTaskBo bo) {
        flowTaskService.complete(bo);
        return R.success("");
    }

    /**
     * 拒绝任务
     */
    @ControllerAuditLog(value = "流程任务-拒绝任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/reject")
    @SaCheckPermission("workflow:process:approval")
    public R taskReject(@RequestBody WfTaskBo taskBo) {
        flowTaskService.taskReject(taskBo);
        return R.success("");
    }

    /**
     * 退回任务
     */
    @ControllerAuditLog(value = "流程任务-退回任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/return")
    @SaCheckPermission("workflow:process:approval")
    public R taskReturn(@RequestBody WfTaskBo bo) {
        flowTaskService.taskReturn(bo);
        return R.success("");
    }

    /**
     * 获取所有可回退的节点
     */
    @ControllerAuditLog(value = "流程任务-获取所有可回退的节点", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/returnList")
    @SaCheckPermission("workflow:process:query")
    public R findReturnTaskList(@RequestBody WfTaskBo bo) {
        return R.data(flowTaskService.findReturnTaskList(bo));
    }

    /**
     * 删除任务
     */
    @ControllerAuditLog(value = "流程任务-删除任务", operateType = OperateType.DELETE, moduleName = ModuleName.PROCESS)
    @DeleteMapping(value = "/delete")
    @SaCheckPermission("workflow:process:approval")
    public R delete(@RequestBody WfTaskBo bo) {
        flowTaskService.deleteTask(bo);
        return R.success("");
    }

    /**
     * 认领/签收任务
     */
    @ControllerAuditLog(value = "流程任务-认领/签收任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/claim")
    @SaCheckPermission("workflow:process:claim")
    public R claim(@RequestBody WfTaskBo bo) {
        flowTaskService.claim(bo);
        return R.success("");
    }

    /**
     * 取消认领/签收任务
     */
    @ControllerAuditLog(value = "流程任务-取消认领/签收任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/unClaim")
    @SaCheckPermission("workflow:process:claim")
    public R unClaim(@RequestBody WfTaskBo bo) {
        flowTaskService.unClaim(bo);
        return R.success("");
    }

    /**
     * 委派任务
     */
    @ControllerAuditLog(value = "流程任务-委派任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/delegate")
    @SaCheckPermission("workflow:process:approval")
    public R delegate(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
            return R.fail("参数错误！");
        }
        flowTaskService.delegateTask(bo);
        return R.success("");
    }

    /**
     * 转办任务
     */
    @ControllerAuditLog(value = "流程任务-转办任务", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/transfer")
    @SaCheckPermission("workflow:process:approval")
    public R transfer(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
            return R.fail("参数错误！");
        }
        flowTaskService.transferTask(bo);
        return R.success("");
    }

    /**
     * 生成流程图
     *
     * @param processId 任务ID
     */
    @ControllerAuditLog(value = "流程任务-生成流程图", operateType = OperateType.EXECUTE, moduleName = ModuleName.PROCESS)
    @RequestMapping("/diagram/{processId}")
    public void genProcessDiagram(HttpServletResponse response,
                                  @PathVariable("processId") String processId) {
        InputStream inputStream = flowTaskService.diagram(processId);
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
