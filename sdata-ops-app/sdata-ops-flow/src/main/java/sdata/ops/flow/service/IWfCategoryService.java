package sdata.ops.flow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.entity.WfCategory;
import sdata.ops.base.flow.model.vo.WfCategoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 流程分类Service接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IWfCategoryService {
    /**
     * 查询单个
     * @return
     */
    WfCategoryVo queryById(Long categoryId);

    /**
     * 查询列表
     */
    Page<WfCategoryVo> queryPageList(WfCategory category, PageQuery pageQuery);

    /**
     * 查询列表
     */
    List<WfCategoryVo> queryList(WfCategory category);

    /**
     * 新增流程分类
     *
     * @param category 流程分类信息
     * @return 结果
     */
    String insertCategory(WfCategory category);

    /**
     * 编辑流程分类
     *
     * @param category 流程分类信息
     * @return 结果
     */
    boolean updateCategory(WfCategory category);

    /**
     * 校验并删除数据
     *
     * @param ids     主键集合
     * @return 结果
     */
    boolean deleteWithValidByIds(Collection<String> ids);
}
