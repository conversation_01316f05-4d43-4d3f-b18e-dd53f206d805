package sdata.ops.flow.handler;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.flow.model.vo.FlowTestVO;
import sdata.ops.base.flow.model.vo.OpsAiWorkflowVO;
import sdata.ops.common.api.R;
import sdata.ops.common.config.feign.FeignLocal;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.flow.service.IWfProcessService;
import sdata.ops.flow.service.OpsWorkflowService;

import java.util.Map;

@Component
@FeignLocal(WorkFlowFeignService.class)
@RequiredArgsConstructor
public class WorkFlowLocalHandler implements WorkFlowFeignService {

    private final OpsWorkflowService workflowService;
    private final ApplicationContext applicationContext;

    @Override
    public OpsAiWorkflow getFlowById(String id) {
        return workflowService.getById(id);
    }

    @Override
    public R<String> startProcessByTask(String processDefinitionId, String userId, Map<String, Object> variables) {
        try {
            // 手动获取Bean，避免循环依赖
            IWfProcessService wfProcessService = applicationContext.getBean(IWfProcessService.class);
            ProcessInstance processInstance = wfProcessService.startProcessByTask(processDefinitionId, userId, variables);
            return R.data(processInstance.getId(), "流程启动成功");
        } catch (Exception e) {
            return R.fail("流程启动失败: " + e.getMessage());
        }
    }
}
