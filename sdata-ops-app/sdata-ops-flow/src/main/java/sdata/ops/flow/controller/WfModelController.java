package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.bo.WfModelBo;
import sdata.ops.base.flow.model.validate.AddGroup;
import sdata.ops.base.flow.model.validate.EditGroup;
import sdata.ops.base.flow.model.vo.WfModelVo;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.annotation.RepeatSubmit;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfCategoryService;
import sdata.ops.flow.service.IWfModelService;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 工作流流程模型管理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/model")
public class WfModelController {

    private final IWfModelService modelService;
    private final IWfCategoryService categoryService;

    /**
     * 查询流程模型列表
     *
     * @param modelBo   流程模型对象
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "模型-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:model:page")
    @GetMapping("/page")
    public R<Object> page(WfModelBo modelBo, PageQuery pageQuery) {
        Page<WfModelVo> page = modelService.page(modelBo, pageQuery);
        return R.data(page);
    }

    /**
     * 查询流程模型列表
     *
     * @param modelBo   流程模型对象
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "模型-分页查历史", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:model:page")
    @GetMapping("/historyPage")
    public R<Object> historyPage(WfModelBo modelBo, PageQuery pageQuery) {
        Page<WfModelVo> page = modelService.historyPage(modelBo, pageQuery);
        return R.data(page);
    }

    /**
     * 获取流程模型详细信息
     *
     * @param modelId 模型主键
     */
    @ControllerAuditLog(value = "模型-详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:model:query")
    @GetMapping(value = "/{modelId}")
    public R<WfModelVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("modelId") String modelId) {
        return R.data(modelService.getModel(modelId));
    }

    /**
     * 获取流程表单详细信息
     *
     * @param modelId 模型主键
     */
    @ControllerAuditLog(value = "模型-查设计图", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:model:query")
    @GetMapping(value = "/bpmnXml/{modelId}")
    public R<String> getBpmnXml(@NotNull(message = "主键不能为空") @PathVariable("modelId") String modelId) {
        return R.data(modelService.queryBpmnXmlById(modelId));
    }

    /**
     * 新增流程模型
     */
    @SaCheckPermission("workflow:model:add")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-新增", operateType = OperateType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WfModelBo modelBo) {
        modelService.insertModel(modelBo);
        return R.success("");
    }

    /**
     * 修改流程模型
     */
    @SaCheckPermission("workflow:model:edit")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-修改", operateType = OperateType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WfModelBo modelBo) {
        modelService.updateModel(modelBo);
        return R.success("");
    }

    /**
     * 保存流程模型
     */
    @SaCheckPermission("workflow:model:save")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-保存设计图", operateType = OperateType.INSERT)
    @PostMapping("/save")
    public R<String> save(@RequestBody WfModelBo modelBo) {
        modelService.saveModel(modelBo);
        return R.success("");
    }

    /**
     * 设为最新流程模型
     *
     * @param modelId
     * @return
     */
    @SaCheckPermission("workflow:model:save")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-设为最新", operateType = OperateType.UPDATE)
    @PostMapping("/latest")
    public R<?> latest(@RequestParam String modelId) {
        modelService.latestModel(modelId);
        return R.success("");
    }

    /**
     * 删除流程模型
     *
     * @param modelIds 流程模型主键串
     */
    @SaCheckPermission("workflow:model:remove")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-删除", operateType = OperateType.DELETE)
    @DeleteMapping("/{modelIds}")
    public R<String> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] modelIds) {
        modelService.deleteByIds(Arrays.asList(modelIds));
        return R.success("");
    }

    /**
     * 部署流程模型
     *
     * @param modelId 流程模型主键
     */
    @SaCheckPermission("workflow:model:deploy")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "模型-部署", operateType = OperateType.EXECUTE)
    @RepeatSubmit()
    @PostMapping("/deploy")
    public R<Object> deployModel(@RequestParam String modelId) {
        return R.data(modelService.deployModel(modelId));
    }
}
