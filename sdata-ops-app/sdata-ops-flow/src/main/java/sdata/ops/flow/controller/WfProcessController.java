package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.bo.WfCopyBo;
import sdata.ops.base.flow.model.vo.ProcessQuery;
import sdata.ops.base.flow.model.vo.WfCopyVo;
import sdata.ops.base.flow.model.vo.WfDefinitionVo;
import sdata.ops.base.flow.model.vo.WfTaskVo;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfCopyService;
import sdata.ops.flow.service.IWfProcessService;
import sdata.ops.flowable.utils.TaskUtils;

import java.util.Map;

/**
 * 工作流流程管理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/process")
public class WfProcessController {

    private final IWfProcessService processService;
    private final IWfCopyService copyService;

    /**
     * 查询可发起流程列表
     *
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "流程-可发起流程列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/startPage")
    @SaCheckPermission("workflow:process:startPage")
    public R<IPage<WfDefinitionVo>> startPage(ProcessQuery processQuery, PageQuery pageQuery) {
        return R.data(processService.selectPageStartProcessList(processQuery, pageQuery));
    }

    /**
     * 我拥有的流程
     */
    @ControllerAuditLog(value = "流程-我的流程", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:ownPage")
    @GetMapping(value = "/ownPage")
    public R<IPage<WfTaskVo>> ownPage(ProcessQuery processQuery, PageQuery pageQuery) {
        return R.data(processService.selectPageOwnProcessList(processQuery, pageQuery));
    }



    /**
     * 获取待办列表
     */
    @ControllerAuditLog(value = "流程-待办流程", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:todoPage")
    @GetMapping(value = "/todoPage")
    public R<Object> todoPage(ProcessQuery processQuery, PageQuery pageQuery) {
        IPage<WfTaskVo> info = processService.selectPageTodoProcessList(processQuery, pageQuery);
        return R.data(info);
    }

    /**
     * 获取待签列表
     *
     * @param processQuery 流程业务对象
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "流程-待签列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:claimPage")
    @GetMapping(value = "/claimPage")
    public R<Object> claimPage(ProcessQuery processQuery, PageQuery pageQuery) {
        IPage<WfTaskVo> info = processService.selectPageClaimProcessList(processQuery, pageQuery);
        return R.data(info);
    }

    /**
     * 获取已办列表
     *
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "流程-已办列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:finishedPage")
    @GetMapping(value = "/finishedPage")
    public R<Object> finishedProcessPage(ProcessQuery processQuery, PageQuery pageQuery) {
        IPage<WfTaskVo> info = processService.selectPageFinishedProcessList(processQuery, pageQuery);
        return R.data(info);
    }

    @ControllerAuditLog(value = "流程-已办列表（分组）", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:finishedPage")
    @GetMapping(value = "/finishedPageGroupForProcess")
    public R<Object> finishedProcessPageGroupForProcess(ProcessQuery processQuery, PageQuery pageQuery) {
        IPage<WfTaskVo> info = processService.selectPageFinishedProcessListGroupForProcessPro(processQuery, pageQuery);
        return R.data(info);
    }

    /**
     * 获取抄送列表
     *
     * @param copyBo 流程抄送对象
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "流程-抄送列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:copyPage")
    @GetMapping(value = "/copyPage")
    public R<Object>  copyProcessPage(WfCopyBo copyBo, PageQuery pageQuery) {
        copyBo.setUserId(TaskUtils.getUserId());
        IPage<WfCopyVo> info = copyService.selectPageList(copyBo, pageQuery);
        return R.data(info);
    }

    /**
     * 查询流程部署关联表单信息
     *
     * @param definitionId 流程定义id
     * @param deployId 流程部署id
     */
    @ControllerAuditLog(value = "流程-查询关联表单", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/getProcessForm")
    @SaCheckPermission("workflow:process:form")
    public R<?> getForm(@RequestParam(value = "definitionId") String definitionId,
                        @RequestParam(value = "deployId") String deployId,
                        @RequestParam(value = "procInsId", required = false) String procInsId) {
        return R.data(processService.selectFormContent(definitionId, deployId, procInsId));
    }

    /**
     * 根据流程定义id启动流程实例
     *
     * @param processDefId 流程定义id
     * @param variables 变量集合,json对象
     */
    @ControllerAuditLog(value = "流程-启动流程（根据定义id）", operateType = OperateType.EXECUTE, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:process:start")
    @PostMapping("/start/{processDefId}")
    public R<Void> start(@PathVariable(value = "processDefId") String processDefId, @RequestBody Map<String, Object> variables) {
        processService.startProcessByDefId(processDefId, variables);
        return R.success("流程启动成功");

    }

    /**
     * 删除流程实例
     *
     * @param instanceIds 流程实例ID串
     */
    @ControllerAuditLog(value = "流程-删除流程", operateType = OperateType.DELETE, moduleName = ModuleName.PROCESS)
    @DeleteMapping("/instance/{instanceIds}")
    public R<Void> delete(@PathVariable String[] instanceIds) {
        processService.deleteProcessByIds(instanceIds);
        return R.success("");
    }

    /**
     * 读取xml文件
     * @param processDefId 流程定义ID
     */
    @ControllerAuditLog(value = "流程-获取设计图", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/bpmnXml/{processDefId}")
    public R<String> getBpmnXml(@PathVariable(value = "processDefId") String processDefId) {
        return R.data(processService.queryBpmnXmlById(processDefId));
    }

    /**
     * 查询流程详情信息
     *
     * @param procInsId 流程实例ID
     * @param taskId 任务ID
     */
    @ControllerAuditLog(value = "流程-查询流程详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/detail")
    public R detail(String procInsId, String taskId) {
        return R.data(processService.queryProcessDetail(procInsId, taskId));
    }

    @ControllerAuditLog(value = "流程-我的流程详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/myProcessByPerId")
    public R<IPage<WfTaskVo>> myProcessByPerId(ProcessQuery processQuery, PageQuery pageQuery) {
        return R.data(processService.selectPageOwnProcessListByPerId(processQuery,pageQuery));
    }



    /**
     * 获取待办列表-new 扩展我部门以及下级部门
     */
    @ControllerAuditLog(value = "流程-我的部门的待办列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/todoPageByPerId")
    public R<Object> todoPageByPerId(ProcessQuery processQuery, PageQuery pageQuery) {
        return R.data(processService.selectPageTodoProcessListByPerId(processQuery, pageQuery));
    }

    /**
     * 获取已办列表-new 扩展我部门以及下级部门
     *
     */
    @ControllerAuditLog(value = "流程-我的部门的已办列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/finishedPageByPerId")
    public R<Object> finishedProcessPageByPerId(ProcessQuery processQuery, PageQuery pageQuery) {
        return R.data(processService.selectPageFinishedProcessListByPerId(processQuery, pageQuery));
    }


    /**
     * 获取抄送列表-new 扩展我部门以及下级部门
     *
     * @param copyBo 流程抄送对象
     * @param pageQuery 分页参数
     */
    @ControllerAuditLog(value = "流程-我的部门的抄送列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping(value = "/copyPageByPerId")
    public R<IPage<WfCopyVo>> copyProcessPageByPerId(WfCopyBo copyBo, PageQuery pageQuery) {
        String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";
        copyBo.setUserId(userId);
        IPage<WfCopyVo> info = copyService.selectPageListByPerId(copyBo, pageQuery);
        return R.data(info);
    }

    /**
     * 通过任务启动流程实例
     *
     * @param processDefinitionId 流程定义ID
     * @param userId 用户ID
     * @param variables 流程变量
     * @return 流程实例ID
     */
    @ControllerAuditLog(value = "流程-通过任务启动流程实例", operateType = OperateType.EXECUTE, moduleName = ModuleName.PROCESS)
    @PostMapping("/startByTask")
    public R<String> startByTask(@RequestParam String processDefinitionId,
                                 @RequestParam String userId,
                                 @RequestBody Map<String, Object> variables) {
        try {
            ProcessInstance instance = processService.startProcessByTask(processDefinitionId, userId, variables);
            log.info("通过任务启动流程实例成功，流程定义ID: {}, 用户ID: {}, 流程实例ID: {}",
                processDefinitionId, userId, instance.getId());
            return R.data(instance.getId());
        } catch (Exception e) {
            log.error("通过任务启动流程实例失败，流程定义ID: {}, 用户ID: {}",
                processDefinitionId, userId, e);
            return R.fail("流程启动失败: " + e.getMessage());
        }
    }
}
