package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngines;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.entity.RetryTask;
import sdata.ops.base.indicator.model.dto.IdDTO;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.handler.WfTriggerListener;
import sdata.ops.flow.service.RetryTaskService;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;

import java.util.Map;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/retryTask")
public class RetryTaskController {
    private final RetryTaskService retryTaskService;

    /**
     * 查失败列表
     */
    @ControllerAuditLog(value = "失败自动任务-分页", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:fatask:page")
    @GetMapping("/page")
    public R<Page<RetryTask>> page(PageQuery pageQuery) {
        Page<RetryTask> page = retryTaskService.lambdaQuery()
                .page(new Page<>(pageQuery.getPage(), pageQuery.getPageSize()));
        return R.data(page);
    }

    /**
     * 重试失败任务
     * @see WfTriggerListener#notify(DelegateTask)
     */
    @ControllerAuditLog(value = "失败自动任务-重试", operateType = OperateType.EXECUTE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/retry")
    @SaCheckPermission("workflow:fatask:retry")
    public R<String> retry(@RequestBody IdDTO dto) {
        RetryTask task = retryTaskService.getById(dto.getId());
        if (task == null) {
            return R.fail("任务不存在");
        }
        // 引擎服务
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        // 工作流服务接口
        IndicatorInfoFeign indicatorInfoFeign = SpringBeanUtil.getBean(IndicatorInfoFeign.class);
        // 获取当前流程变量
        Map<String, Object> variables = runtimeService.getVariables(task.getExecutionId());
        // 调用工作流服务接口
        R<WorkflowTriggerResultVO> result = indicatorInfoFeign.executeWorkflowForTrigger(task.getTaskFlowId(), variables);

        if (Optional.ofNullable(result).map(R::getData).map(WorkflowTriggerResultVO::getTriggerResult).filter(Boolean.TRUE::equals).isPresent()) {
            // 成功了，就自动完成
            taskService.complete(task.getExecutionId());
            // 成功了，删除这条记录
            retryTaskService.removeById(dto.getId());
            return R.success("执行成功");
        } else {
            return R.fail("执行失败，请检查配置");
        }
    }
}
