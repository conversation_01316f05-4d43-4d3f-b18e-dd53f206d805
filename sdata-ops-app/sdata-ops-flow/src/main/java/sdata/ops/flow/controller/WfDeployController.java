package sdata.ops.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.vo.ProcessQuery;
import sdata.ops.base.flow.model.vo.WfDeployVo;
import sdata.ops.base.flow.model.vo.WfFormVo;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfDeployFormService;
import sdata.ops.flow.service.IWfDeployService;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * 流程部署
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/deploy")
public class WfDeployController {

    private final IWfDeployService deployService;
    private final IWfDeployFormService deployFormService;

    /**
     * 查询流程部署列表
     */
    @ControllerAuditLog(value = "流程部署-查部署列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:deploy:page")
    @GetMapping("/page")
    public R<Object> page(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfDeployVo> page = deployService.queryPageList(processQuery, pageQuery);
        return R.data(page);
    }

    /**
     * 查询流程部署版本列表
     */
    @ControllerAuditLog(value = "流程部署-查已部署列表", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:deploy:list")
    @GetMapping("/publishPage")
    public R<Object> publishPage(@RequestParam String processKey, PageQuery pageQuery) {
        Page<WfDeployVo> page = deployService.publishPage(processKey, pageQuery);
        return R.data(page);
    }

    /**
     * 激活或挂起流程
     *
     * @param state 状态（active:激活 suspended:挂起）
     * @param definitionId 流程定义ID
     */
    @ControllerAuditLog(value = "流程部署-激活/挂起", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:deploy:state")
    @PutMapping(value = "/changeState")
    public R<Void> changeState(@RequestParam String state, @RequestParam String definitionId) {
        deployService.updateState(definitionId, state);
        return R.success("");
    }

    /**
     * 读取xml文件
     * @param definitionId 流程定义ID
     */
    @ControllerAuditLog(value = "流程部署-查关联设计图", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @SaCheckPermission("workflow:deploy:query")
    @GetMapping("/bpmnXml/{definitionId}")
    public R<String> getBpmnXml(@PathVariable(value = "definitionId") String definitionId) {
        return R.data(deployService.queryBpmnXmlById(definitionId));
    }

    /**
     * 删除流程模型
     * @param deployIds 流程部署ids
     */
    @SaCheckPermission("workflow:deploy:remove")
    @ControllerAuditLog(moduleName = ModuleName.PROCESS, value = "流程部署-删除", operateType = OperateType.DELETE)
    @DeleteMapping("/{deployIds}")
    public R<String> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] deployIds) {
        deployService.deleteByIds(Arrays.asList(deployIds));
        return R.success("");
    }

    /**
     * 查询流程部署关联表单信息
     *
     * @param deployId 流程部署id
     */
    @ControllerAuditLog(value = "流程部署-查关联表单", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/form/{deployId}")
    public R<?> start(@PathVariable(value = "deployId") String deployId) {
        WfFormVo formVo = deployFormService.selectDeployFormByDeployId(deployId);
        if (Objects.isNull(formVo)) {
            return R.fail("请先配置流程表单");
        }
        return R.data(JsonUtils.parseObject(formVo.getContent(), Map.class));
    }
}
