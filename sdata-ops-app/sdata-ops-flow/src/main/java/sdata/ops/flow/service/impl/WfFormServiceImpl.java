package sdata.ops.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.bo.WfFormBo;
import sdata.ops.base.flow.model.entity.WfForm;
import sdata.ops.base.flow.model.vo.WfFormVo;
import sdata.ops.flow.mapper.WfFormMapper;
import sdata.ops.flow.service.IWfFormService;

import java.util.Collection;
import java.util.List;

/**
 * 流程表单Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@RequiredArgsConstructor
@Service
public class WfFormServiceImpl implements IWfFormService {

    private final WfFormMapper baseMapper;

    /**
     * 查询流程表单
     *
     * @param formId 流程表单ID
     * @return 流程表单
     */
    @Override
    public WfFormVo queryById(String formId) {
        return baseMapper.selectVoById(formId);
    }

    /**
     * 查询流程表单列表
     *
     * @param bo 流程表单
     * @return 流程表单
     */
    @Override
    public IPage<WfFormVo> queryPageList(WfFormBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WfForm> lqw = buildQueryWrapper(bo);
        return baseMapper.<Page<WfFormVo>>selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 查询流程表单列表
     *
     * @param bo 流程表单
     * @return 流程表单
     */
    @Override
    public List<WfFormVo> queryList(WfFormBo bo) {
        LambdaQueryWrapper<WfForm> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增流程表单
     *
     * @param bo 流程表单
     * @return 结果
     */
    @Override
    public WfForm insertForm(WfFormBo bo) {
        WfForm wfForm = new WfForm();
        wfForm.setFormName(bo.getFormName());
        wfForm.setContent(bo.getContent());
        wfForm.setRemark(bo.getRemark());
        if (baseMapper.insert(wfForm) > 0) {
            return wfForm;
        } else {
            return null;
        }
    }

    /**
     * 修改流程表单
     *
     * @param bo 流程表单
     * @return 结果
     */
    @Override
    public WfFormVo updateForm(WfFormBo bo) {
        int update = baseMapper.update(new WfForm(), new LambdaUpdateWrapper<WfForm>()
                .set(StringUtils.isNotBlank(bo.getFormName()), WfForm::getFormName, bo.getFormName())
                .set(StringUtils.isNotBlank(bo.getContent()), WfForm::getContent, bo.getContent())
                .set(StringUtils.isNotBlank(bo.getRemark()), WfForm::getRemark, bo.getRemark())
                .eq(WfForm::getFormId, bo.getFormId()));
        if (update > 0) {
            return baseMapper.selectVoById(bo.getFormId());
        } else {
            return null;
        }
    }

    /**
     * 批量删除流程表单
     *
     * @param ids 需要删除的流程表单ID
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    private LambdaQueryWrapper<WfForm> buildQueryWrapper(WfFormBo bo) {
        LambdaQueryWrapper<WfForm> lqw = Wrappers.lambdaQuery();
        lqw.select(WfForm::getFormId, WfForm::getFormName, WfForm::getRemark);
        lqw.like(StringUtils.isNotBlank(bo.getFormName()), WfForm::getFormName, bo.getFormName());
        lqw.orderByDesc(WfForm::getFormId);
        return lqw;
    }
}
