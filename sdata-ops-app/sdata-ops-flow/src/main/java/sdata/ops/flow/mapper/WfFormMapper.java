package sdata.ops.flow.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import sdata.ops.base.flow.model.entity.WfForm;
import sdata.ops.base.flow.model.vo.WfFormVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程表单Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface WfFormMapper extends sdata.ops.flow.mapper.BaseMapperPlus<WfFormMapper, WfForm, WfFormVo> {

    List<WfFormVo> selectFormVoList(@Param("deployId") String deployId);
}
