package sdata.ops.flow.handler;

import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.el.FixedValue;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngines;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import sdata.ops.base.flow.model.entity.RetryTask;
import sdata.ops.base.flow.model.entity.WfLog;
import sdata.ops.base.indicator.model.dto.IdDTO;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.flow.controller.RetryTaskController;
import sdata.ops.flow.mapper.RetryTaskMapper;
import sdata.ops.flow.service.IWfLogService;
import sdata.ops.flowable.common.constant.ProcessConstants;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2025-09-28
 */
@Slf4j
@Data
public class WfTriggerListener implements TaskListener {
    /**
     * 工作流id
     */
    @Setter
    private FixedValue taskFlowId;

    /**
     * @param delegateTask 任务
     * @see RetryTaskController#retry(IdDTO)
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        // 流程实例id
        String procInsId = delegateTask.getProcessInstanceId();
        // 默认引擎
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        // 工作流服务接口
        IndicatorInfoFeign indicatorInfoFeign = SpringBeanUtil.getBean(IndicatorInfoFeign.class);

        Collection<WfLog> logs = new ArrayList<>();
        logs.add(createLog(ProcessConstants.LOG_TYPE_START, procInsId, "开始执行工作流,任务名称: %s", delegateTask.getName()));
        // 获取当前流程变量
        Map<String, Object> variables = processEngine.getRuntimeService().getVariables(delegateTask.getExecutionId());
        // 调用工作流服务接口
        R<WorkflowTriggerResultVO> result = indicatorInfoFeign.executeWorkflowForTrigger(taskFlowId.getExpressionText(), variables);

        if (Optional.ofNullable(result).map(R::getData).map(WorkflowTriggerResultVO::getTriggerResult).filter(Boolean.TRUE::equals).isPresent()) {
            // 成功了，就自动完成
            processEngine.getTaskService().complete(delegateTask.getExecutionId());

            logs.add(createLog(ProcessConstants.LOG_TYPE_DONE, procInsId, "工作流执行完成，任务名称: %s", delegateTask.getName()));
        } else {
            // 失败了，记录到数据库，等待重试
            RetryTask entity = new RetryTask();
            entity.setTaskId(delegateTask.getId());
            entity.setTaskName(delegateTask.getName());
            entity.setProcessInstanceId(procInsId);
            entity.setTaskFlowId(taskFlowId.getExpressionText());
            entity.setExecutionId(delegateTask.getExecutionId());
            SpringBeanUtil.getBean(RetryTaskMapper.class).insert(entity);

            logs.add(createLog(ProcessConstants.LOG_TYPE_DONE, procInsId, "工作流执行失败，任务名称: %s", delegateTask.getName()));
        }

        // 保存日志
        SpringBeanUtil.getBean(IWfLogService.class).saveBatch(logs);
    }

    private WfLog createLog(String type, String procInsId, String template, Object... args) {
        WfLog log = new WfLog();
        log.setProcInsId(procInsId);
        log.setLogType(type);
        log.setLogContent(String.format(template, args));
        log.setCreateTime(new Date());
        return log;
    }
}
