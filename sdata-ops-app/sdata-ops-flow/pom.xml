<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sdata-ops-app</artifactId>
        <groupId>sdata.ops.platform</groupId>
        <version>24.3.1</version>
    </parent>


    <modelVersion>4.0.0</modelVersion>

    <artifactId>sdata-ops-flow</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- spring boot 验证参数 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- flowable -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>
        <!-- 谷歌的表达式引擎 -->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-common</artifactId>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-flow-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-system-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-model</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-api</artifactId>
            <version>24.3.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>