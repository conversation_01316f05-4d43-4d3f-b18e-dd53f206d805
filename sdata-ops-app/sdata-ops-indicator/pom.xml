<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>sdata.ops.platform</groupId>
        <artifactId>sdata-ops-app</artifactId>
        <version>24.3.1</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>sdata-ops-indicator</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-common</artifactId>
        </dependency>
        <!--graalvm-js-engine使用-->
        <dependency>
            <groupId>org.graalvm.sdk</groupId>
            <artifactId>graal-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.graalvm.js</groupId>
            <artifactId>js</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--缓存池对象框架-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <!--java类动态编译器-->
        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-memorycompiler</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <!--实体类依赖-->
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <!--实体类依赖-->
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-system-api</artifactId>
            <version>24.3.1</version>
        </dependency>

        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-model</artifactId>
            <version>24.3.1</version>
        </dependency>

        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-system-model</artifactId>
            <version>24.3.1</version>
        </dependency>
        <!--达梦数据库驱动-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.1.193</version>
        </dependency>
        <!--oracle依赖-->
        <!-- Oracle JDBC Driver -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.8.0.0</version>
        </dependency>
       <!-- <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>-->
        <dependency>
            <groupId>dev.tinyflow</groupId>
            <artifactId>tinyflow-java-core</artifactId>
            <version>1.1.6</version>
            <exclusions>
                <exclusion>
                    <groupId>co.elastic.clients</groupId>
                    <artifactId>elasticsearch-java</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>agents-flex-store-elasticsearch</artifactId>
                    <groupId>com.agentsflex</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-volcengine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-gitee</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-openai</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-qianfan</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-qwen</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-siliconflow</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-tencent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-volcengine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-chatglm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-deepseek</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-gitee</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-moonshot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-openai</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-image-stability</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-coze</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-qianfan</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-qwen</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-siliconflow</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-spark</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-tencent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-vllm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-llm-volcengine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-aliyun</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-chroma</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-opensearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-qcloud</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-qdrant</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-vectorex</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-vectorexdb</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-milvus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-pgvector</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-flow-api</artifactId>
            <version>24.3.1</version>
            <scope>compile</scope>
        </dependency>
        <!-- 版本由Spring Boot父POM统一管理 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.5.13</version> <!-- 安全版本 -->
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.5.13</version> <!-- 安全版本 -->
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>2.0</version> <!-- 安全版本 -->
        </dependency>


    </dependencies>
</project>