package sdata.ops.indicator.handler.flow.config;

import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.ChainParser;
import org.springframework.stereotype.Component;
import sdata.ops.indicator.handler.flow.node.*;

@Component
public class FlowPreparationConfig {


    public void initProvidersAndNodeParsers(Tinyflow tinyflow) {
        setExtraNodeParser(tinyflow);
        setSearchEngineProvider(tinyflow);
        setLlmProvider(tinyflow);
        setKnowledgeProvider(tinyflow);
    }

    public void setExtraNodeParser(Tinyflow tinyflow) {

        // SQL查询
        SqlNodeParser sqlNodeParser = new SqlNodeParser();
        // NoSql缓存
        NoSqlCacheNodeParser noSqlCacheNodeParser = new NoSqlCacheNodeParser();
        // doc 解析 -读取服务类还未有场景设置 todo
        // DocNodeParser docNodeParser = new DocNodeParser(new ReadDocService());
        DataBaseWriteNodeParser dataBaseWriteNodeParser = new DataBaseWriteNodeParser();
        DataBaseReadNodeParser dataBaseReadNodeParser = new DataBaseReadNodeParser();
        JsonGenNodeParser jsonGenNodeParser=new JsonGenNodeParser();
        ScriptProcessNodeParser scriptProcessNode=new ScriptProcessNodeParser();
        MailReceiveNodeParser mailReceiveNodeParser=new MailReceiveNodeParser();
        ApiSourceExecNodeParser apiSourceExecNodeParser=new ApiSourceExecNodeParser();
        ChainParser chainParser = tinyflow.getChainParser();
        chainParser.addNodeParser(dataBaseWriteNodeParser.getNodeName(), dataBaseWriteNodeParser);
        chainParser.addNodeParser(dataBaseReadNodeParser.getNodeName(), dataBaseReadNodeParser);
        chainParser.addNodeParser(sqlNodeParser.getNodeName(), sqlNodeParser);
        chainParser.addNodeParser(noSqlCacheNodeParser.getNodeName(), noSqlCacheNodeParser);
        chainParser.addNodeParser(jsonGenNodeParser.getNodeName(),jsonGenNodeParser);
        chainParser.addNodeParser(scriptProcessNode.getNodeName(),scriptProcessNode);
        chainParser.addNodeParser(mailReceiveNodeParser.getNodeName(),mailReceiveNodeParser);
        chainParser.addNodeParser(apiSourceExecNodeParser.getNodeName(),apiSourceExecNodeParser);
    }

    /**
     * 设置搜索引擎提供者
     *
     * @param tinyflow
     */
    public void setSearchEngineProvider(Tinyflow tinyflow) {
//        tinyflow.setSearchEngineProvider(new SearchEngineProvider() {
//            @Override
//            public SearchEngine getSearchEngine(Object id) {
//                BochaaiSearchEngine searchEngine = new BochaaiSearchEngine();
//                searchEngine.setApiKey(bochaaiProps.getApiKey());
//                return searchEngine;
//            }
//        });
    }

    /**
     * 设置LLM提供者
     *
     * @param tinyflow
     */
    public void setLlmProvider(Tinyflow tinyflow) {
//        tinyflow.setLlmProvider(new LlmProvider() {
//            @Override
//            public Llm getLlm(Object id) {
//                AiLlm aiLlm = aiLlmService.getById(new BigInteger(id.toString()));
//                return aiLlm.toLlm();
//            }
//        });
    }

    /**
     * 设置知识模型提供者
     *
     * @param tinyflow
     */
    public void setKnowledgeProvider(Tinyflow tinyflow) {
//        tinyflow.setKnowledgeProvider(new KnowledgeProvider() {
//            @Override
//            public Knowledge getKnowledge(Object id) {
//                AiKnowledge aiKnowledge = aiKnowledgeService.getById(new BigInteger(id.toString()));
//                return new Knowledge() {
//                    @Override
//                    public List<Document> search(String keyword, int limit, KnowledgeNode knowledgeNode, Chain chain) {
//                        DocumentStore documentStore = aiKnowledge.toDocumentStore();
//                        if (documentStore == null) {
//                            return null;
//                        }
//                        AiLlm aiLlm = aiLlmService.getById(aiKnowledge.getVectorEmbedLlmId());
//                        if (aiLlm == null) {
//                            return null;
//                        }
//                        documentStore.setEmbeddingModel(aiLlm.toLlm());
//                        SearchWrapper wrapper = new SearchWrapper();
//                        wrapper.setMaxResults(Integer.valueOf(limit));
//                        wrapper.setText(keyword);
//                        StoreOptions options = StoreOptions.ofCollectionName(aiKnowledge.getVectorStoreCollection());
//
//                        List<Document> results = documentStore.search(wrapper, options);
//                        return results;
//                    }
//                };
//            }
//        });
    }
}
