package sdata.ops.indicator.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum WarnSyncTypeEnum {
    FIXED_RATE(1, "固定频率(分钟)"),
    CRON(2, "cron规则"),
    ;
    private final int code;
    private final String desc;

    WarnSyncTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean is(Integer code) {
        return Objects.equals(this.code, code);
    }
}
