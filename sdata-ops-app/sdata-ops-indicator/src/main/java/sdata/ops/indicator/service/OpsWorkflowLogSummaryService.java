package sdata.ops.indicator.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsWorkflowLogSummary;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_workflow_log_summary(工作流运行日志表)】的数据库操作Service
* @createDate 2025-07-01 16:13:09
*/
public interface OpsWorkflowLogSummaryService extends IService<OpsWorkflowLogSummary> {


    void saveWorkflowLogSummaryByWorkflowExecute(JSONObject itemOne, List<JSONObject> eventList);
}
