package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class DataBaseWriteNodeParser extends BaseNodeParser {

    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        DataBaseWriteNode dataBaseWriteNode = new DataBaseWriteNode(new cn.hutool.json.JSONObject(data));
        // 添加输入参数
        addParameters(dataBaseWriteNode, data);
        // 添加输出参数 "db-wirte-node"
        addOutputDefs(dataBaseWriteNode, data);
        return dataBaseWriteNode;
    }

    public String getNodeName() {
        return "db-write-node";
    }
}
