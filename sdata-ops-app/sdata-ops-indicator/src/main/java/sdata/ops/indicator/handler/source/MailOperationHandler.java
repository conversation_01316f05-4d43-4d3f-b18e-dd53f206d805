package sdata.ops.indicator.handler.source;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.indicator.config.mail.MailManager;
import sdata.ops.indicator.config.mail.MailModel;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

@Component("mailOperation")
public class MailOperationHandler implements ScriptExecutor {
    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        MailModel model = JSONUtil.toBean(vo.getScript(), MailModel.class);
        return MailManager.executeMailReceiver(model);
    }
}
