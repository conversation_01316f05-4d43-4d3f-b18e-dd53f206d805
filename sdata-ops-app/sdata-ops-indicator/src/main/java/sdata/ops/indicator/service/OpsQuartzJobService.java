package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.quartz.SchedulerException;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.indicator.handler.quartz.exception.TaskException;

/**
 * <AUTHOR>
 * @description 针对表【ops_quartz_job(定时任务调度表)】的数据库操作Service
 * @createDate 2025-07-28 19:20:32
 */
public interface OpsQuartzJobService extends IService<OpsQuartzJob> {

    String addJob(OpsQuartzJob job) throws SchedulerException, TaskException;


    void editJob(OpsQuartzJob job) throws SchedulerException, TaskException;

    void resumeJob(String id) throws SchedulerException;


    void pause(String id) throws SchedulerException;


    void deletedJob(String id) throws SchedulerException;

    void run(String id) throws SchedulerException;

//    void pauseByWarnId(String id) throws SchedulerException;
//
//    void resumeJobByWarnId(String id) throws SchedulerException;
//
//    void addJobForWarnCenter(String cronExpression, String warnId) throws SchedulerException, TaskException;

}
