package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.indicator.model.entity.OpsSignData;

/**
* <AUTHOR>
* @description 针对表【OPS_SIGN_DATA(任务是否需要导入配置标记表)】的数据库操作Mapper
* @createDate 2024-11-01 15:24:20
* @Entity generator.domain.OpsSignData
*/
@Mapper
public interface OpsSignDataMapper extends BaseMapper<OpsSignData> {

}




