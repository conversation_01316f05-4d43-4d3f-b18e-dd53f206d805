package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsSignData;
import sdata.ops.indicator.mapper.OpsSignDataMapper;
import sdata.ops.indicator.service.OpsSignDataService;

/**
* <AUTHOR>
* @description 针对表【OPS_SIGN_DATA(任务是否需要导入配置标记表)】的数据库操作Service实现
* @createDate 2024-11-01 15:24:20
*/
@Service
public class OpsSignDataServiceImpl extends ServiceImpl<OpsSignDataMapper, OpsSignData>
    implements OpsSignDataService {

}




