package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.PropertyPlaceholderHelper;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DataBaseReadNode extends BaseNode {

    private final JSONObject data;

    public DataBaseReadNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        //输出容器
        Map<String, Object> res = new HashMap<>();
        // 获取输入参数
        Map<String, Object> map = chain.getParameterValues(this);

        JSONObject nodeConfig = data.getJSONObject("config");
        //获取数据源id
        String sourceId = nodeConfig.getString("sourceId");
        //获取数据源操作对象
        DataSourceOperationHandler operationHandler = SpringBeanUtil.getBean(DataSourceOperationHandler.class);
        //获取操作对象
        String selectSql = nodeConfig.getString("sql");
        //校验sql语句规范
        verify(selectSql);
        //替换sql中动态参数位置为？并生成参数集合
        cn.hutool.json.JSONObject setParams = new cn.hutool.json.JSONObject();
        selectSql = replaceWithSpring(map, selectSql, setParams);
        //执行入参
        cn.hutool.json.JSONObject push = new cn.hutool.json.JSONObject();
        push.set("type", "read");
        push.set("sql", selectSql);
        push.set("resultType", nodeConfig.getString("resultType"));
        push.set("params", setParams);
        Object callResult = operationHandler.invokeExecute(sourceId, push);
        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            for (Parameter def : outputDefs) {
                res.put(def.getName(), callResult);
            }
        }
        return res;
    }

    private static String replaceWithSpring(Map<String, Object> paramMap, String sql, cn.hutool.json.JSONObject setParams) {
        PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}");
        return helper.replacePlaceholders(sql, key -> {
            if (paramMap.get(key) == null) {
                throw new RuntimeException("缺少动态参数::" + key);
            }
            setParams.set(key, paramMap.get(key));
            return ":"+key;
        });
    }

    /**
     * 校验SQL是否为合法的查询语句
     *
     * @param selectSql 待校验的SQL语句
     * @throws IllegalArgumentException 如果SQL不是合法的查询语句
     */
    private void verify(String selectSql) {
        if (StringUtils.isEmpty(selectSql)) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }

        // 去除前后空格并转换为小写方便处理
        String trimmedSql = selectSql.trim().toLowerCase();

        // 1. 校验必须以SELECT开头(允许前面有注释)
        if (!trimmedSql.matches("^(--.*?\\n|/\\*.*?\\*/|\\s)*select\\s+.*")) {
            throw new IllegalArgumentException("只允许执行SELECT查询语句");
        }

        // 2. 校验不包含危险操作关键词
        String[] forbiddenKeywords = {"insert", "update", "delete", "drop", "alter", "truncate", "create", "grant", "revoke"};
        for (String keyword : forbiddenKeywords) {
            if (trimmedSql.contains(keyword)) {
                throw new IllegalArgumentException("SQL语句包含非法操作: " + keyword);
            }
        }

        // 3. 校验不以分号结尾(防止SQL注入)
        if (trimmedSql.endsWith(";")) {
            throw new IllegalArgumentException("SQL语句不应包含结束分号");
        }

        // 4. 校验SQL语法有效性(简单校验)
        if (!trimmedSql.matches("^select\\s+.*\\s+from\\s+\\w+(\\s+where\\s+.*)?(\\s+group\\s+by\\s+.*)?(\\s+having\\s+.*)?(\\s+order\\s+by\\s+.*)?$")) {
            throw new IllegalArgumentException("SQL语法不符合基本SELECT查询格式");
        }

        // 5. 可选：校验表名白名单
        // verifyTableNames(selectSql);
    }

}
