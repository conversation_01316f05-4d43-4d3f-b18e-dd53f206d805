package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.indicator.mapper.OpsDataSourceMapper;
import sdata.ops.indicator.service.OpsDataSourceService;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpsDataSourceServiceImpl extends ServiceImpl<OpsDataSourceMapper, OpsDataSource> implements OpsDataSourceService {


}
