package sdata.ops.indicator.service;



import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.IndicatorRelease;
import sdata.ops.common.api.R;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface IndicatorReleaseService extends IService<IndicatorRelease> {

    /**
     * 历史版本还原
     *
     * @param releaseId
     * @return
     */
    R<Object> reduction(String releaseId);
}
