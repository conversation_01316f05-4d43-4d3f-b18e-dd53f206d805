package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.indicator.model.entity.OpsWorkflowNodeLogErrorDetail;

/**
* <AUTHOR>
* @description 针对表【ops_workflow_node_log_error_detail(工作流异常详情表)】的数据库操作Mapper
* @createDate 2025-07-01 17:50:35
* @Entity generator.domain.OpsWorkflowNodeLogErrorDetail
*/
@Mapper
public interface OpsWorkflowNodeLogErrorDetailMapper extends BaseMapper<OpsWorkflowNodeLogErrorDetail> {

}




