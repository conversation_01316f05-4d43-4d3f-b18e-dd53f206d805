package sdata.ops.indicator.config.js;

import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.AbandonedConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.graalvm.polyglot.Context;

/***
 * <AUTHOR>
 * @version 1.0
 *  This class implements the PooledObject interface for  repeat use jsEngine
 */
public class ContextGenericObjectPool extends GenericObjectPool<Context> {
    public ContextGenericObjectPool(PooledObjectFactory<Context> factory) {
        super(factory);
    }

    public ContextGenericObjectPool(PooledObjectFactory<Context> factory, GenericObjectPoolConfig<Context> config) {
        super(factory, config);
    }

    public ContextGenericObjectPool(PooledObjectFactory<Context> factory, GenericObjectPoolConfig<Context> config, AbandonedConfig abandonedConfig) {
        super(factory, config, abandonedConfig);
    }
}
