package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsWarnDataHistory;
import sdata.ops.indicator.mapper.OpsWarnDataHistoryMapper;
import sdata.ops.indicator.service.OpsWarnDataHistoryService;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_data_history(历史预警数据表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@RequiredArgsConstructor
public class OpsWarnDataHistoryServiceImpl extends ServiceImpl<OpsWarnDataHistoryMapper, OpsWarnDataHistory>
        implements OpsWarnDataHistoryService {

}