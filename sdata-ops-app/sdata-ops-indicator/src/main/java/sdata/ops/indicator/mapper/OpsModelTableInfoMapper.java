package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;

/**
* <AUTHOR>
* @description 针对表【ops_model_table_info(数据表基础信息表)】的数据库操作Mapper
* @createDate 2025-08-01 14:15:54
* @Entity generator.domain.OpsModelTableInfo
*/
public interface OpsModelTableInfoMapper extends BaseMapper<OpsModelTableInfo> {

    IPage<OpsModelTableInfo> pageCustom(Page<OpsModelTableInfo> page,@Param("ew")LambdaQueryWrapper<OpsModelTableInfo> wrapper);
}




