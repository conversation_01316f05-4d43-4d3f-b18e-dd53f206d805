package sdata.ops.indicator.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ql.util.express.DefaultContext;
import groovy.lang.Tuple3;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.OpsMetricFieldVO;
import sdata.ops.base.indicator.model.entity.OpsMetricApiConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricTag;
import sdata.ops.base.indicator.model.vo.OpsMetricApiConfigVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicApiVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicFieldVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicVO;
import sdata.ops.common.config.rule.RuleUtil;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.indicator.handler.flow.WorkFlowExecHandler;
import sdata.ops.indicator.mapper.OpsMetricBasicMapper;
import sdata.ops.indicator.mapper.OpsMetricTagMapper;
import sdata.ops.indicator.service.OpsMetricApiConfigService;
import sdata.ops.indicator.service.OpsMetricBasicService;
import sdata.ops.indicator.service.OpsMetricResultConfigService;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_metric_basic(指标基本信息表)】的数据库操作Service实现
 * @createDate 2025-08-15 15:54:13
 */
@Service
@RequiredArgsConstructor
public class OpsMetricBasicServiceImpl extends ServiceImpl<OpsMetricBasicMapper, OpsMetricBasic>
        implements OpsMetricBasicService {

    private final OpsMetricTagMapper opsMetricTagMapper;

    private final OpsMetricApiConfigService apiConfigService;

    private final WorkFlowExecHandler execHandler;

    private final OpsMetricResultConfigService resultConfigService;

    private final WorkFlowExecHandler workFlowExecHandler;

    private final SystemFeignService systemFeignService;

    private  final JSONConfig config = JSONConfig.create().setIgnoreNullValue(false);


    @Override
    public void deleted(String id) {
        //todo 查询是否被监控引用
        removeById(Long.valueOf(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateOp(OpsMetricBasicVO metricBasic) {
        OpsMetricBasic opsMetricBasic = new OpsMetricBasic();
        BeanUtil.copyProperties(metricBasic, opsMetricBasic);
        opsMetricBasic.setTestParams(JSONUtil.toJsonStr(metricBasic.getParams()));
        long metricId = IdWorker.getId();
        if (metricBasic.getId() == null) {
            opsMetricBasic.setId(metricId);
        }
        // 更新标签
        updateMetricTags(metricBasic, opsMetricBasic);

        // 判断是否需要更新工作流
        boolean isUpdateWorkFlow = checkIfWorkflowNeedsUpdate(metricBasic);

        // 更新结果集配置
        updateResultConfigIfNecessary(metricBasic, opsMetricBasic, isUpdateWorkFlow);

        // 保存或更新指标基本信息
        saveOrUpdate(opsMetricBasic);
    }

    /**
     * 更新指标标签
     */
    private void updateMetricTags(OpsMetricBasicVO metricBasic, OpsMetricBasic opsMetricBasic) {
        if (!metricBasic.getTags().isEmpty()) {
            opsMetricTagMapper.delete(Wrappers.<OpsMetricTag>lambdaQuery()
                    .eq(OpsMetricTag::getMetricId, opsMetricBasic.getId()));
            metricBasic.getTags().forEach(tag -> {
                OpsMetricTag opsMetricTag = new OpsMetricTag();
                opsMetricTag.setMetricId(String.valueOf(opsMetricBasic.getId()));
                opsMetricTag.setTagName(tag);
                opsMetricTag.setTagType("sys");
                opsMetricTagMapper.insert(opsMetricTag);
            });
        }
    }

    /**
     * 检查是否需要更新工作流
     */
    private boolean checkIfWorkflowNeedsUpdate(OpsMetricBasicVO metricBasic) {
        boolean isUpdateWorkFlow = false;
        if (metricBasic.getId() != null) {
            OpsMetricBasic oldMetric = getById(metricBasic.getId());
            if (!oldMetric.getTaskFlowId().equals(metricBasic.getTaskFlowId())) {
                isUpdateWorkFlow = true;
            }
        } else if (metricBasic.getTaskFlowId() != null) {
            isUpdateWorkFlow = true;
        }
        return isUpdateWorkFlow;
    }

    /**
     * 根据条件更新结果集配置
     */
    private void updateResultConfigIfNecessary(OpsMetricBasicVO metricBasic, OpsMetricBasic opsMetricBasic, boolean isUpdateWorkFlow) {
        if (!metricBasic.getParams().isEmpty() && metricBasic.getJsonpathConfig() != null && isUpdateWorkFlow) {
            try {
                Map<String, Object> flowResult = workFlowExecHandler.invokeWorkflowResult(metricBasic.getTaskFlowId(), metricBasic.getParams());
                Object cs = JSONUtil.getByPath(JSONUtil.parse(flowResult,config), metricBasic.getJsonpathConfig());
                if (cs instanceof List) {
                    List<OpsMetricResultConfig> saveCap = new ArrayList<>();
                    JSONObject json = JSONUtil.parseArray(cs).getJSONObject(0);
                    json.forEach((k, v) -> {
                        OpsMetricResultConfig field = new OpsMetricResultConfig();
                        field.setMetricId(opsMetricBasic.getId());
                        field.setResultField(k);
                        field.setFieldType(v.getClass().getSimpleName());
                        saveCap.add(field);
                    });
                    resultConfigService.saveOrUpdateBatch(saveCap);
                }
            } catch (RuntimeException e) {
                throw new RuntimeException("流程静默执行生成结果集字段配置错误!", e);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateApiConf(OpsMetricBasicApiVO metricBasic) {
        if (StringUtils.isEmpty(metricBasic.getMetricId())) {
            throw new RuntimeException("指标id不能为空!");
        }
        OpsMetricApiConfigVO configVO = metricBasic.getApiConfig();
        OpsMetricApiConfig config = new OpsMetricApiConfig();
        BeanUtil.copyProperties(configVO, config);
        config.setParams(JSONUtil.toJsonStr(configVO.getParams()));
        config.setMetricId(Long.valueOf(metricBasic.getMetricId()));
        apiConfigService.saveOrUpdate(config);
    }

    @Override
    public Object executeMetricForApi(String httpMethod, String apiPath, Map<String, String> queryParams, Map<String, Object> bodyParams) {

        OpsMetricApiConfig apiConfig = apiConfigService.getOne(Wrappers.<OpsMetricApiConfig>lambdaQuery().
                eq(OpsMetricApiConfig::getHttpMethod, httpMethod)
                .eq(OpsMetricApiConfig::getApiPath, apiPath));
        if (apiConfig == null) {
            throw new RuntimeException("接口不存在!");
        }
        if (apiConfig.getIsEnabled() == 0) {
            throw new RuntimeException("接口已禁用!");
        }

        return metricExecutorForWeb(httpMethod, apiConfig.getMetricId(), bodyParams, queryParams);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBasicResultConfig(OpsMetricFieldVO fieldVO) {
        for (OpsMetricResultConfig field : fieldVO.getFields()) {
            field.setMetricId(Long.valueOf(fieldVO.getMetricId()));
        }
        resultConfigService.remove(Wrappers.<OpsMetricResultConfig>lambdaQuery().eq(OpsMetricResultConfig::getMetricId, fieldVO.getMetricId()));
        resultConfigService.saveBatch(fieldVO.getFields());
    }

    @Override
    public OpsMetricApiConfig apiConfByMetricId(String metricId) {
        return apiConfigService.getOne(Wrappers.<OpsMetricApiConfig>lambdaQuery().eq(OpsMetricApiConfig::getMetricId, metricId));
    }

    @Override
    public List<OpsMetricResultConfig> fieldDetailByMeticId(String metricId) {
        return resultConfigService.list(Wrappers.<OpsMetricResultConfig>lambdaQuery().eq(OpsMetricResultConfig::getMetricId, metricId));
    }

    @Override
    public Map<String, List<OpsMetricTag>> findTagsByMetricId(List<String> metricId) {
        List<OpsMetricTag> res = opsMetricTagMapper.selectList(Wrappers.lambdaQuery(OpsMetricTag.class).in(OpsMetricTag::getMetricId, metricId));
        return res.stream().collect(Collectors.groupingBy(OpsMetricTag::getMetricId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAsById(String id) {
        //查询当前指标内容
        OpsMetricBasic metricBasic = getById(id);
        //创建一个新指标
        OpsMetricBasic newMetricBasic = new OpsMetricBasic();
        BeanUtil.copyProperties(metricBasic, newMetricBasic);
        newMetricBasic.setCreateTime(null);
        newMetricBasic.setUpdateTime(null);
        newMetricBasic.setId(IdWorker.getId());
        save(newMetricBasic);
        //查询指标标签 //创建一个新标签
        List<OpsMetricTag> tags = opsMetricTagMapper.selectList(Wrappers.<OpsMetricTag>lambdaQuery().eq(OpsMetricTag::getMetricId, id));
        if (!tags.isEmpty()) {
            tags.forEach(tag -> {
                OpsMetricTag newTag = new OpsMetricTag();
                BeanUtil.copyProperties(tag, newTag);
                newTag.setId(null);
                newTag.setMetricId(String.valueOf(newMetricBasic.getId()));
                opsMetricTagMapper.insert(newTag);
            });
        }

        //查询相关结果集配置
        List<OpsMetricResultConfig> resultConfigs = resultConfigService.list(Wrappers.<OpsMetricResultConfig>lambdaQuery().eq(OpsMetricResultConfig::getMetricId, id));
        if (!resultConfigs.isEmpty()) {
            resultConfigs.forEach(config -> {
                OpsMetricResultConfig newConfig = new OpsMetricResultConfig();
                BeanUtil.copyProperties(config, newConfig);
                newConfig.setId(null);
                newConfig.setMetricId(newMetricBasic.getId());
                resultConfigService.save(newConfig);
            });
        }
        //查询相关api配置
        //创建一个新api配置
        OpsMetricApiConfig apiConfig = apiConfigService.getOne(Wrappers.<OpsMetricApiConfig>lambdaQuery().eq(OpsMetricApiConfig::getMetricId, id));
        if (apiConfig != null) {
            OpsMetricApiConfig newApiConfig = new OpsMetricApiConfig();
            BeanUtil.copyProperties(apiConfig, newApiConfig);
            newApiConfig.setId(null);
            newApiConfig.setMetricId(newMetricBasic.getId());
            apiConfigService.save(newApiConfig);
        }
    }

    @Override
    public Object executeMetricByMonitorCenter(String metricId, Map<String, Object> input) {
        OpsMetricBasic metricBasic = getById(metricId);
        if (metricBasic == null || metricBasic.getTaskFlowId() == null) {
            throw new RuntimeException("指标绑定异常");
        }
        Map<String, Object> variables = new HashMap<>(input);
        variables.putAll(JsonUtils.toMap(metricBasic.getTestParams()));
        //todo 动态参数替换
        Map<String, Object> res = execHandler.invokeWorkflowResult(metricBasic.getTaskFlowId(), variables);
        //jsonPath 处理
        Object finalRes = null;
        if (metricBasic.getJsonpathConfig() != null && !metricBasic.getJsonpathConfig().isEmpty()) {
            finalRes = JSONUtil.getByPath(JSONUtil.parse(res), metricBasic.getJsonpathConfig());
        }
        return finalRes;
    }

    @Override
    public long countPerm(String permId) {
        return resultConfigService.count(new LambdaQueryWrapper<>(OpsMetricResultConfig.class).eq(OpsMetricResultConfig::getPermissionId, permId));
    }

    @Override
    public List<OpsMetricBasicFieldVO> queryAllFields() {
        List<OpsMetricBasic> allBasic = list(new LambdaQueryWrapper<>(OpsMetricBasic.class).select(OpsMetricBasic::getId, OpsMetricBasic::getMetricName));
        Map<Long, List<OpsMetricResultConfig>> allResultConfigs = resultConfigService.list().stream().collect(Collectors.groupingBy(OpsMetricResultConfig::getMetricId));
        List<OpsMetricBasicFieldVO> capture = new ArrayList<>();
        for (OpsMetricBasic basic : allBasic) {
            OpsMetricBasicFieldVO metricBasicFieldVO = new OpsMetricBasicFieldVO();
            metricBasicFieldVO.setId(String.valueOf(basic.getId()));
            metricBasicFieldVO.setMetricName(basic.getMetricName());
            metricBasicFieldVO.setFields(allResultConfigs.get(basic.getId()));
            capture.add(metricBasicFieldVO);
        }
        return capture;
    }

    private Tuple3<Boolean, String, List<OpsMetricResultConfig>> isPermissionEnabled(String metricId) {
        LambdaQueryWrapper<OpsMetricResultConfig> queryWrapper = Wrappers.<OpsMetricResultConfig>lambdaQuery()
                .eq(OpsMetricResultConfig::getMetricId, metricId).
                isNotNull(OpsMetricResultConfig::getPermissionId).last(" and permission_id <> ''");
        List<OpsMetricResultConfig> resultConfigs = resultConfigService.list(queryWrapper);
        if (resultConfigs.isEmpty()) {
            return new Tuple3<>(false, "", new ArrayList<>());
        }
        return new Tuple3<>(true, resultConfigs.get(0).getPermissionId(), resultConfigs);
    }

    private Object metricExecutorForWeb(String httpMethod, Long metricId, Map<String, Object> bodyParams, Map<String, String> queryParams) {
        OpsMetricBasic metricBasic = getById(metricId);
        if (metricBasic == null || metricBasic.getTaskFlowId() == null) {
            throw new RuntimeException("指标绑定异常");
        }
        Map<String, Object> variables = new HashMap<>();
        if (httpMethod.equalsIgnoreCase("get")) {
            variables = queryParams.keySet().stream().collect(Collectors.toMap(key -> key, queryParams::get));
        } else if (httpMethod.equalsIgnoreCase("post")) {
            variables = bodyParams;
        }
        //todo 动态参数替换
        Map<String, Object> res = execHandler.invokeWorkflowResult(metricBasic.getTaskFlowId(), variables);
        //jsonPath 处理
        Object finalRes = null;
        if (metricBasic.getJsonpathConfig() != null && !metricBasic.getJsonpathConfig().isEmpty()) {
            finalRes = JSONUtil.getByPath(JSONUtil.parse(res), metricBasic.getJsonpathConfig());
        }
        //数据权限过滤-调用数据权限配置逻辑-对数据进行内存过滤
        Tuple3<Boolean, String, List<OpsMetricResultConfig>> permissionEnabled = isPermissionEnabled(String.valueOf(metricId));
        if (permissionEnabled.getV1()) {
            if (finalRes instanceof List) {
                JSONArray jsonArray = new JSONArray(finalRes);
                finalRes = permissionFilter(jsonArray, permissionEnabled.getV2(), permissionEnabled.getV3());
            }
        }
        return finalRes;
    }


    private Object permissionFilter(JSONArray jsonArray, String permissionId, List<OpsMetricResultConfig> fields) {
        JSONObject dataPerm = systemFeignService.getDataPermById(permissionId, SecureUtil.currentUserId());
        if (dataPerm == null) {
            return jsonArray;
        }

        return dataPerm.getInt("permType") == 1
                ? filterByScope(jsonArray, dataPerm, fields)
                : filterByExpression(jsonArray, dataPerm);
    }

    private JSONArray filterByScope(JSONArray jsonArray, JSONObject dataPerm, List<OpsMetricResultConfig> fields) {
        JSONObject conf = dataPerm.getJSONObject("permConf");
        String scopType = conf.getStr("scopeType");
        List<String> scopeVals = conf.getJSONArray("vars").toList(String.class);

        // 使用HashSet提高包含判断效率
        Set<String> scopeValSet = new HashSet<>(scopeVals);
        String resultField = fields.get(0).getResultField();

        if ("all".equals(scopType)) {
            return jsonArray;
        }
        jsonArray.removeIf(item -> {
            JSONObject jsonObject = parseToJsonObject(item);
            String value = jsonObject.getStr(resultField);

            return scopType.equals("in")
                    ? !scopeValSet.contains(value)
                    : scopType.equals("notIn") && scopeValSet.contains(value);
        });

        return jsonArray;
    }

    private JSONArray filterByExpression(JSONArray jsonArray, JSONObject dataPerm) {
        JSONObject conf = dataPerm.getJSONObject("permConf");
        String expression = conf.getStr("expressVar");
        List<String> dimKey = conf.getJSONArray("dimKey").toList(String.class);
        List<String> dimValues = conf.getJSONArray("dimVal").toList(String.class);

        // 预编译表达式(假设RuleUtil支持)
        String currentUserId = SecureUtil.safeUserId();

        jsonArray.removeIf(item -> {
            JSONObject jsonObject = parseToJsonObject(item);
            DefaultContext<String, Object> context = new DefaultContext<>();

            // 预填充固定值
            String bizDate = DateUtil.format(DateUtil.date(), "yyyy-MM-dd");

            for (int i = 0; i < dimKey.size(); i++) {
                String val = dimValues.get(i);
                if ("${userId}".equals(val)) {
                    context.put(dimKey.get(i), currentUserId);
                } else if ("${bizDate}".equals(val)) {
                    context.put(dimKey.get(i), bizDate);
                } else {
                    context.put(dimKey.get(i), jsonObject.get(val));
                }
            }

            return !RuleUtil.execute(expression, context);
        });

        return jsonArray;
    }

    private JSONObject parseToJsonObject(Object item) {
        return item instanceof JSONObject
                ? (JSONObject) item
                : JSONUtil.parseObj(item.toString());
    }

}




