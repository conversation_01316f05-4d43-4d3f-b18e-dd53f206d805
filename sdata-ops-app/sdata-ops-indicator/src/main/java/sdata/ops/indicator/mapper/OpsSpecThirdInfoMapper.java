package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【OPS_SPEC_THIRD_INFO(读取oa或者读取邮件的存储表，提供明细任务使用)】的数据库操作Mapper
* @createDate 2024-10-16 10:35:29
* @Entity generator.domain.OpsSpecThirdInfo
*/
public interface OpsSpecThirdInfoMapper extends BaseMapper<OpsSpecThirdInfo> {

    List<OpsSpecThirdInfo> scriptObtUnlessInfo(@Param("userId") String userId, @Param("indicatorId") String indicatorId, @Param("bizDate") String bizDate);
}




