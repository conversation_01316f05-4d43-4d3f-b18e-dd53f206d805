package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.quartz.SchedulerException;
import sdata.ops.base.indicator.model.dto.EnableDTO;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.indicator.handler.quartz.exception.TaskException;

/**
* <AUTHOR>
* @description 针对表【ops_warn_info(预警信息表)】的数据库操作Service
* @createDate 2025-08-16
*/
public interface OpsWarnInfoService extends IService<OpsWarnInfo> {
    /**
     * 删除监控单元
     *
     * @param id 监控单元id
     * @return 是否删除成功
     */
    void deleteWarnInfo(String id) throws SchedulerException;

    /**
     * 保存监控单元
     */
    void saveWarnInfo(WarnInfoVO opsWarnInfo) throws SchedulerException, TaskException;

    /**
     * 根据id查询
     *
     * @param id 监控单元id
     * @return 完整的监控单元信息
     */
    WarnInfoVO getWarnInfo(String id);

    /**
     * 更新监控单元
     *
     * @param opsWarnInfo 监控单元信息
     */
    void updateWarnInfo(WarnInfoVO opsWarnInfo) throws SchedulerException, TaskException;

    /**
     * 立刻执行
     * @param id 监控单元id
     */
    void runNow(String id);

    /**
     * 执行取娄
     *
     * @param warnId 监控单元id
     */
    void syncData(String warnId);

    /**
     * 发送告警
     *
     * @param warnId 监控单元id
     */
    void alert(String warnId);

    /**
     * 禁用启用监控单元
     */
    void enable(EnableDTO dto) throws SchedulerException;
}