package sdata.ops.indicator.config.mail;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MailModel {

    private String protocol;

    private String host;

    private String port;

    private String username;

    private String password;

    //是否开启ssl 默认0 不开启 1 开启
    private int sslEnable;

    //是否扫描全部文件夹 默认0 不开 开启1
    private int scanFull;

    //可以开启只获取昨天0点到当前时间内容邮件 默认0  开启 1 不开
    private int dataScope;

}
