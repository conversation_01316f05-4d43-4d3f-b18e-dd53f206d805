package sdata.ops.indicator.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.indicator.service.IndicatorInfoService;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:05
 */
@RestController
@RequestMapping("/indicator")
@RequiredArgsConstructor
public class IndicatorInfoController {

    private final IndicatorInfoService infoService;

//    private final IndicatorDataSourceService sourceService;
//
//
//    @GetMapping("/list")
//    public R<Object> list(IndicatorInfo info) {
//        LambdaQueryWrapper<IndicatorInfo> queryWrapper = new LambdaQueryWrapper<>(info);
//        queryWrapper.orderByDesc(IndicatorInfo::getCreateTime);
//        queryWrapper.eq(IndicatorInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
//        return R.data(infoService.list(queryWrapper));
//    }
//
//    @GetMapping("/dataSourceList")
//    public R<Object> dataSourceList(IndicatorInfo info) {
//        LambdaQueryWrapper<IndicatorInfo> queryWrapper = new LambdaQueryWrapper<>(info);
//        queryWrapper.select(IndicatorInfo::getId, IndicatorInfo::getName, IndicatorInfo::getIndicatorType);
//        queryWrapper.orderByDesc(IndicatorInfo::getCreateTime);
//        queryWrapper.eq(IndicatorInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
//        queryWrapper.eq(IndicatorInfo::getIndicatorType, "result");
//        List<IndicatorInfo> scriptList = infoService.list(queryWrapper);
//        List<IndicatorDataSource> sourceList = sourceService.list(Wrappers.lambdaQuery(IndicatorDataSource.class).in(IndicatorDataSource::getVariableConfig, "api"));
//        return R.data(mergeAndConvert(sourceList, scriptList));
//    }
//
//    @GetMapping("/execTypeList")
//    public R<Object> execTypeList() {
//        LambdaQueryWrapper<IndicatorInfo> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.select(IndicatorInfo::getId, IndicatorInfo::getName, IndicatorInfo::getIndicatorType);
//        queryWrapper.orderByDesc(IndicatorInfo::getCreateTime);
//        queryWrapper.eq(IndicatorInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
//        queryWrapper.eq(IndicatorInfo::getIndicatorType, "exec");
//        List<IndicatorInfo> scriptList = infoService.list(queryWrapper);
//        return R.data(scriptList);
//    }
//
//    private List<IndicatorMergeVO> mergeAndConvert(List<IndicatorDataSource> sourceList, List<IndicatorInfo> scriptList) {
//        List<IndicatorMergeVO> res = new ArrayList<>();
//        for (IndicatorDataSource source : sourceList) {
//            IndicatorMergeVO item = new IndicatorMergeVO();
//            item.setId(source.getId()).setType(source.getVariableConfig()).setName(source.getSourceName());
//            res.add(item);
//        }
//        for (IndicatorInfo info : scriptList) {
//            IndicatorMergeVO item = new IndicatorMergeVO();
//            item.setId(info.getId()).setType("result").setName(info.getName());
//            res.add(item);
//        }
//        return res;
//    }
//
//    @GetMapping("/shortlist")
//    public R<Object> shortlist() {
//        List<MappingDTO> res = infoService.listShortInfo();
//        return R.data(res);
//    }
//
//    @PostMapping("/save")
//    public R<Object> save(@RequestBody IndicatorInfo info) {
//        return infoService.saveInfo(info);
//    }
//
//    @PostMapping("/move")
//    public R<Object> move(@RequestBody IndicatorMoveVO info) {
//        return infoService.moveInfo(info);
//    }
//
//    public R<Object> remove(@RequestBody IndicatorInfo info) {
//        if (StrUtil.isEmptyIfStr(info.getId()) || StrUtil.isEmptyIfStr(info.getGroupId())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        LambdaUpdateWrapper<IndicatorInfo> updateInfo = Wrappers.<IndicatorInfo>lambdaUpdate().set(IndicatorInfo::getGroupId, info.getGroupId()).eq(IndicatorInfo::getId, info.getId());
//        infoService.update(updateInfo);
//        return R.success(MessageConstant.SAVE_SUCCESS);
//    }
//
//    @GetMapping("/detail")
//    public R<Object> detail(@RequestParam String id) {
//        return infoService.detail(id);
//    }
//
//    @PostMapping("/delete")
//    public R<Object> delete(@RequestBody IndicatorInfo info) {
//        return infoService.deleteInfo(info.getId());
//    }
//
//    @PostMapping("/sql/parse")
//    public R<Object> sqlParse(@RequestBody IndicatorInfo info) {
//        return infoService.sqlParse(info);
//    }
//
//    @PostMapping("/batch-save")
//    public R<Object> batchSave(@RequestBody BatchIndicatorInfoDTO dto) {
//        return infoService.batchSaveInfo(dto.getInfoList());
//    }
//
//    @PostMapping("/exec")
//    public R<Object> exec(@RequestBody IndicatorInfoDTO dto) {
//        if (StrUtil.isEmptyIfStr(dto.getId()) || StrUtil.isEmptyIfStr(dto.getType())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        if (dto.getParam() == null) {
//            dto.setParam(new JSONObject());
//        }
//        dto.setUserId(SecureUtil.currentUserId());
//        dto.setSave(true);
//        return infoService.execute(dto);
//    }
//
//    @PostMapping("/execUp")
//    public R<Object> execUp(@RequestBody IndicatorInfoDTO dto) {
//        if (StrUtil.isEmptyIfStr(dto.getId())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        if (dto.getParam() == null) {
//            dto.setParam(new JSONObject());
//        }
//        dto.getParam().set("id",dto.getId());
//        dto.setUserId(SecureUtil.currentUserId());
//        dto.setSave(true);
//        return infoService.executeUp(dto);
//    }
//
//    @GetMapping("/api/{id:[0-9]+}")
//    public R<Object> api(@PathVariable String id, HttpServletRequest req) {
//        Map<String, String[]> parameterMap = req.getParameterMap();
//        JSONObject param = new JSONObject();
//        for (String key : parameterMap.keySet()) {
//            String[] values = parameterMap.get(key);
//            if (values.length > 0) {
//                param.set(key, values[0]);
//            }
//        }
//        IndicatorInfo info = infoService.getById(id);
//        if (null == info || StrUtil.isEmptyIfStr(info.getType())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        IndicatorInfoDTO dto = new IndicatorInfoDTO();
//        BeanUtil.copyProperties(info, dto);
//        dto.setParam(param);
//        return infoService.execute(dto);
//    }
//
//    @PostMapping("/rpc")
//    public R<Object> rpc(@RequestBody IndicatorInfoDTO dto) {
//        IndicatorInfo info = infoService.getById(dto.getId());
//        if (null == info || StrUtil.isEmptyIfStr(info.getType())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        BeanUtil.copyProperties(info, dto);
//        return infoService.execute(dto);
//    }
//
//    /**
//     * 为了解决脚本调用自身导致的死循环
//     */
//    @GetMapping("/script-api/{id:[0-9]+}")
//    public R<Object> scriptApi(@PathVariable String id, HttpServletRequest req) {
//        // 校验是否存在该指标
//        IndicatorInfo info = infoService.getById(id);
//        if (null == info || StrUtil.isEmptyIfStr(info.getType())) {
//            return R.data(new ArrayList<>());
//        }
//        // 校验只支持SQL指标
//        if (!info.getType().equals(ExecuteType.SQL_READ.getType())) {
//            return R.data(new ArrayList<>());
//        }
//        // 封装参数
//        JSONObject param = new JSONObject();
//        Map<String, String[]> parameterMap = req.getParameterMap();
//        for (String key : parameterMap.keySet()) {
//            String[] values = parameterMap.get(key);
//            if (values.length > 0) {
//                param.set(key, values[0]);
//            }
//        }
//        // 返回结果
//        IndicatorInfoDTO dto = new IndicatorInfoDTO();
//        BeanUtil.copyProperties(info, dto);
//        dto.setParam(param);
//        return infoService.execute(dto);
//    }

}