package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;
import sdata.ops.indicator.mapper.OpsModelTableIndexMapper;
import sdata.ops.base.indicator.model.entity.OpsModelTableIndex;
import sdata.ops.indicator.service.OpsModelTableIndexService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_model_table_index(数据表索引配置表)】的数据库操作Service实现
 * @createDate 2025-08-01 14:15:54
 */
@Service
public class OpsModelTableIndexServiceImpl extends ServiceImpl<OpsModelTableIndexMapper, OpsModelTableIndex>
        implements OpsModelTableIndexService {
}




