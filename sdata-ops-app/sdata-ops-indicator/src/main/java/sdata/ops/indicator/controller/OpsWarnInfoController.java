package sdata.ops.indicator.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.common.annotation.SaveGroup;
import sdata.ops.common.annotation.UpdateGroup;
import sdata.ops.base.indicator.model.dto.*;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;
import sdata.ops.base.indicator.model.vo.FilterVo;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.config.rule.RuleUtil;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.enums.WarnAlertRuleTypeEnum;
import sdata.ops.indicator.enums.WarnLevelEnum;
import sdata.ops.indicator.handler.quartz.exception.TaskException;
import sdata.ops.indicator.service.OpsWarnConfigService;
import sdata.ops.indicator.service.OpsWarnInfoService;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 预警信息表控制器
 * @createDate 2025-08-16
 */
@RestController
@RequestMapping("/opsWarnInfo")
@RequiredArgsConstructor
public class OpsWarnInfoController extends PageCustomController {

    private final OpsWarnInfoService opsWarnInfoService;
    private final OpsWarnConfigService warnConfigService;
    private final SystemFeignService systemFeignService;

    /**
     * 下拉列表
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.QUERY,
            value = "预警信息-下拉菜单"
    )
    @GetMapping("/filter")
    public R<List<FilterVo>> fileter(FilterDTO dto) {
        if ("warnLevel".equals(dto.getKey())) {
            List<FilterVo> vos = Arrays.stream(WarnLevelEnum.values())
                    .map(e -> new FilterVo(e.getChnName(), e.getChnName()))
                    .collect(Collectors.toList());
            return R.data(vos);
        }
        return R.data(Collections.emptyList());
    }

    /**
     * 新增数据
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.INSERT,
            value = "监控单元-新增"
    )
    @PostMapping("/save")
    public R<Object> save(@Validated(SaveGroup.class) @RequestBody WarnInfoVO vo) throws SchedulerException, TaskException {
        // 检查表达式语法正确
        if (WarnAlertRuleTypeEnum.EXPRESSION_MATCH.is(vo.getWarnAlertRuleType())) {
            String errmsg = RuleUtil.lexCheck(vo.getWarnAlertExpression());
            if (errmsg != null) {
                return R.fail(errmsg + ":" + vo.getWarnAlertExpression());
            }
        }
        opsWarnInfoService.saveWarnInfo(vo);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 根据id查询
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.QUERY,
            value = "监控单元-详情"
    )
    @GetMapping("/detail")
    public R<Object> detail(@RequestParam("id") String id) {
        if (StrUtil.isEmpty(id)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        WarnInfoVO vo = opsWarnInfoService.getWarnInfo(id);
        if (vo != null) {
            // 补充用户名
            Map<String, String> userMap = systemFeignService.idNameMapper();
            vo.setCreateBy(userMap.get(vo.getCreateBy()));
            vo.setUpdateBy(userMap.get(vo.getUpdateBy()));
            return R.data(vo);
        }
        return R.fail(MessageConstant.DATA_NOT_EXISTS);
    }

    /**
     * 查询列表
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.QUERY,
            value = "监控单元-列表查询"
    )
    @GetMapping("/list")
    public R<Object> list(WarnInfoQueryDto dto) {
        LambdaQueryWrapper<OpsWarnInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(dto.getWarnGroupId()), OpsWarnInfo::getWarnGroupId, dto.getWarnGroupId());
        queryWrapper.like(StrUtil.isNotBlank(dto.getWarnName()), OpsWarnInfo::getWarnName, dto.getWarnName());
        // 可以根据需要添加查询条件
        List<OpsWarnInfo> list = opsWarnInfoService.list(queryWrapper);
        // 转vo
        List<WarnInfoVO> vos = BeanUtil.copyToList(list, WarnInfoVO.class);
        // 补充配置
        warnConfigService.fillConfig(vos);
        // 补充用户名
        Map<String, String> userMap = systemFeignService.idNameMapper();
        for (var info : vos) {
            info.setCreateBy(userMap.get(info.getCreateBy()));
            info.setUpdateBy(userMap.get(info.getUpdateBy()));
        }
        return R.data(vos);
    }

    /**
     * 分页查询列表
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.QUERY,
            value = "监控单元-分页查询"
    )
    @GetMapping("/page")
    public R<Object> page(WarnInfoQueryDto dto) {
        LambdaQueryWrapper<OpsWarnInfo> wrapper = Wrappers.lambdaQuery(OpsWarnInfo.class);
        wrapper.eq(StrUtil.isNotBlank(dto.getWarnGroupId()), OpsWarnInfo::getWarnGroupId, dto.getWarnGroupId());
        wrapper.like(StrUtil.isNotBlank(dto.getWarnName()), OpsWarnInfo::getWarnName, dto.getWarnName());
        Page<OpsWarnInfo> page = opsWarnInfoService.page(new Page<>(dto.getPageNo(), dto.getPageSize()), wrapper);
        // 转vo
        List<WarnInfoVO> vos = BeanUtil.copyToList(page.getRecords(), WarnInfoVO.class);
        // 补充配置
        warnConfigService.fillConfig(vos);
        // 补充用户名
        Map<String, String> userMap = systemFeignService.idNameMapper();
        for (var info : vos) {
            info.setCreateBy(userMap.get(info.getCreateBy()));
            info.setUpdateBy(userMap.get(info.getUpdateBy()));
        }
        Page<WarnInfoVO> voPage = Page.of(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(vos);
        Map<String, Object> resp = customPage(voPage, Function.identity());
        return R.data(resp);
    }

    /**
     * 根据id修改
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.UPDATE,
            value = "监控单元-修改"
    )
    @PostMapping("/update")
    public R<Object> update(@Validated(UpdateGroup.class) @RequestBody WarnInfoVO vo) throws SchedulerException, TaskException {
        // 检查表达式语法正确
        if (WarnAlertRuleTypeEnum.EXPRESSION_MATCH.is(vo.getWarnAlertRuleType())) {
            String errmsg = RuleUtil.lexCheck(vo.getWarnAlertExpression());
            if (errmsg != null) {
                return R.fail(errmsg + ":" + vo.getWarnAlertExpression());
            }
        }
        opsWarnInfoService.updateWarnInfo(vo);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }

    /**
     * 启用禁用
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.UPDATE,
            value = "监控单元-启用禁用"
    )
    @PostMapping("/enable")
    public R<Object> enable(@RequestBody EnableDTO dto) throws SchedulerException {
        opsWarnInfoService.enable(dto);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }

    /**
     * 根据id删除
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.DELETE,
            value = "监控单元-删除"
    )
    @PostMapping("/delete")
    public R<Object> delete(@RequestBody DeleteDTO dto) throws SchedulerException {
        if (StrUtil.isEmpty(dto.getId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        opsWarnInfoService.deleteWarnInfo(dto.getId());
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 立刻执行
     */
    @ControllerAuditLog(
            moduleName = ModuleName.WARN,
            operateType = OperateType.EXECUTE,
            value = "监控单元-立即执行"
    )
    @PostMapping("run")
    public R<Object> runNow(@RequestBody IdDTO dto) {
        opsWarnInfoService.runNow(dto.getId());
        return R.success("");
    }
}