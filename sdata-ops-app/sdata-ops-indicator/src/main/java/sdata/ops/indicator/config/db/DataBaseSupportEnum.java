package sdata.ops.indicator.config.db;


/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public enum DataBaseSupportEnum {

    /**
     * 支持的数据库
     * 参数含义：数据库类型、默认端口号、驱动类、jdbc地址、使用数据库连接池、验证有效性的Sql
     */
    MYSQL("mysql", 3306, "com.mysql.cj.jdbc.Driver",
            "jdbc:mysql://[host]:[port]/[dbname]?characterEncoding=utf8&useSSL=false&autoReconnect=true&failOverReadOnly=false&serverTimezone=Asia/Shanghai",
            "select 1"),

    SQLSERVER("sqlserver", 1433, "com.microsoft.sqlserver.jdbc.SQLServerDriver",
            "jdbc:sqlserver://[host]:[port];DatabaseName=[dbname]",
            "select 1"),

    ORACLESID("oraclesid", 1521, "oracle.jdbc.driver.OracleDriver",
            "jdbc:oracle:thin:@[host]:[port]:[dbname]",
            "select 1 from dual"),

    ORACLESERVICE("oracle", 1521, "oracle.jdbc.driver.OracleDriver",
            "jdbc:oracle:thin:@[host]:[port]/[dbname]",
            "select 1 from dual"),

    DB2("db2", 6789, "com.ibm.db2.jcc.DB2Driver",
            "jdbc:db2://[host]:[port]/[dbname]",
            "SELECT 1 FROM SYSIBM.SYSDUMMY1"),

    GBASE("gbase", 5258, "com.gbase.jdbc.Driver",
            "jdbc:gbase://[host]:[port]/[dbname]",
            "select 1"),

    HIVE("hive", 10000, "org.apache.hive.jdbc.HiveDriver",
            "jdbc:hive2://[host]:[port]/[dbname]",
            "select 1"),

    POSTGRESQL("postgresql", 5432, "org.postgresql.Driver",
            "jdbc:postgresql://[host]:[port]/[dbname]",
            "select version()"),
    DM("dm",5236,"dm.jdbc.driver.DmDriver","jdbc:dm://[host]:[port]?schema=[dbname]","select 1");

    /**
     * 支持的数据库名称
     */
    private String name;

    /**
     * 数据库默认端口
     */
    private int defaultPort;

    /**
     * 数据源驱动类
     */
    private String driverClass;

    /**
     * 数据源连接URL
     */
    private String driverUrl;

    /**
     * 验证链接的Sql
     */
    private String validSql;

    DataBaseSupportEnum(String name, int defaultPort, String driverClass, String driverUrl, String validSql) {
        this.name = name;
        this.defaultPort = defaultPort;
        this.driverClass = driverClass;
        this.driverUrl = driverUrl;
        this.validSql = validSql;
    }

    /**
     * 根据类型的名称，返回类型的枚举实例。
     *
     * @param typeName 类型名称
     */
    public static DataBaseSupportEnum fromTypeName(String typeName) {
        for (DataBaseSupportEnum type : DataBaseSupportEnum.values()) {
            if (type.getName().equalsIgnoreCase(typeName)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取数据库名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    /**
     * 获取默认端口
     *
     * @return
     */
    public int getDefaultPort() {
        return this.defaultPort;
    }

    /**
     * 获取驱动类
     *
     * @return
     */
    public String getDriverClass() {
        return this.driverClass;
    }

    /**
     * 获取数据库连接URL
     *
     * @return
     */
    public String getDriverUrl() {
        return this.driverUrl;
    }

    /**
     * 获取该数据库验证链接的Sql
     *
     * @return
     */
    public String getValidSql() {
        return this.validSql;
    }

}
