package sdata.ops.indicator.config.java;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.crypto.SecureUtil;
import com.taobao.arthas.compiler.DynamicCompiler;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存JAVA类实例
 *
 * <AUTHOR>
 * @date 2023/2/21 16:47
 */
public class CompileUtil {

    /**
     * 存放JAVA类实例
     */
    private static final ConcurrentHashMap<String, Object> JAVA_BEAN_CACHE = new ConcurrentHashMap<>();

    /**
     * 存放JAVA类内容HASH值
     */
    private static final ConcurrentHashMap<String, String> JAVA_CONTENT_HASH_CACHE = new ConcurrentHashMap<>();

    /**
     * 私有构造不实例化此类
     */
    private CompileUtil() {
    }

    /**
     * JAVA源码编译并实例化
     * 不考虑两次内容不一致，HASH值一致情况
     *
     * @param javaCode JAVA代码
     * @param params   有参构造的参数列表
     * @return JAVA代码实例化对象
     */
    public static Object javaCode2Object(String javaCode, Object... params) {
        //过滤空内容
        if (!StringUtils.hasText(javaCode)) {
            return null;
        }
        //获取类全路径与内容Hash值
        String fullClassName = getFullClassNameFromJavaCode(javaCode);
        String hashCode = SecureUtil.md5(javaCode) + ":" + javaCode.length();
        //缓存命中，返回类实例
        if (JAVA_CONTENT_HASH_CACHE.containsKey(fullClassName) && JAVA_CONTENT_HASH_CACHE.get(fullClassName).equals(hashCode)) {
            return JAVA_BEAN_CACHE.get(fullClassName);
        }
        //加锁，编译源码生成类实例
        synchronized (CompileUtil.class) {
            //缓存命中，返回类实例
            if (JAVA_CONTENT_HASH_CACHE.containsKey(fullClassName) && JAVA_CONTENT_HASH_CACHE.get(fullClassName).equals(hashCode)) {
                return JAVA_BEAN_CACHE.get(fullClassName);
            }
            //使用阿里工具包动态编译
            DynamicCompiler dynamicCompiler = new DynamicCompiler(CompileUtil.class.getClassLoader());
            dynamicCompiler.addSource(fullClassName, javaCode);
            Map<String, Class<?>> classMap = dynamicCompiler.build();
            if (CollectionUtils.isEmpty(classMap)) {
                return null;
            }
            //覆盖缓存内容
            JAVA_CONTENT_HASH_CACHE.put(fullClassName, hashCode);
            JAVA_BEAN_CACHE.put(fullClassName, ReflectUtil.newInstance(classMap.get(fullClassName), params));
            return JAVA_BEAN_CACHE.get(fullClassName);
        }
    }

    /**
     * 从JAVA源码中获取类全路径
     *
     * @param javaCode 源码
     * @return 类全路径
     */
    private static String getFullClassNameFromJavaCode(String javaCode) {
        //获取包名
        String startStr = "package ";
        int sPos = javaCode.indexOf(startStr);
        int endPos = javaCode.indexOf(";");
        String packageName = javaCode.substring(sPos + startStr.length(), endPos).trim();
        //获取类名
        startStr = "public class ";
        sPos = javaCode.indexOf(startStr);
        endPos = javaCode.indexOf(" ", sPos + startStr.length());
        String className = javaCode.substring(sPos + startStr.length(), endPos).trim();
        return packageName + "." + className;
    }

}
