package sdata.ops.indicator.controller;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.dto.DeleteDTO;
import sdata.ops.base.indicator.model.dto.WarnGroupListVO;
import sdata.ops.base.indicator.model.dto.WarnGroupUpdateDto;
import sdata.ops.base.indicator.model.entity.OpsWarnGroup;
import sdata.ops.common.annotation.SaveGroup;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.OpsWarnGroupService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 预警分类表控制器
 * @createDate 2025-08-16
 */
@RestController
@RequestMapping("/opsWarnGroup")
@RequiredArgsConstructor
public class OpsWarnGroupController extends PageCustomController {

    private final OpsWarnGroupService opsWarnGroupService;

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ControllerAuditLog(value = "预警分类-保存", operateType = OperateType.INSERT, moduleName = ModuleName.WARN)
    public R<Object> save(@Validated(SaveGroup.class) @RequestBody OpsWarnGroup opsWarnGroup) {
        if (opsWarnGroup.getOrderSort() == null) {
            // 排序到最前
            opsWarnGroup.setOrderSort(0);
        }
        if (StringUtils.isBlank(opsWarnGroup.getPid())) {
            // 默认是根节点
            opsWarnGroup.setPid("0");
        }
        opsWarnGroupService.save(opsWarnGroup);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 根据id查询
     */
    @GetMapping("/detail")
    @ControllerAuditLog(value = "预警分类-详情", operateType = OperateType.QUERY, moduleName = ModuleName.WARN)
    public R<Object> detail(@RequestParam("id") String id) {
        if (StringUtils.isEmpty(id)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        OpsWarnGroup opsWarnGroup = opsWarnGroupService.getById(id);
        return R.data(opsWarnGroup);
    }

    /**
     * 查询树形结构
     */
    @GetMapping("/tree")
    @ControllerAuditLog(value = "预警分类-树", operateType = OperateType.QUERY, moduleName = ModuleName.WARN)
    public R<List<WarnGroupListVO>> tree() {
        // 可以根据需要添加查询条件
        return R.data(opsWarnGroupService.tree());
    }

    /**
     * 根据id修改
     */
    @PostMapping("/update")
    @ControllerAuditLog(value = "预警分类-更新", operateType = OperateType.UPDATE, moduleName = ModuleName.WARN)
    public R<Object> update(@Validated @RequestBody WarnGroupUpdateDto dto) {
        if (StringUtils.isEmpty(dto.getId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        OpsWarnGroup entity = BeanUtil.copyProperties(dto, OpsWarnGroup.class);
        opsWarnGroupService.updateById(entity);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }

    /**
     * 根据id删除
     */
    @PostMapping("/delete")
    @ControllerAuditLog(value = "预警分类-删除", operateType = OperateType.DELETE, moduleName = ModuleName.WARN)
    public R<Object> delete(@RequestBody DeleteDTO dto) {
        return opsWarnGroupService.deleteGroup(dto.getId());
    }
}