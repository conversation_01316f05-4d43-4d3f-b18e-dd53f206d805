package sdata.ops.indicator.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsDataSource;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface OpsDataSourceService extends IService<OpsDataSource> {

    /**
     * 测试数据源连接
     *
     * @param sourceType
     * @param sourceConfig
     * @return
     */
  //  R<Object> testConnect(String sourceType, String sourceConfig);
}
