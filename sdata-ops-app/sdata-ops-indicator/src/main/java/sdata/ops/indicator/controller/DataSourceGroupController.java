package sdata.ops.indicator.controller;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.DataSourceGroup;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.DataSourceGroupService;

/**
 * <AUTHOR>
 * @date 2025/6/03
 */
@RestController
@RequestMapping("/dataSourceGroup")
@RequiredArgsConstructor
public class DataSourceGroupController {

    private final DataSourceGroupService groupService;

    @ControllerAuditLog(value = "数据源分类-树", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tree")
    public R<Object> tree() {
        return R.data(groupService.tree());
    }

    @ControllerAuditLog(value = "数据源分类-保存", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/save")
    public R<Object> save(@RequestBody DataSourceGroup group) {
        if (StrUtil.isEmpty(group.getPId())) {
            group.setPId("0");
        }
        groupService.saveOrUpdate(group);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "数据源分类-删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) {
        return groupService.deleteGroup(id);
    }

}