package sdata.ops.indicator.handler.flow;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONObject;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.ChainStatus;
import com.agentsflex.core.chain.event.ChainStatusChangeEvent;
import dev.tinyflow.core.Tinyflow;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.indicator.service.OpsWorkflowLogSummaryService;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.indicator.handler.flow.config.FlowPreparationConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 工作流执行器
 * desc: read flow from db-service then execute
 * author: zw
 * date: 2025/06/18
 */
@Component
@RequiredArgsConstructor
public class WorkFlowExecHandler {


    private final FlowPreparationConfig flowPreparationConfig;

    private final WorkFlowFeignService workFlowFeignService;

    private final OpsWorkflowLogSummaryService logSummaryService;

    public void initProvider(Tinyflow tinyflow) {
        //初始化相关提供者
        flowPreparationConfig.initProvidersAndNodeParsers(tinyflow);

    }

    /**
     * 指标调用流程
     *
     * @param flowId    流程id
     * @param variables 参数
     * @return map
     */
    public Map<String, Object> invokeWorkflowResult(String flowId, Map<String, Object> variables) {
        OpsAiWorkflow flow = workFlowFeignService.getFlowById(flowId);
        if (flow == null) {
            throw new RuntimeException("工作流不存在");
        }
        Tinyflow tinyflow = new Tinyflow(flow.getContent());
        initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        return chain.executeForResult(variables);
    }

    /**
     * 执行工作流
     *
     * @param flowId    流程id
     * @param variables 全局参数
     */
    public void invokeWorkflowExecution(String flowId, Map<String, Object> variables, String scheduleId, String execType, String executionId) {

        OpsAiWorkflow flow = workFlowFeignService.getFlowById(flowId);
        if (flow == null) {
            throw new RuntimeException("工作流不存在");
        }
        Tinyflow tinyflow = new Tinyflow(flow.getContent());
        initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        //添加监听器，获取执行日志
        List<JSONObject> eventList = new ArrayList<>();
        chain.addEventListener((event, chainThis) -> {
            if (event instanceof ChainStatusChangeEvent) {
                ChainStatus status = ((ChainStatusChangeEvent) event).getStatus();
                if (ChainStatus.FINISHED_ABNORMAL.equals(status)) {
                    String message = chainThis.getMessage();
                    JSONObject content = new JSONObject();
                    content.set("status", "error");
                    content.set("errorMsg", message);
                    // content.set("nodeId", node.getId());
                    eventList.add(content);
                }
            }
        });

        chain.addNodeErrorListener((e, node, map, chainThis) -> {
            String message = ExceptionUtil.getRootCauseMessage(e);
            JSONObject content = new JSONObject();
            content.set("nodeId", node.getId());
            content.set("status", "nodeError");
            content.set("errorMsg", message);

            eventList.add(content);
        });

        executeForRecord(flow, variables, chain, execType, scheduleId, executionId, eventList);

    }

    private void executeForRecord(OpsAiWorkflow flow, Map<String, Object> variables, Chain chain, String execType, String scheduleId, String executionId, List<JSONObject> eventList) {
        //日志写入
        JSONObject logJson = new JSONObject();
        logJson.set("flowId", flow.getId());
        logJson.set("flowName", flow.getTitle());
        logJson.set("startTime", DateUtil.date());
        try {
            chain.executeForResult(variables);
            logJson.set("errorMsg", chain.getStatus().equals(ChainStatus.FINISHED_ABNORMAL) ? chain.getMessage() : "");
        } catch (Exception e) {
            logJson.set("errorMsg", ExceptionUtil.getRootCauseMessage(e));
        }
        logJson.set("endTime", DateUtil.date());
        logJson.set("duration", DateUtil.between(logJson.getDate("startTime"), logJson.getDate("endTime"), DateUnit.MS));
        logJson.set("status", chain.getStatus());
        logJson.set("resultSummary", chain.getStatus().name());
        logJson.set("triggerType", execType);
        logJson.set("scheduleId", scheduleId);
        logJson.set("executionId", executionId);
        logSummaryService.saveWorkflowLogSummaryByWorkflowExecute(logJson, eventList);
    }

    /**
     * 执行工作流用于触发器调用
     * 工作流必须在返回的Map中包含"triggerResult"字段（Boolean类型）
     * 可选包含"taskInfo"字段（Map类型），用于传递任务创建信息
     * 如果没有triggerResult字段或值不是Boolean类型，默认返回false
     *
     * @param flowId 工作流ID
     * @param variables 执行参数
     * @return 触发器执行结果
     */
    public WorkflowTriggerResultVO executeWorkflowForTrigger(String flowId, Map<String, Object> variables) {
        try {
            Map<String, Object> result = invokeWorkflowResult(flowId, variables);

            // 智能解析 triggerResult，支持多层嵌套和类型转换
            Boolean triggerResult = extractTriggerResult(result);

            WorkflowTriggerResultVO resultVO = new WorkflowTriggerResultVO();
            resultVO.setTriggerResult(triggerResult != null ? triggerResult : false);

            // 智能解析工作流返回的任务信息（可选）
            Map<String, Object> taskInfo = extractTaskInfo(result);
            if (taskInfo != null && !taskInfo.isEmpty()) {
                resultVO.setTaskInfo(taskInfo);
            }

            return resultVO;
        } catch (Exception e) {
            // 工作流执行失败时，记录日志并返回false
            WorkflowTriggerResultVO resultVO = new WorkflowTriggerResultVO();
            resultVO.setTriggerResult(false);
            resultVO.setMessage("工作流执行失败: " + e.getMessage());
            return resultVO;
        }
    }

    /**
     * 智能提取 triggerResult 值，支持多层嵌套和类型转换
     *
     * @param result 工作流执行结果
     * @return Boolean 值，如果找不到或无法转换则返回 null
     */
    private Boolean extractTriggerResult(Map<String, Object> result) {
        if (result == null) {
            return null;
        }

        // 尝试多种可能的路径和键名
        String[] possibleKeys = {"triggerResult", "trigger_result", "result", "success", "shouldTrigger"};
        String[] possiblePaths = {"", "data.", "result.", "output."};

        for (String path : possiblePaths) {
            for (String key : possibleKeys) {
                Object value = getNestedValue(result, path + key);
                Boolean boolValue = convertToBoolean(value);
                if (boolValue != null) {
                    return boolValue;
                }
            }
        }

        return null;
    }

    /**
     * 智能提取 taskInfo 信息，支持多层嵌套
     *
     * @param result 工作流执行结果
     * @return Map 任务信息，如果找不到则返回 null
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractTaskInfo(Map<String, Object> result) {
        if (result == null) {
            return null;
        }

        // 尝试多种可能的路径和键名
        String[] possibleKeys = {"taskInfo", "task_info", "taskData", "task"};
        String[] possiblePaths = {"", "data.", "result.", "output."};

        for (String path : possiblePaths) {
            for (String key : possibleKeys) {
                Object value = getNestedValue(result, path + key);
                if (value instanceof Map) {
                    return (Map<String, Object>) value;
                }
            }
        }

        return null;
    }

    /**
     * 根据路径获取嵌套值
     *
     * @param map 源 Map
     * @param path 路径，如 "data.result.value"
     * @return 找到的值，如果路径不存在则返回 null
     */
    @SuppressWarnings("unchecked")
    private Object getNestedValue(Map<String, Object> map, String path) {
        if (map == null || path == null || path.isEmpty()) {
            return null;
        }

        String[] keys = path.split("\\.");
        Object current = map;

        for (String key : keys) {
            if (key.isEmpty()) {
                continue;
            }
            if (!(current instanceof Map)) {
                return null;
            }
            current = ((Map<String, Object>) current).get(key);
            if (current == null) {
                return null;
            }
        }

        return current;
    }

    /**
     * 将各种类型的值转换为 Boolean
     *
     * @param value 待转换的值
     * @return Boolean 值，如果无法转换则返回 null
     */
    private Boolean convertToBoolean(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        if (value instanceof String) {
            String str = ((String) value).toLowerCase().trim();
            if ("true".equals(str) || "yes".equals(str) || "1".equals(str)) {
                return true;
            }
            if ("false".equals(str) || "no".equals(str) || "0".equals(str)) {
                return false;
            }
        }

        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }

        return null;
    }

}
