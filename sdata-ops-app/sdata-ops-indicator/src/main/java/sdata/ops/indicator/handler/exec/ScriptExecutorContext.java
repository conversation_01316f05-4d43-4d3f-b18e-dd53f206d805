package sdata.ops.indicator.handler.exec;


import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ScriptExecutorContext {

    private final Map<String, ScriptExecutor> executors;

    public ScriptExecutorContext(Map<String, ScriptExecutor> executors) {
        this.executors = executors;
    }

    public ScriptExecutor getExecutor(String type) {
        return executors.get(type);
    }
}