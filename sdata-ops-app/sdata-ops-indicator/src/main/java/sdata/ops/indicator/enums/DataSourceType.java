package sdata.ops.indicator.enums;

import com.alibaba.druid.DbType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

@Getter
@AllArgsConstructor
public enum DataSourceType {

    api("api", null), mysql("mysql", DbType.mysql), oracle("oracle", DbType.oracle),dm("dm", DbType.dm),
    sqlserver("sqlserver", DbType.sqlserver),
    postgresql("postgresql", DbType.postgresql);

    /**
     * 注入spring中的beanName
     */
    private final String type;

    /**
     * 配置类节点实现类class
     */
    private final DbType dbType;

    public static Optional<ExecuteType> getEnumByValue(String paramValue) {
        for (ExecuteType nodeTypeEnum : ExecuteType.values()) {
            String nodeType = nodeTypeEnum.getType();
            if (nodeType.equals(paramValue)) {
                return Optional.of(nodeTypeEnum);
            }
        }
        return Optional.empty();
    }

}
