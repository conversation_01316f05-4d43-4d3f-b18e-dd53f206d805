package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsWarnConfig;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.indicator.enums.WarnConfigKeyEnum;

import java.util.Collection;

/**
* <AUTHOR>
* @description 针对表【ops_warn_config(预警信息配置表)】的数据库操作Service
* @createDate 2025-08-16
*/
public interface OpsWarnConfigService extends IService<OpsWarnConfig> {
    /**
     * 保存配置
     */
    void saveWarnConfig(String warnId, WarnInfoVO vo);

    /**
     * 把基本信息补充为完整配置
     *
     */
    void fillConfig(Collection<WarnInfoVO> vos);

    /**
     * 更新配置信息
     *
     * @param key    配置项
     * @param value  配置值
     * @param warnId 预警ID
     */
    void updateConfig(String warnId, WarnConfigKeyEnum key, Object value);
}