package sdata.ops.indicator.handler.flow.node;

import cn.hutool.json.JSONUtil;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import reactor.util.function.Tuple2;
import sdata.ops.common.core.util.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class JsonGenNode extends BaseNode {

    private final JSONObject data;

    public JsonGenNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        //输出容器
        Map<String, Object> res = new HashMap<>();
        // 获取输入参数
        Map<String, Object> map = chain.getParameterValues(this);

        JSONObject nodeConfig = data.getJSONObject("config");
        //获取json内容
        String json = nodeConfig.getString("jsonStr");
        //校验json格式
        Tuple2<Boolean, String> verity = JsonUtils.isValid(json);
        if (!verity.getT1()) {
            throw new RuntimeException("json节点异常：json数据格式不正确");
        }
        //替换json动态占位符
        Object fullStr = replacerFactVal(json, map);
        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            for (Parameter def : outputDefs) {
                res.put(def.getName(), fullStr);
            }
        }
        return res;
    }

    /**
     * 对 JSON 字符串中的动态参数 ${key} 进行安全替换
     *
     * @param json 原始 JSON 字符串
     * @param map  参数键值对映射
     * @return 替换后的 JSON 字符串
     */
    private Object replacerFactVal(String json, Map<String, Object> map) {
        // 解析原始 JSON 为 JSONObject/JSONArray
        Object parsedJson = JSONUtil.parse(json);

        // 递归处理 JSON 中的占位符
        processPlaceholders(parsedJson, map);

        // 保持原始 JSON 的格式化返回
        return parsedJson;
    }
    /**
     * 递归处理 JSON 中的占位符
     */
    private void processPlaceholders(Object jsonNode, Map<String, Object> map) {
        if (jsonNode instanceof cn.hutool.json.JSONObject) {
            cn.hutool.json.JSONObject jsonObj = (cn.hutool.json.JSONObject) jsonNode;

            // 处理对象值
            jsonObj.forEach((key, value) -> {
                if (value instanceof String) {
                    // 处理字符串类型的值
                    String strValue = (String) value;
                    if (strValue.startsWith("${") && strValue.endsWith("}")) {
                        String placeholder = strValue.substring(2, strValue.length() - 1);
                        if (map.containsKey(placeholder)) {
                            Object replacement = map.get(placeholder);
                            // 如果是简单类型，直接替换；如果是复杂类型，替换为对应结构
                            if (replacement instanceof String || replacement instanceof Number ||
                                    replacement instanceof Boolean) {
                                jsonObj.set(key, replacement);
                            } else {
                                jsonObj.set(key, replacement);
                            }
                        } else {
                            throw new IllegalArgumentException("未定义的占位符: " + strValue);
                        }
                    }
                } else {
                    // 递归处理嵌套结构
                    processPlaceholders(value, map);
                }
            });
        } else if (jsonNode instanceof List) {
            // 处理 JSON 数组
            @SuppressWarnings("unchecked")
            List<Object> list = (List<Object>) jsonNode;
            for (int i = 0; i < list.size(); i++) {
                Object element = list.get(i);
                if (element instanceof String) {
                    String strElement = (String) element;
                    if (strElement.startsWith("${") && strElement.endsWith("}")) {
                        String placeholder = strElement.substring(2, strElement.length() - 1);
                        if (map.containsKey(placeholder)) {
                            list.set(i, map.get(placeholder));
                        } else {
                            throw new IllegalArgumentException("未定义的占位符: " + strElement);
                        }
                    }
                } else {
                    processPlaceholders(element, map);
                }
            }
        }
    }


}
