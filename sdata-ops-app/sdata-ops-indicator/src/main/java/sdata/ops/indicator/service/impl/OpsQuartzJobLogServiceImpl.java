package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsQuartzJobLog;
import sdata.ops.base.indicator.model.entity.OpsWorkflowLogSummary;
import sdata.ops.indicator.mapper.OpsQuartzJobLogMapper;
import sdata.ops.indicator.service.OpsQuartzJobLogService;
import sdata.ops.indicator.service.OpsWorkflowLogSummaryService;

/**
* <AUTHOR>
* @description 针对表【ops_quartz_job_log(定时任务调度日志表)】的数据库操作Service实现
* @createDate 2025-07-28 19:20:32
*/
@Service
@RequiredArgsConstructor
public class OpsQuartzJobLogServiceImpl extends ServiceImpl<OpsQuartzJobLogMapper, OpsQuartzJobLog>
    implements OpsQuartzJobLogService {

    private final OpsWorkflowLogSummaryService wfDetailService;
    @Override
    public OpsWorkflowLogSummary  wfDetail(String executionId){
        LambdaQueryWrapper<OpsWorkflowLogSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpsWorkflowLogSummary::getExecutionId, executionId);
        return wfDetailService.getOne(wrapper);
    };

}




