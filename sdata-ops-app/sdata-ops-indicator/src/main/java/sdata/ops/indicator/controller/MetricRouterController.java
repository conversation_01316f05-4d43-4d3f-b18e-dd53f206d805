package sdata.ops.indicator.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.OpsMetricBasicService;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/metric-router")
@RequiredArgsConstructor
public class MetricRouterController {


    private final OpsMetricBasicService opsMetricBasicService;

    @ControllerAuditLog(value = "指标路由器", operateType = OperateType.EXECUTE, moduleName = ModuleName.INDICATOR)
    @RequestMapping("/**")
    public Object handleDynamicRequest(
            HttpServletRequest request,
            @RequestBody(required = false) Map<String, Object> bodyParams) {

        // 获取请求路径中的动态部分
        String requestURI = request.getRequestURI();
        String dynamicPath = extractDynamicPath(requestURI);

        // 获取HTTP方法
        String httpMethod = validateHttpMethod(request.getMethod());

        // 获取所有query参数
        Map<String, String> queryParams = extractQueryParameters(request);

        // 调用指标服务处理请求
        return opsMetricBasicService.executeMetricForApi(httpMethod, dynamicPath, queryParams, bodyParams);//;
    }


    /**
     * 从request中提取query参数
     */
    private Map<String, String> extractQueryParameters(HttpServletRequest request) {
        return request.getParameterMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue()[0] // 取第一个值
                ));
    }

    /**
     * 从URI中提取动态路径部分
     */
    private String extractDynamicPath(String requestURI) {
        String basePath = "/ops/metric-router/";
        if (!requestURI.startsWith(basePath)) {
            throw new IllegalArgumentException("Invalid request path");
        }
        return requestURI.substring(basePath.length());
    }

    /**
     * 验证HTTP方法
     */
    private String validateHttpMethod(String method) {
        if (!"GET".equalsIgnoreCase(method) && !"POST".equalsIgnoreCase(method)) {
            throw new UnsupportedOperationException("Method " + method + " is not supported");
        }
        return method.toUpperCase();
    }
}
