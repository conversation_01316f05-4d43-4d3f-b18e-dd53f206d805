package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsModelTableColumn;
import sdata.ops.indicator.mapper.OpsModelTableColumnMapper;
import sdata.ops.indicator.service.OpsModelTableColumnService;

/**
* <AUTHOR>
* @description 针对表【ops_model_table_column(数据表字段配置表)】的数据库操作Service实现
* @createDate 2025-08-01 14:15:54
*/
@Service
public class OpsModelTableColumnServiceImpl extends ServiceImpl<OpsModelTableColumnMapper, OpsModelTableColumn>
    implements OpsModelTableColumnService {

}




