package sdata.ops.indicator.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.DataSourceGroup;
import sdata.ops.base.indicator.model.entity.IndicatorHistory;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.entity.IndicatorRelease;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.*;
import sdata.ops.indicator.service.IndicatorRecycleService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@RequiredArgsConstructor
@Service
public class IndicatorRecycleServiceImpl extends ServiceImpl<IndicatorInfoMapper, IndicatorInfo> implements IndicatorRecycleService {

    private final IndicatorReleaseMapper releaseMapper;

    private final IndicatorHistoryMapper indicatorHistoryMapper;

    private final DataSourceGroupMapper groupMapper;

    /**
     * 指标恢复
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> reduction(String id) {
        if (StrUtil.isEmptyIfStr(id)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        IndicatorInfo info = this.getById(id);
        if (null == info) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        // 递归恢复分组
        this.reductionGroup(info.getGroupId());
        // 恢复指标
        LambdaUpdateWrapper<IndicatorInfo> infoWrapper = Wrappers.<IndicatorInfo>lambdaUpdate().set(IndicatorInfo::getDelFlag,
                CommonConstant.DEL_FLAG_0).eq(IndicatorInfo::getId, id);
        this.update(infoWrapper);
        return R.success(MessageConstant.REDUCTION_SUCCESS);
    }

    /**
     * 物理删除指标中心
     *
     * @param idList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> delete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return R.success(MessageConstant.DELETE_SUCCESS);
        }
        LambdaQueryWrapper<IndicatorInfo> infoWrapper = Wrappers.<IndicatorInfo>lambdaQuery().eq(IndicatorInfo::getDelFlag, CommonConstant.DEL_FLAG_1).in(IndicatorInfo::getId, idList);
        List<IndicatorInfo> infoList = this.list(infoWrapper);
        if (CollectionUtils.isEmpty(infoList)) {
            return R.success(MessageConstant.DELETE_SUCCESS);
        }
        List<String> ids = infoList.stream().map(IndicatorInfo::getId).collect(Collectors.toList());
        // 删除历史记录
        LambdaQueryWrapper<IndicatorHistory> historyWrapper = Wrappers.<IndicatorHistory>lambdaQuery().in(IndicatorHistory::getIndicatorId, ids);
        indicatorHistoryMapper.delete(historyWrapper);
        // 删除版本管理
        LambdaQueryWrapper<IndicatorRelease> releaseWrapper = Wrappers.<IndicatorRelease>lambdaQuery().in(IndicatorRelease::getIndicatorId, ids);
        releaseMapper.delete(releaseWrapper);
        // 删除指标中心
        this.removeByIds(ids);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 递归恢复分组
     *
     * @param groupId 分组id
     */
    private void reductionGroup(String groupId) {
        if (StrUtil.isEmptyIfStr(groupId)) {
            return;
        }
        DataSourceGroup group = groupMapper.selectById(groupId);
        if (null == group) {
            return;
        }
        Integer delFlag = group.getDeleted();
        if (delFlag == 0) {
            return;
        }
        // 将删除状态更新为未删除
        LambdaUpdateWrapper<DataSourceGroup> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(DataSourceGroup::getDeleted, CommonConstant.DEL_FLAG_0);
        updateWrapper.eq(DataSourceGroup::getId, group.getId());
        groupMapper.update(null, updateWrapper);
        this.reductionGroup(group.getPId());
    }
}
