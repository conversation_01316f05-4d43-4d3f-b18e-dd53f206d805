package sdata.ops.indicator.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.dto.MappingDTO;
import sdata.ops.base.indicator.model.entity.InOrOutConfigModel;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.entity.IndicatorRelease;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.*;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.IndicatorConstant;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.config.http.HttpHandler;
import sdata.ops.common.core.model.HttpRequestModel;
import sdata.ops.common.core.util.AesUtils;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.common.exception.ScriptException;
import sdata.ops.indicator.config.mail.MailManager;
import sdata.ops.indicator.config.mail.MailModel;
import sdata.ops.indicator.enums.ExecuteType;
import sdata.ops.indicator.handler.exec.ScriptExecutor;
import sdata.ops.indicator.mapper.IndicatorInfoMapper;
import sdata.ops.indicator.mapper.IndicatorReleaseMapper;
import sdata.ops.indicator.mapper.OpsDataSourceMapper;
import sdata.ops.indicator.service.IndicatorHistoryService;
import sdata.ops.indicator.service.IndicatorInfoService;
import sdata.ops.indicator.utils.SqlParseUtil;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Slf4j
@Service
@DependsOn("springBeanUtil")
@RequiredArgsConstructor
public class IndicatorInfoServiceImpl extends ServiceImpl<IndicatorInfoMapper, IndicatorInfo> implements IndicatorInfoService {

    private final IndicatorReleaseMapper releaseMapper;

    private final IndicatorHistoryService historyService;

    private final OpsDataSourceMapper dataSourceMapper;


    private SystemFeignService systemFeignService;



    private final Map<String, Object> locks = new HashMap<>();

    private Object getLock(String str) {
        synchronized (locks) {
            return locks.computeIfAbsent(str, k -> new Object());
        }
    }

    /**
     * 保存指标中心
     *
     * @param info
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Object> saveInfo(IndicatorInfo info) {
        if (StrUtil.isEmptyIfStr(info.getGroupId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        if (StrUtil.isEmptyIfStr(info.getType())) {
            // 默认为sql类型
            info.setType(ExecuteType.SQL_READ.getType());
        }
        // 如果是新增操作
        if (info.getId() == null) {
            int count = (int) (this.count() + 5);
            info.setOrderSort(count);
        }

        // 1、数据保存
        this.saveOrUpdate(info);
        LambdaQueryWrapper<IndicatorRelease> releaseWrapper = Wrappers.<IndicatorRelease>lambdaQuery().eq(IndicatorRelease::getIndicatorId, info.getId());
        Long count = releaseMapper.selectCount(releaseWrapper);
        String version = "VERSION_" + (count + 1);
        // 2、生成历史版本
        IndicatorRelease release = new IndicatorRelease();
        BeanUtil.copyProperties(info, release);
        release.setId(null);
        release.setCreateBy(null);
        release.setCreateTime(null);
        release.setUpdateBy(null);
        release.setUpdateTime(null);
        release.setIndicatorId(info.getId());
        release.setReleaseNo(version);
        releaseMapper.insert(release);
        // 3、更新版本号
        LambdaUpdateWrapper<IndicatorInfo> infoWrapper = Wrappers.<IndicatorInfo>lambdaUpdate().set(IndicatorInfo::getReleaseId, release.getId()).eq(IndicatorInfo::getId, info.getId());
        this.update(infoWrapper);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 获取指标详情
     *
     * @param id 指标id
     * @return
     */
    @Override
    public R<Object> detail(String id) {
        if (null == id) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        IndicatorInfo info = this.getById(id);
        if (null == info || info.getDelFlag().intValue() == CommonConstant.DEL_FLAG_1) {
            return R.fail(MessageConstant.PARAM_MISS);
        }

        IndicatorInfoVO result = new IndicatorInfoVO();
        BeanUtil.copyProperties(info, result);
        // 数据源ID
        if (!StrUtil.isEmptyIfStr(info.getDataSourceId())) {
            OpsDataSource dataSource = dataSourceMapper.selectById(info.getDataSourceId());
            if (null != dataSource) {
                result.setDataSourceName(dataSource.getSourceName());
            }
        }
        // 版本号
        if (!StrUtil.isEmptyIfStr(info.getReleaseId())) {
            IndicatorRelease release = releaseMapper.selectById(info.getReleaseId());
            if (null != release) {
                result.setReleaseNo(release.getReleaseNo());
            }
        }
        if (StrUtil.isEmptyIfStr(result.getScript())) {
            result.setScript("");
        }
        Map<String, String> nameMapping = systemFeignService.idNameMapper();
        result.setCreateBy(nameMapping.get(result.getCreateBy()));
        result.setUpdateBy(nameMapping.get(result.getCreateBy()));
        return R.data(result);
    }

    /**
     * 逻辑删除指标中心
     *
     * @param id 指标id
     * @return bool
     */
    @Override
    public R<Object> deleteInfo(String id) {
        if (null == id) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        // 逻辑删除指标中心
        IndicatorInfo delete = new IndicatorInfo();
        delete.setId(id);
        delete.setDelFlag(CommonConstant.DEL_FLAG_1);
        this.updateById(delete);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 通过特定的规则,去解析传递过来的sql语句,并返回,数据集合信息列表及参数条件信息列表
     *
     * @param info
     * @return
     */
    @Override
    public R<Object> sqlParse(IndicatorInfo info) {
        try {
            // 参数列表
            List<SqlParamInfoVO> paramInfoList = new ArrayList<>();
            // 字段列表
            List<SqlColumnInfoVO> columnInfoList = new ArrayList<>();
            SqlParseInfoVO result = new SqlParseInfoVO();
            result.setParamsInfoList(paramInfoList);
            result.setColumnInfoList(columnInfoList);
            String sql = info.getScript();
            //sql语句
            if (StrUtil.isEmptyIfStr(sql)) {
                return R.data(result);
            }
            // SQL 解析
            SqlParseUtil.extractSqlParameter(sql, paramInfoList);
            result.setParamsInfoList(paramInfoList);
            result.setColumnInfoList(columnInfoList);
            return R.data(result);
        } catch (Exception e) {
            log.error("sql解析功能异常", e);
            return R.fail(MessageConstant.SQL_PARSE_ERROR + e.getMessage());
        }
    }

    /**
     * @param info 指标参数
     * @return rs
     */
    @Override
    public R<Object> execute(IndicatorInfoDTO info) {
        // 如果为sql类型，校验数据源是否为空
        if (info.getType().equals(ExecuteType.SQL_READ.getType()) && StrUtil.isEmptyIfStr(info.getDataSourceId())) {
            throw new ScriptException("执行脚本错误,sql脚本未指定对应数据源链接");
        }
        ExecuteVO executeVO = new ExecuteVO().setDataSourceId(info.getDataSourceId()).setScript(info.getScript()).setUserId(info.getUserId()).setCurrentURL(info.getCurrentURL());
        try {
            ExecuteType executeType = ExecuteType.getEnumByValue(info.getType()).get();
            ScriptExecutor baseNode = SpringBeanUtil.getBean(executeType.getClassConfig());
            Object result = baseNode.execute(executeVO, info.getParam());
            return R.data(result);
        } catch (Exception e) {
            historyService.saveHistory(info, executeVO.getExecScript(), R.fail(e.getMessage()));
            log.error("脚本执行异常", e);
            return R.fail("执行异常：" + e.getMessage());
        }
    }

    /**
     * 更新调整版本
     * 添加数据源类型  api,邮件,脚本引用
     * 添加脚本处理器类型，邮件与qlExpress
     *
     * @param info 参数信息
     * @return endResult
     */
    @Override
    public R<Object> executeUp(IndicatorInfoDTO info) {

        /**
         *判定是否绑定数据源,数据源是否有前置处理
         */
        if (!StrUtil.isBlankIfStr(info.getDataSourceId())) {
            Object input = null;
            //如果是数据源 ，api单独执行 ，
            if (IndicatorConstant.API.equals(info.getDataSourceType())) {
                OpsDataSource source = dataSourceMapper.selectById(info.getDataSourceId());
                input = HttpHandler.sendHttpCallJson(JSONUtil.toBean(AesUtils.decryptStr(source.getSourceConfig()), HttpRequestModel.class));
            }
            //mail则返回邮箱邮件数组
            if (IndicatorConstant.MAIL.equals(info.getDataSourceType())) {
                OpsDataSource source = dataSourceMapper.selectById(info.getDataSourceId());
                input = MailManager.executeMailReceiver(JSONUtil.toBean(AesUtils.decryptStr(source.getSourceConfig()), MailModel.class));
            }
            //script是脚本本身需要的数据源来自于另一个脚本
            if (IndicatorConstant.RESULT.equals(info.getDataSourceType())) {
                IndicatorInfo ref = getById(info.getDataSourceId());
                IndicatorInfoDTO refDto = new IndicatorInfoDTO();
                refDto.setParam(info.getParam());
                BeanUtil.copyProperties(ref, refDto);
                R<Object> refResult = executeUp(refDto);
                input = refResult.isSuccess() ? refResult.getData() : null;
            }
            //输入前置处理配置不为空
            if (info.getInputConfig() != null) {
                InOrOutConfigModel configModel = JSONUtil.toBean(info.getInputConfig(), InOrOutConfigModel.class);
                if (IndicatorConstant.json.equals(configModel.getSourceType())) {
                    input = JSONUtil.getByPath((JSONObject) input, configModel.getJsonpath());
                }
            }
            info.getParam().set("data", input);
        }
        /**
         * 前置处理完成,进行脚本自身配置类型处理执行
         */
        return execute(info);
    }

    /**
     * 批量更新排序
     *
     * @param infoList 指标保存
     * @return bool
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> batchSaveInfo(List<IndicatorInfo> infoList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return R.fail(MessageConstant.PARAM_FAIL);
        }
        List<IndicatorInfo> updateList = new ArrayList<>(16);
        for (IndicatorInfo info : infoList) {
            String id = info.getId();
            Integer orderSort = info.getOrderSort();
            if (id != null && orderSort != null) {
                IndicatorInfo update = new IndicatorInfo();
                update.setId(id);
                update.setOrderSort(orderSort);
                updateList.add(update);
            }
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @Override
    public List<MappingDTO> listShortInfo() {
        return new ArrayList<>();
    }

    @Override
    public R<Object> moveInfo(IndicatorMoveVO info) {
        //baseMapper.moveInfoPos(info.getId(),info.getParentId());
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }



}
