package sdata.ops.indicator.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.agentsflex.core.chain.*;
import com.agentsflex.core.chain.event.ChainStatusChangeEvent;
import com.agentsflex.core.chain.event.NodeEndEvent;
import com.agentsflex.core.chain.event.NodeStartEvent;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.NodeParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.common.api.R;
import sdata.ops.common.config.web.MySseEmitter;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.indicator.handler.flow.WorkFlowExecHandler;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/indicator/workflow")
@RequiredArgsConstructor
public class WorkflowController {


    private final WorkFlowFeignService workFlowFeignService;


    private final WorkFlowExecHandler workFlowExecHandler;


    /**
     * 获取任务流的第一个入参
     *
     * @param id 任务流id
     * @return map
     */
    @ControllerAuditLog(value = "任务流-获取第一个入参", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/getByParams")
    public R<Object> getByParams(@RequestParam("id") String id) {
        OpsAiWorkflow workflow = workFlowFeignService.getFlowById(id);
        if (workflow == null) {
            return R.fail("工作流不存在");
        }

        String content = workflow.getContent();
        JSONObject json = JSONUtil.parseObj(content);
        JSONArray nodes = json.getJSONArray("nodes");
        if (nodes == null || nodes.isEmpty()) {
            return R.data(null);
        }
        List<JSONObject> firstNodeParams = new ArrayList<>();
        for (Object o : nodes) {
            JSONObject node = (JSONObject) o;
            if ("startNode".equalsIgnoreCase(node.getStr("type"))) {
                JSONObject data = node.getJSONObject("data");
                if (data != null) {
                    JSONArray params = data.getJSONArray("parameters");
                    if (params != null) {
                        params.forEach(param -> {
                            firstNodeParams.add((JSONObject) param);
                        });
                    }
                }
                break;
            }
        }

        return R.data(firstNodeParams);
    }

    /**
     * 阻塞响应式调用 -流程一次执行
     *
     * @param body 请求体
     * @return 执行结果
     */
    @ControllerAuditLog(value = "任务流-流程一次执行（阻塞）", operateType = OperateType.EXECUTE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/testRunning")
    public R<Object> testRunning(@RequestBody Map<String, Object> body) {

        String id = (String) body.get("id");
        @SuppressWarnings("unchecked")
        Map<String, Object> variables = (Map<String, Object>) body.get("variables");
        // 添加过滤逻辑：移除值为null或空字符串的键值对
        if (variables != null) {
            variables.entrySet().removeIf(entry -> entry.getValue() == null ||
                    (entry.getValue() instanceof String && ((String) entry.getValue()).isEmpty()));
        }
        OpsAiWorkflow workflow = workFlowFeignService.getFlowById(id);

        if (workflow == null) {
            return R.fail("工作流不存在");
        }

        Tinyflow tinyflow = new Tinyflow(workflow.getContent());
        workFlowExecHandler.initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        if (chain == null) {
            return R.fail("工作流为空");
        }

        Map<String, Object> result = chain.executeForResult(variables);

        return R.data(result, chain.getMessage());
    }


    /**
     * 流式响应调用 - 流程一次执行
     *
     * @param body     请求内容
     * @param response 流式响应内容
     * @return sse
     */
    @ControllerAuditLog(value = "任务流-流程一次执行（响应式）", operateType = OperateType.EXECUTE, moduleName = ModuleName.INDICATOR)
    @PostMapping("tryRunningStream")
    public SseEmitter tryRunningStream(
            @RequestBody Map<String, Object> body,
            HttpServletResponse response) {
        String id = (String) body.get("id");
        @SuppressWarnings("unchecked")
        Map<String, Object> variables = (Map<String, Object>) body.get("variables");
        // 添加过滤逻辑：移除值为null或空字符串的键值对
        if (variables != null) {
            variables.entrySet().removeIf(entry -> entry.getValue() == null ||
                    (entry.getValue() instanceof String && ((String) entry.getValue()).isEmpty()));
        }
        response.setContentType("text/event-stream");

        MySseEmitter emitter = new MySseEmitter((long) (1000 * 60 * 10));

        OpsAiWorkflow workflow = workFlowFeignService.getFlowById(id);
        if (workflow == null) {
            JSONObject content = new JSONObject();
            content.set("status", "error");
            content.set("errorMsg", "工作流不存在");

            JSONObject json = new JSONObject();
            json.set("content", content);

            emitter.sendAndComplete(JSONUtil.toJsonStr(json));
            return emitter;
        }

        Tinyflow tinyflow = new Tinyflow(workflow.getContent());
        workFlowExecHandler.initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        if (chain == null) {
            JSONObject content = new JSONObject();
            content.set("status", "error");
            content.set("errorMsg", "工作流配置错误");

            JSONObject json = new JSONObject();
            json.set("content", content);

            emitter.sendAndComplete(JSONUtil.toJsonStr(json));
            return emitter;
        }

        JSONObject json = new JSONObject();

        chain.addEventListener((event, chain12) -> {
            if (event instanceof NodeStartEvent) {
                JSONObject content = new JSONObject();
                ChainNode node = ((NodeStartEvent) event).getNode();
                content.set("nodeId", node.getId());
                content.set("status", "start");
                json.set("content", content);
                emitter.send(JSONUtil.toJsonStr(json));
            }
            if (event instanceof NodeEndEvent) {
                ChainNode node = ((NodeEndEvent) event).getNode();
                Map<String, Object> result = ((NodeEndEvent) event).getResult();
                JSONObject content = new JSONObject();
                content.set("nodeId", node.getId());
                content.set("status", "end");
                content.set("res", result);
                json.set("content", content);
                emitter.send(JSONUtil.toJsonStr(json));
            }
            if (event instanceof ChainStatusChangeEvent) {
                ChainStatus status = ((ChainStatusChangeEvent) event).getStatus();
                if (ChainStatus.FINISHED_ABNORMAL.equals(status)) {
                    String message = chain12.getMessage();
                    JSONObject content = new JSONObject();
                    content.set("status", "error");
                    content.set("errorMsg", message);
                    json.set("content", content);
                    emitter.sendAndComplete(JSONUtil.toJsonStr(json));
                }
            }
        });

        chain.addNodeErrorListener((e, node, map, chain1) -> {
            String message = ExceptionUtil.getRootCauseMessage(e);
            JSONObject content = new JSONObject();
            content.set("nodeId", node.getId());
            content.set("status", "nodeError");
            content.set("errorMsg", message);
            json.set("content", content);
            emitter.sendAndComplete(JSONUtil.toJsonStr(json));
        });

        ThreadUtil.execAsync(() -> {
            Map<String, Object> result = chain.executeForResult(variables);
            JSONObject content = new JSONObject();
            content.set("execResult", result);
            json.set("content", content);
            emitter.sendAndComplete(JSONUtil.toJsonStr(json));
        });
        return emitter;
    }

    @ControllerAuditLog(value = "任务流-单个执行", operateType = OperateType.EXECUTE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/singleRun")
    public R<Object> singleRun(
            @RequestBody Map<String, Object> body) {
        String id = (String) body.get("id");
        @SuppressWarnings("unchecked")
        Map<String, Object> variables = (Map<String, Object>) body.get("variables");
        @SuppressWarnings("unchecked")
        Map<String, Object> node = (Map<String, Object>) body.get("node");
        OpsAiWorkflow workflow = workFlowFeignService.getFlowById(id);
        if (workflow == null) {
            return R.fail("工作流不存在");
        }
        List<ChainNode> nodes = new ArrayList<>();
        Tinyflow tinyflow = new Tinyflow(workflow.getContent());
        workFlowExecHandler.initProvider(tinyflow);
        Chain fullChain = tinyflow.toChain();
        if (fullChain != null) {
            nodes = fullChain.getNodes();
        }
        Map<String, NodeParser> map = tinyflow.getChainParser().getNodeParserMap();
        NodeParser parser = map.get(node.get("type").toString());
        if (parser == null) {
            return R.fail("节点类型不存在");
        }
        ChainNode currentNode = parser.parse(new com.alibaba.fastjson.JSONObject(node), tinyflow);
        if (currentNode == null) {
            return R.fail("节点不存在");
        }
        currentNode.setInwardEdges(null);
        currentNode.setOutwardEdges(null);
        fixParamType(nodes, currentNode);
        Chain chain = new Chain();
        chain.addNode(currentNode);
        Map<String, Object> res = chain.executeForResult(variables);
        return R.data(res);
    }

    /**
     * 修正引用类的值类型
     */
    private void fixParamType(List<ChainNode> allNodes, ChainNode currentNode) {
        List<Parameter> currentParams = currentNode.getParameters();
        if (CollectionUtil.isEmpty(currentParams)) {
            return;
        }

        Map<String, DataType> refToDataTypeMap = new HashMap<>();

        for (ChainNode node : allNodes) {
            if (node == null || CollectionUtil.isEmpty(node.getParameters())) {
                continue;
            }

            String nodeId = node.getId();
            for (Parameter param : node.getParameters()) {
                if (param != null && param.getName() != null) {
                    refToDataTypeMap.put(nodeId + "." + param.getName(), param.getDataType());
                }
            }
        }

        for (Parameter parameter : currentParams) {
            if (parameter == null || !RefType.REF.equals(parameter.getRefType())) {
                continue;
            }

            parameter.setRefType(RefType.INPUT);
            String ref = parameter.getRef();

            if (StrUtil.isNotEmpty(ref) && refToDataTypeMap.containsKey(ref)) {
                parameter.setDataType(refToDataTypeMap.get(ref));
            }
        }
    }
}
