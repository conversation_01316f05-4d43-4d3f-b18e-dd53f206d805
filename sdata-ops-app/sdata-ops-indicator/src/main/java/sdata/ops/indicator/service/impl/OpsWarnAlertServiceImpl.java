package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsWarnAlert;
import sdata.ops.indicator.mapper.OpsWarnAlertMapper;
import sdata.ops.indicator.service.OpsWarnAlertService;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_alert(预警通知表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@RequiredArgsConstructor
public class OpsWarnAlertServiceImpl extends ServiceImpl<OpsWarnAlertMapper, OpsWarnAlert>
        implements OpsWarnAlertService {

}