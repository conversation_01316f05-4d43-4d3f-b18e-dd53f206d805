package sdata.ops.indicator.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsWarnConfig;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.indicator.enums.WarnConfigKeyEnum;
import sdata.ops.indicator.mapper.OpsWarnConfigMapper;
import sdata.ops.indicator.service.OpsWarnConfigService;

import java.util.*;
import java.util.stream.Collectors;

import static sdata.ops.indicator.enums.WarnConfigKeyEnum.*;
import static sdata.ops.indicator.enums.WarnConfigValueTypeEnum.*;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_config(预警信息配置表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@RequiredArgsConstructor
public class OpsWarnConfigServiceImpl extends ServiceImpl<OpsWarnConfigMapper, OpsWarnConfig>
        implements OpsWarnConfigService {
    private final WarnConfigKeyEnum[] CONFIG_ENUMS = {
            WarnConfigKeyEnum.WARN_SYNC_INDICATOR_ID,
            WarnConfigKeyEnum.WARN_SYNC_START_TIME,
            WarnConfigKeyEnum.WARN_SYNC_END_TIME,
            WarnConfigKeyEnum.WARN_SYNC_TYPE,
            WarnConfigKeyEnum.WARN_SYNC_FIXED_RATE_MINUTES,
            WarnConfigKeyEnum.WARN_SYNC_CRON,
            WarnConfigKeyEnum.WARN_ALERT_RULE_TYPE,
            WarnConfigKeyEnum.WARN_ALERT_EXPRESSION,
            WarnConfigKeyEnum.WARN_ALERT_EXPRESSION_DIM_KEY,
            WarnConfigKeyEnum.WARN_ALERT_EXPRESSION_DIM_VALUE,
            WarnConfigKeyEnum.DICT_IDS,
            WarnConfigKeyEnum.WARN_ALERT_CHANNELS,
            WarnConfigKeyEnum.WARN_SYNC_JOB_ID,
            WarnConfigKeyEnum.WARN_ALERT_JOB_ID,
            WarnConfigKeyEnum.MARKET,
    };
    @Override
    public void saveWarnConfig(String warnId, WarnInfoVO vo) {
        if (warnId == null || vo == null) {
            return;
        }
        // 删除旧配置
        super.lambdaUpdate()
                .eq(OpsWarnConfig::getWarnId, warnId)
                .in(OpsWarnConfig::getConfigKey, Arrays.stream(CONFIG_ENUMS).map(Enum::name).collect(Collectors.toList()))
                .remove();
        // 保存新配置
        var entities = new ArrayList<OpsWarnConfig>(16);
        int index = 0;
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncIndicatorId())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncStartTime())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncEndTime())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncType())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncFixedRateMinutes())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnSyncCron())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnAlertRuleType())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnAlertExpression())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnAlertDimKey())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnAlertDimValue())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getDictIds())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getWarnAlertChannels())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getSyncJobId())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getAlertJobId())).ifPresent(entities::add);
        Optional.ofNullable(this.createConfig(warnId, CONFIG_ENUMS[index++], vo.getMarket())).ifPresent(entities::add);
        if (!entities.isEmpty()) {
            super.saveBatch(entities);
        }
    }

    @Override
    public void fillConfig(Collection<WarnInfoVO> vos) {
        if (vos == null || vos.isEmpty()) {
            return;
        }

        Set<String> warnIds = vos.stream().map(WarnInfoVO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (warnIds.isEmpty()) {
            return;
        }
        Map<String, List<OpsWarnConfig>> configMap = super.lambdaQuery()
                .in(OpsWarnConfig::getWarnId, warnIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OpsWarnConfig::getWarnId));

        for (WarnInfoVO vo : vos) {
            List<OpsWarnConfig> cfgList = configMap.get(vo.getId());
            if (cfgList == null || cfgList.isEmpty()) {
                continue;
            }
            Map<String, String> keyMap = cfgList.stream()
                    .collect(Collectors.toMap(OpsWarnConfig::getConfigKey, OpsWarnConfig::getConfigValue, (a, b) -> b));

            Optional.ofNullable(keyMap.get(WARN_SYNC_LAST_TIMESTAMP.name())).map(Long::parseLong).ifPresent(vo::setWarnSyncLastTimestamp);
            Optional.ofNullable(keyMap.get(WARN_ALERT_LAST_TIMESTAMP.name())).map(Long::parseLong).ifPresent(vo::setWarnAlertLastTimestamp);
            Optional.ofNullable(keyMap.get(WARN_EXCEPTION_STATUS.name())).map(Integer::parseInt).ifPresent(vo::setWarnExceptionStatus);
            int index = 0;
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setWarnSyncIndicatorId);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setWarnSyncStartTime);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setWarnSyncEndTime);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(Integer::parseInt).ifPresent(vo::setWarnSyncType);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(Integer::parseInt).ifPresent(vo::setWarnSyncFixedRateMinutes);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setWarnSyncCron);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(Integer::parseInt).ifPresent(vo::setWarnAlertRuleType);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setWarnAlertExpression);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(e -> JSONUtil.toList(e, String.class)).ifPresent(vo::setWarnAlertDimKey);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(e -> JSONUtil.toList(e, Object.class)).ifPresent(vo::setWarnAlertDimValue);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(e -> JSONUtil.toList(e, long.class)).ifPresent(vo::setDictIds);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).map(e -> JSONUtil.toList(e, String.class)).ifPresent(vo::setWarnAlertChannels);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setSyncJobId);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setAlertJobId);
            Optional.ofNullable(keyMap.get(CONFIG_ENUMS[index++].name())).ifPresent(vo::setMarket);
        }
    }

    @Override
    public void updateConfig(String warnId, WarnConfigKeyEnum key, Object value) {
        // 先删除
        super.lambdaUpdate()
                .eq(OpsWarnConfig::getWarnId, warnId)
                .eq(OpsWarnConfig::getConfigKey, key.name())
                .remove();
        // 再插入
        Optional.ofNullable(this.createConfig(warnId, key, value)).ifPresent(this::save);
    }


    private OpsWarnConfig createConfig(String warnId, WarnConfigKeyEnum warnConfigKeyEnum, Object value) {
        if (value == null) {
            return null;
        }
        String configValue;
        if (warnConfigKeyEnum.TYPE == STRING || warnConfigKeyEnum.TYPE == NUMBER) {
            configValue = String.valueOf(value);
        } else if (warnConfigKeyEnum.TYPE == STRING_ARRAY || warnConfigKeyEnum.TYPE == NUMBER_ARRAY || warnConfigKeyEnum.TYPE == ARRAY) {
            configValue = JSONUtil.toJsonStr(value);
        } else {
            return null;
        }

        OpsWarnConfig entity = new OpsWarnConfig();
        entity.setWarnId(warnId);
        entity.setConfigKey(warnConfigKeyEnum.name());
        entity.setConfigValueType(warnConfigKeyEnum.TYPE.name());
        entity.setConfigValue(configValue);
        entity.setDeleted(0);
        return entity;
    }
}