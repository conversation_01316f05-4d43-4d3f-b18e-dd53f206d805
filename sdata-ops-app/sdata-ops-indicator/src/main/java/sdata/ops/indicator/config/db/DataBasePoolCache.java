package sdata.ops.indicator.config.db;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义数据连接池缓存，当前只存储数据源连接池，之后可扩展至全部数据存储
 *
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@Slf4j
public class DataBasePoolCache {

    /**
     * 类加载时实例化
     * -- GETTER --
     * 获取实例
     */
    @Getter
    private static final DataBasePoolCache instance = new DataBasePoolCache();
    /**
     * 存放数据源，作为数据源缓存
     */
    private final ConcurrentHashMap<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();


    /***
     * 存放jdbcTemplate
     */
    private final ConcurrentHashMap<String, JdbcTemplate> dataTemplate = new ConcurrentHashMap<>();

    public JdbcTemplate getJdbcTemplate(String datasourceId) {
        return dataTemplate.get(datasourceId);
    }

    /**
     * 数据源添加至缓存
     *
     * @param dataSourceId 数据源id
     * @param dataSource   数据源对象
     */
    public void addDataSourcePool(String dataSourceId, DataSource dataSource) {
        dataSourceCache.put(dataSourceId, dataSource);
        dataTemplate.put(dataSourceId, new JdbcTemplate(dataSource));
    }

    /**
     * 从自定义缓存中获取数据库连接池
     *
     * @param dataSourceId 数据源id
     * @return datasource
     */
    public DataSource getDataSourcePool(String dataSourceId) {
        return dataSourceCache.get(dataSourceId);
    }

    /**
     * 判断Map容器中是否存在该Key
     *
     * @param datasourceId 数据源id
     * @return bool
     */
    public boolean containsKey(String datasourceId) {
        return dataSourceCache.containsKey(datasourceId);
    }

    /**
     * 从自定义缓存中移除数据库连接池
     *
     * @param dataSourceId 数据源id
     */
    public void removeDataSourcePool(String dataSourceId) {
        try {
            dataSourceCache.remove(dataSourceId);
        } catch (Exception e) {
            log.error("从数据源缓存中移除数据源异常", e);
        }
    }

}
