package sdata.ops.indicator.service.impl;

import lombok.extern.slf4j.Slf4j;
import sdata.ops.base.indicator.model.vo.WarnAlertContext;
import sdata.ops.indicator.enums.WarnAlertChannelEnum;
import sdata.ops.indicator.service.AbstractWarnAlertService;
import sdata.ops.indicator.service.OpsWarnAlertService;

import java.util.List;

@Slf4j
public class WarnAlertSmsServiceImpl extends AbstractWarnAlertService {
    public WarnAlertSmsServiceImpl(OpsWarnAlertService warnAlertService) {
        super(warnAlertService);
    }

    @Override
    public boolean support(List<String> channels) {
        return WarnAlertChannelEnum.SMS.enabled(channels);
    }

    @Override
    public String channelName() {
        return WarnAlertChannelEnum.SMS.name();
    }

    @Override
    public void alert(WarnAlertContext context) {
        String warnId = context.getWarn().getId();
        List<String> dataList = context.getItems();
        Result executeResult = super.calc(warnId, dataList);
        super.executeResult(executeResult);
        log.info("todo 获取短信配置，短信通知");
    }
}
