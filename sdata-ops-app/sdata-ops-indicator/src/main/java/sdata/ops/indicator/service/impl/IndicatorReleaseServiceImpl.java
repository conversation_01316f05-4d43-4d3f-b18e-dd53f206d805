package sdata.ops.indicator.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.entity.IndicatorRelease;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.IndicatorReleaseMapper;
import sdata.ops.indicator.service.IndicatorInfoService;
import sdata.ops.indicator.service.IndicatorReleaseService;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@RequiredArgsConstructor
@Service
public class IndicatorReleaseServiceImpl extends ServiceImpl<IndicatorReleaseMapper, IndicatorRelease> implements IndicatorReleaseService {

    private final IndicatorInfoService infoService;

    /**
     * 版本还原
     *
     * @param releaseId 指标版本id
     * @return str
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Object> reduction(String releaseId) {
        if (StrUtil.isEmptyIfStr(releaseId)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        IndicatorRelease release = this.getById(releaseId);
        if (null == release) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        // 指标还原
        IndicatorInfo updateInfo = new IndicatorInfo();
        BeanUtil.copyProperties(release, updateInfo);
        updateInfo.setId(release.getIndicatorId() + "");
        updateInfo.setReleaseId(release.getId());
        if (StrUtil.isEmptyIfStr(release.getScript())) {
            updateInfo.setScript("");
        }
        infoService.saveOrUpdate(updateInfo);
        return R.success(MessageConstant.REDUCTION_SUCCESS);
    }
}
