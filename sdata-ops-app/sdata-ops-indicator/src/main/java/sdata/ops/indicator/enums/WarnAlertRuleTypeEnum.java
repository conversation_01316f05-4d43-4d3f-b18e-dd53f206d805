package sdata.ops.indicator.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum WarnAlertRuleTypeEnum {
    /**
     * 所有数据
     */
    ALL(1),
    /**
     * 表达式匹配
     */
    EXPRESSION_MATCH(2),
    ;
    private final int code;

    WarnAlertRuleTypeEnum(int code) {
        this.code = code;
    }

    public boolean is(Integer code) {
        return Objects.equals(this.code, code);
    }
}
