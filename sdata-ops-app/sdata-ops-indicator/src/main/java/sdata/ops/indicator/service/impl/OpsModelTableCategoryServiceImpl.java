package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsModelTableCategory;
import sdata.ops.indicator.mapper.OpsModelTableCategoryMapper;
import sdata.ops.indicator.service.OpsModelTableCategoryService;

/**
* <AUTHOR>
* @description 针对表【ops_model_table_category(数据表分类表)】的数据库操作Service实现
* @createDate 2025-08-01 14:15:54
*/
@Service
public class OpsModelTableCategoryServiceImpl extends ServiceImpl<OpsModelTableCategoryMapper, OpsModelTableCategory>
    implements OpsModelTableCategoryService {

}




