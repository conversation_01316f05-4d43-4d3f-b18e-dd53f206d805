package sdata.ops.indicator.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.entity.IndicatorHistory;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.IndicatorHistoryMapper;
import sdata.ops.indicator.service.IndicatorHistoryService;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@Service
public class IndicatorHistoryServiceImpl extends ServiceImpl<IndicatorHistoryMapper, IndicatorHistory> implements IndicatorHistoryService {

    /**
     * 保存执行历史
     *
     * @param info       指标信息
     * @param execScript 执行脚本
     * @param result     执行结果
     */
    @Override
    @Async("sdataopsTaskExecutor")
    public void saveHistory(IndicatorInfoDTO info, String execScript, R<Object> result) {
        if (Boolean.FALSE.equals(info.getSave())) {
            // 不保存历史记录
            return;
        }
        IndicatorHistory history = new IndicatorHistory();
        history.setIndicatorId(info.getId());
        history.setName(info.getName());
        history.setDataSourceId(info.getDataSourceId());
        history.setScript(info.getScript());
        history.setType(info.getType());
        history.setGroupId(info.getGroupId());
        history.setReleaseId(info.getReleaseId());
        history.setParamInfo(info.getParam().toString());
        history.setExecScript(execScript);
        history.setExecuteTime(new Date());
        if (!result.isSuccess()) {
            // 执行异常，保存异常记录
            history.setExceptionInfo(result.getMessage());
        }
        // TODO 请求来源和操作人
        history.setFromSystem("指标中心");
        history.setExecuteUser("sys");
        this.save(history);
    }

}
