package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class JsonGenNodeParser extends BaseNodeParser {

    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        JsonGenNode jsonGenNode = new JsonGenNode(data);
        // 添加输入参数
        addParameters(jsonGenNode, data);
        // 添加输出参数
        addOutputDefs(jsonGenNode, data);
        return jsonGenNode;
    }

    public String getNodeName() {
        return "json-output-node";
    }
}
