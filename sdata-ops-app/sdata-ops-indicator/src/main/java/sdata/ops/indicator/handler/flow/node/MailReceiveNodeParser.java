package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class MailReceiveNodeParser extends BaseNodeParser {
    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        MailReceiveNode mailReceiveNode = new MailReceiveNode(data);
        // 添加输入参数
        addParameters(mailReceiveNode, data);
        // 添加输出参数
        addOutputDefs(mailReceiveNode, data);
        return mailReceiveNode;
    }

    public String getNodeName() {
        return "mail-receive-node";
    }
}
