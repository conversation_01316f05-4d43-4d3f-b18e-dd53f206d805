package sdata.ops.indicator.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.WarnGroupListVO;
import sdata.ops.base.indicator.model.entity.OpsWarnGroup;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.OpsWarnGroupMapper;
import sdata.ops.indicator.service.OpsWarnGroupService;
import sdata.ops.indicator.service.OpsWarnInfoService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_group(预警分类表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@RequiredArgsConstructor
public class OpsWarnGroupServiceImpl extends ServiceImpl<OpsWarnGroupMapper, OpsWarnGroup>
        implements OpsWarnGroupService {

    @Override
    public List<WarnGroupListVO> tree() {
        List<WarnGroupListVO> list = getBaseMapper().groupList();

        // 根节点
        var rootList = new ArrayList<WarnGroupListVO>(16);
        // 根据父id分组
        var groupByPid = new HashMap<String, List<WarnGroupListVO>>(16);
        for (var vo : list) {
            if (StrUtil.isBlank(vo.getPid())) {
                rootList.add(vo);
            }
            groupByPid.computeIfAbsent(vo.getPid(), k -> new ArrayList<>(16)).add(vo);
        }
        // 收集根节点
        for (var vo : rootList) {
            vo.setChildren(groupByPid.getOrDefault(vo.getId(), Collections.emptyList()));
        }
        return rootList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> deleteGroup(String id) {
        // 子分组
        OpsWarnGroup one = lambdaQuery().eq(OpsWarnGroup::getPid, id).last("LIMIT 1").one();
        if (one != null) {
            return R.fail(MessageConstant.DELETE_FAIL_GROUP);
        }

        // 关联的监控单元
        OpsWarnInfo info = SpringUtil.getBean(OpsWarnInfoService.class).lambdaQuery()
                .eq(OpsWarnInfo::getWarnGroupId, id)
                .last("LIMIT 1")
                .one();
        if (info != null) {
            return R.fail(MessageConstant.DELETE_FAIL_INDICATOR);
        }
        this.removeById(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}