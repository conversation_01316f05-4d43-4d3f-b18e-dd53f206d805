package sdata.ops.indicator.utils;

import org.apache.commons.lang3.StringUtils;
import sdata.ops.base.indicator.model.entity.OpsModelTableIndex;

public class IndexSQLGenerator {

    /**
     * 生成创建索引的SQL语句
     * @param tableName 表名
     * @param index 索引配置
     * @return 完整的SQL语句
     */
    public static String generateCreateIndexSQL(String tableName, OpsModelTableIndex index) {
        if (index == null || StringUtils.isBlank(index.getIndexName())
            || StringUtils.isBlank(index.getColumnNames())) {
            throw new IllegalArgumentException("索引配置不完整");
        }

        StringBuilder sql = new StringBuilder();
        
        // 处理主键索引
        if ("PRIMARY".equalsIgnoreCase(index.getIndexType())) {
            sql.append("ALTER TABLE ").append(tableName)
               .append(" ADD PRIMARY KEY (").append(index.getColumnNames()).append(")");
        } 
        // 处理唯一索引
        else if ("UNIQUE".equalsIgnoreCase(index.getIndexType())) {
            sql.append("CREATE UNIQUE INDEX ").append(index.getIndexName())
               .append(" ON ").append(tableName)
               .append(" (").append(index.getColumnNames()).append(")");
        }
        // 处理普通索引
        else if ("NORMAL".equalsIgnoreCase(index.getIndexType())) {
            sql.append("CREATE INDEX ").append(index.getIndexName())
               .append(" ON ").append(tableName)
               .append(" (").append(index.getColumnNames()).append(")");
        }
        // 处理全文索引
        else if ("FULLTEXT".equalsIgnoreCase(index.getIndexType())) {
            sql.append("CREATE FULLTEXT INDEX ").append(index.getIndexName())
               .append(" ON ").append(tableName)
               .append(" (").append(index.getColumnNames()).append(")");
        } else {
            throw new IllegalArgumentException("不支持的索引类型: " + index.getIndexType());
        }

        // 添加索引注释(如果支持)
        if (StringUtils.isNotBlank(index.getDescription())) {
            sql.append(" COMMENT '").append(index.getDescription()).append("'");
        }

        return sql.toString();
    }

    /**
     * 生成删除索引的SQL语句
     * @param tableName 表名
     * @param index 索引配置
     * @return 删除索引的SQL
     */
    public static String generateDropIndexSQL(String tableName, OpsModelTableIndex index) {
        if (index == null || StringUtils.isBlank(index.getIndexName())) {
            throw new IllegalArgumentException("索引配置不完整");
        }

        // 主键索引特殊处理
        if ("PRIMARY".equalsIgnoreCase(index.getIndexType())) {
            return "ALTER TABLE " + tableName + " DROP PRIMARY KEY";
        }

        return "ALTER TABLE " + tableName + " DROP INDEX " + index.getIndexName();
    }
}
