package sdata.ops.indicator.handler.quartz.job;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.common.api.ScheduleConstants;


/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, OpsQuartzJob sysJob) throws Exception
    {
        JobInvokeUtil.invokeMethod(sysJob, (String) context.getJobDetail().getJobDataMap().get(ScheduleConstants.EXECUTION_ID));
    }
}
