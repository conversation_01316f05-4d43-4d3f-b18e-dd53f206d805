package sdata.ops.indicator.enums;

import sdata.ops.base.system.model.entity.OpsSysCalendar;

import static sdata.ops.indicator.enums.WarnConfigValueTypeEnum.*;

public enum WarnConfigKeyEnum {
    /**
     * 预警同步指标ID
     */
    WARN_SYNC_INDICATOR_ID(STRING),
    /**
     * 同步数据时段-开始
     */
    WARN_SYNC_START_TIME(STRING),
    /**
     * 同步数据时段-结束
     */
    WARN_SYNC_END_TIME(STRING),
    /**
     * 预警同步类型, 1固定频率，2 CRON表达式
     */
    WARN_SYNC_TYPE(NUMBER),
    /**
     * 预警同步频率(分钟)
     */
    WARN_SYNC_FIXED_RATE_MINUTES(NUMBER),
    /**
     * 预警同步CRON表达式
     */
    WARN_SYNC_CRON(STRING),
    /**
     * 最后同步时间
     */
    WARN_SYNC_LAST_TIMESTAMP(NUMBER),
    /**
     * 告警规则,1:有数据就告警 2:根据表达式匹配规则
     */
    WARN_ALERT_RULE_TYPE(NUMBER),
    /**
     * 最后告警时间
     */
    WARN_ALERT_LAST_TIMESTAMP(NUMBER),
    /**
     * 告警规则表达式
     */
    WARN_ALERT_EXPRESSION(STRING),
    /**
     * 告警规则表达式维度
     */
    WARN_ALERT_EXPRESSION_DIM_KEY(STRING_ARRAY),
    /**
     * 告警规则表达式维度值
     */
    WARN_ALERT_EXPRESSION_DIM_VALUE(STRING_ARRAY),
    /**
     * 告警渠道, ["MAIL","SMS","WECHAT"]
     */
    WARN_ALERT_CHANNELS(STRING_ARRAY),
    /**
     * 关联字典
     */
    DICT_IDS(NUMBER_ARRAY),
    /**
     * 取数任务id
     */
    WARN_SYNC_JOB_ID(STRING),
    /**
     * 告警任务id
     */
    WARN_ALERT_JOB_ID(STRING),
    /**
     * 异常状态, 1:取数为null  2:取数指标执行异常 3: 告警异常
     */
    WARN_EXCEPTION_STATUS(NUMBER),
    /**
     * 日历类型，规定只按指定日历执行定时任务
     * 1：A股，2：港股，3：银行间
     * @see OpsSysCalendar#market
     */
    MARKET(STRING),

    //... 待补充
    ;

    WarnConfigKeyEnum(WarnConfigValueTypeEnum TYPE) {
        this.TYPE = TYPE;
    }

    public final WarnConfigValueTypeEnum TYPE;
    ;
}
