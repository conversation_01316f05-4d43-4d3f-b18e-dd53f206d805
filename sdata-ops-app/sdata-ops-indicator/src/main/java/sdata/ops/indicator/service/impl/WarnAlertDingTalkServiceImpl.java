package sdata.ops.indicator.service.impl;

import lombok.extern.slf4j.Slf4j;
import sdata.ops.base.indicator.model.vo.WarnAlertContext;
import sdata.ops.indicator.enums.WarnAlertChannelEnum;
import sdata.ops.indicator.service.AbstractWarnAlertService;
import sdata.ops.indicator.service.OpsWarnAlertService;

import java.util.List;

@Slf4j
public class WarnAlertDingTalkServiceImpl extends AbstractWarnAlertService {
    public WarnAlertDingTalkServiceImpl(OpsWarnAlertService warnAlertService) {
        super(warnAlertService);
    }

    @Override
    public boolean support(List<String> channels) {
        return WarnAlertChannelEnum.DING_TALK.enabled(channels);
    }

    @Override
    public String channelName() {
        return WarnAlertChannelEnum.DING_TALK.name();
    }

    @Override
    public void alert(WarnAlertContext context) {
        String warnId = context.getWarn().getId();
        List<String> dataList = context.getItems();
        Result executeResult = super.calc(warnId, dataList);
        super.executeResult(executeResult);
        log.info("todo 发钉钉消息");
    }
}
