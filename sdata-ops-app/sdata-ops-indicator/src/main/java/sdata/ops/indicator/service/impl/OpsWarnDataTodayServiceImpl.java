package sdata.ops.indicator.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.sql.SqlExecutor;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ql.util.express.DefaultContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.WarnInfoQueryDto;
import sdata.ops.base.indicator.model.entity.OpsWarnDataHistory;
import sdata.ops.base.indicator.model.entity.OpsWarnDataToday;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;
import sdata.ops.base.indicator.model.vo.SqlColumnInfoVO;
import sdata.ops.base.indicator.model.vo.WarnBenchVo;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.base.indicator.model.vo.WarnResultVo;
import sdata.ops.base.system.model.entity.OpsSysDictItem;
import sdata.ops.common.api.R;
import sdata.ops.common.config.rule.RuleUtil;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.indicator.enums.EnabledEnum;
import sdata.ops.indicator.enums.WarnAlertRuleTypeEnum;
import sdata.ops.indicator.enums.WarnRunStatusEnum;
import sdata.ops.indicator.mapper.OpsWarnDataTodayMapper;
import sdata.ops.indicator.service.*;
import sdata.ops.system.api.feign.SystemFeignService;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_data_today(当前预警数据表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OpsWarnDataTodayServiceImpl extends ServiceImpl<OpsWarnDataTodayMapper, OpsWarnDataToday>
        implements OpsWarnDataTodayService {
    private @Autowired OpsWarnInfoService warnInfoService;
    private @Autowired OpsWarnReadLogService warnReadLogService;
    private @Autowired OpsWarnConfigService warnConfigService;
    private final Executor executor = Executors.newWorkStealingPool();

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveData(List<String> dataList, WarnInfoVO vo) {
        if (vo == null || StrUtil.isBlank(vo.getId())) {
            return;
        }

        // 删除旧数据
        super.lambdaUpdate().eq(OpsWarnDataToday::getWarnId, vo.getId()).remove();
        // 删除旧已读记录
        warnReadLogService.removeOld(vo.getId(), null);

        String today = DateUtil.today();
        List<OpsWarnDataToday> entities = dataList
                .stream()
                .filter(item -> match(vo, item))
                .map(item -> {
                    OpsWarnDataToday warnDataToday = new OpsWarnDataToday();
                    warnDataToday.setWarnId(vo.getId());
                    warnDataToday.setWarnDate(today);
                    warnDataToday.setWarnContent(item);
                    warnDataToday.setDeleted(0);
                    return warnDataToday;
                })
                .collect(Collectors.toList());

        if (entities.isEmpty()) {
            return;
        }
        this.saveBatch(entities);
    }

    @Override
    public void transferTodayToHistory() {
        String copySql = "INSERT INTO sdata_ops.ops_warn_data_history \n" +
                "(id, warn_id, warn_date, warn_content, deleted, create_by, create_time, update_by, update_time)\n" +
                "SELECT id, warn_id, warn_date, warn_content, deleted, create_by, create_time, update_by, update_time \n" +
                "FROM sdata_ops.ops_warn_data_today\n" +
                "WHERE warn_date <= (date(?) - INTERVAL 1 DAY) AND deleted = 0";
        String deleteSql = "DELETE FROM sdata_ops.ops_warn_data_today WHERE warn_date <= (date(?) - INTERVAL 1 DAY)";
        String today = DateUtil.today();

        DataSource dataSource = SpringUtil.getBean(DataSource.class);
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            connection.setAutoCommit(false);
            // 从当前表复制到历史表
            int copyCount = SqlExecutor.execute(connection, copySql, today);
            // 清除当前表
            int removeCount = SqlExecutor.execute(connection, deleteSql, today);
            connection.commit();
            log.info("迁移当天数据到历史表，复制{}条，删除{}条", copyCount, removeCount);
        } catch (SQLException e) {
            try {
                if (connection != null) {
                    connection.rollback();
                }
            } catch (SQLException ex) {
                log.error("回滚失败", ex);
                throw new RuntimeException(ex);
            }
            log.error("迁移当天数据到历史表失败", e);
            throw new RuntimeException(e);
        } finally {
            DbUtil.close(connection);
        }
    }

    @Override
    public List<WarnBenchVo> workbenchList(WarnInfoQueryDto dto) {
        // 获取当前登录用户ID，如果未登录则设为"-1"
        String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";

        List<WarnBenchVo> records = baseMapper.workbenchList(userId, dto.getWarnGroupId(), dto.getWarnName());

        // 转vo,补充配置信息
        List<WarnInfoVO> configs = BeanUtil.copyToList(records, WarnInfoVO.class);
        warnConfigService.fillConfig(configs);
        Map<String, WarnInfoVO> configMap = configs.stream().collect(Collectors.toMap(WarnInfoVO::getId, Function.identity(), (a, b) -> b));
        for (WarnBenchVo record : records) {
            WarnInfoVO config = configMap.get(record.getId());
            if (config != null) {
                record.belong(config);
            }
        }

        // 获取指定字典类型  dictId -> dictType
        Map<Long, String> dictMap = findDictMap(dto.getDictTypes());

        // 根据字典类型过滤
        if (CollUtil.isNotEmpty(dto.getDictTypes())) {
            if (dictMap.isEmpty()) {
                return Collections.emptyList();
            }
            records.removeIf(vo -> CollUtil.isNotEmpty(vo.getDictIds()) && !dictMap.keySet().containsAll(vo.getDictIds()));
        }

        // 补充字典类型
        for (WarnBenchVo record : records) {
            record.setDictTypes(record.getDictIds().stream().map(dictMap::get).filter(Objects::nonNull).collect(Collectors.toSet()));
        }
        // 排序
        records.sort(Comparator.comparing(WarnBenchVo::getWarnSyncLastTime/*, Comparator.nullsLast(Date::compareTo)*/).reversed());

        return records;
    }

    @Override
    public List<WarnResultVo> centerList(WarnInfoQueryDto dto) {
        // 获取当前登录用户ID，如果未登录则设为"-1"
        String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";

        List<OpsWarnInfo> infos = warnInfoService.lambdaQuery()
                // 只查启用的
                .eq(OpsWarnInfo::getWarnStatus, EnabledEnum.ENABLED.getCode())
                .eq(StrUtil.isNotBlank(dto.getWarnGroupId()), OpsWarnInfo::getWarnGroupId, dto.getWarnGroupId())
                .like(StrUtil.isNotBlank(dto.getWarnName()), OpsWarnInfo::getWarnName, dto.getWarnName())
                .eq(OpsWarnInfo::getCreateBy, userId)
                .orderByDesc(OpsWarnInfo::getCreateTime)
                .list();
        if (infos.isEmpty()) {
            return Collections.emptyList();
        }

        // 转vo,补充配置信息
        List<WarnInfoVO> vos = BeanUtil.copyToList(infos, WarnInfoVO.class);
        warnConfigService.fillConfig(vos);

        // 获取指定字典类型  dictId -> dictType
        Map<Long, String> dictMap = findDictMap(dto.getDictTypes());
        if (CollUtil.isNotEmpty(dto.getDictTypes())) {
            if (dictMap.isEmpty()) {
                return Collections.emptyList();
            }

            vos.removeIf(vo -> CollUtil.isNotEmpty(vo.getDictIds()) && !dictMap.keySet().containsAll(vo.getDictIds()));
        }
        if (vos.isEmpty()) {
            return Collections.emptyList();
        }


        // 异步请求，会注意并发问题
        List<WarnResultVo> ret = Collections.synchronizedList(new ArrayList<>(infos.size()));
        CompletableFuture[] futures = vos.stream()
                .map(e -> CompletableFuture.supplyAsync(() -> unitStatus(e, dto.getStartDate(), dto.getEndDate()), executor))
                // 执行完异步任务，把返回的结果保存到列表里
                .map(f -> f.thenAccept(ret::add))
                .toArray(CompletableFuture[]::new);

        // 等所有异步任务完成
        CompletableFuture.allOf(futures).join();
        for (WarnResultVo vo : ret) {
            // 获取字典类型
            var types = vo.getDictIds()
                    .stream()
                    .map(dictMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            // 填充字典
            vo.setDictTypes(types);
        }
        // 返回数据
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, Object>> detail(String id, String startDate, String endDate) {
        OpsWarnInfo warn = warnInfoService.getById(id);
        if (warn == null) {
            return Collections.emptyList();
        }
        // 记录已读
        if (includeToday(startDate, endDate)) {
            String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";
            // 删除多余记录
            warnReadLogService.removeOld(id, userId);
            // 记录已读
            warnReadLogService.addNew(id, userId);
        }
        return unitDetail(warn, startDate, endDate);
    }

    private List<Map<String, Object>> unitDetail(OpsWarnInfo warn, String startDate, String endDate) {
        WarnInfoVO vo = BeanUtil.copyProperties(warn, WarnInfoVO.class);
        warnConfigService.fillConfig(List.of(vo));

        var dataList = new LinkedList<Map<String, Object>>();
        // 查询当前数据
        if (includeToday(startDate, endDate)) {
            var contents = super.lambdaQuery().eq(OpsWarnDataToday::getWarnId, warn.getId()).list().stream()
                    .map(OpsWarnDataToday::getWarnContent)
                    .map(JsonUtils::toMap)
                    .collect(Collectors.toList());
            dataList.addAll(contents);
        }

        // 查询历史数据
        if (includeHistory(startDate, endDate)) {
            var contents = SpringUtil.getBean(OpsWarnDataHistoryService.class).lambdaQuery()
                    .select(OpsWarnDataHistory::getWarnContent)
                    .eq(OpsWarnDataHistory::getWarnId, vo.getId())
                    .between(OpsWarnDataHistory::getWarnDate, startDate, endDate)
                    .list()
                    .stream()
                    .map(OpsWarnDataHistory::getWarnContent)
                    .map(JsonUtils::toMap)
                    .collect(Collectors.toList());
            dataList.addAll(contents);
        }
        // 替换表头
        replaceHead(vo.getWarnSyncIndicatorId(), dataList);
        return dataList;
    }

    private WarnResultVo unitStatus(WarnInfoVO vo, String startDate, String endDate) {
        if (vo.getWarnExceptionStatus() != null) {
            // 取数异常,或者告警异常
            WarnResultVo ret = WarnResultVo.of(vo);
            ret.setWarnStatus(WarnRunStatusEnum.EXECUTE_EXCEPTION.name());
            return ret;
        }
//         日期包含今天
        if (includeToday(startDate, endDate)) {
            // 今天0点0分0秒
            long today = DateUtil.beginOfDay(DateUtil.date()).getTime();
            if (!Objects.equals(vo.getWarnStatus(), 1) || vo.getWarnSyncLastTimestamp() == null || vo.getWarnSyncLastTimestamp() <= today) {
                // 禁用、或今天未运行
                WarnResultVo ret = WarnResultVo.of(vo);
                ret.setWarnStatus(WarnRunStatusEnum.WAIT.name());
                return ret;
            }
        }
        var dataSize = 0;
        if (includeToday(startDate, endDate)) {
            dataSize += super.lambdaQuery().eq(OpsWarnDataToday::getWarnId, vo.getId()).count();
        }

        if (includeHistory(startDate, endDate)) {
            dataSize += SpringUtil.getBean(OpsWarnDataHistoryService.class).lambdaQuery()
                    .select(OpsWarnDataHistory::getWarnContent)
                    .eq(OpsWarnDataHistory::getWarnId, vo.getId())
                    .between(OpsWarnDataHistory::getWarnDate, startDate, endDate)
                    .count();
        }
        WarnResultVo ret = WarnResultVo.of(vo);
        ret.setDictIds(vo.getDictIds());
        ret.setWarnCount(dataSize);
        if (dataSize > 0) {
            ret.setWarnStatus(WarnRunStatusEnum.ERROR.name());
        } else {
            ret.setWarnStatus(WarnRunStatusEnum.NORMAL.name());
        }
        return ret;
    }

    private boolean includeHistory(String startDate, String endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        String today = DateUtil.today();
        return 0 < today.compareTo(startDate) || 0 < today.compareTo(endDate);
    }


    private boolean includeToday(String startDate, String endDate) {
        // 判断今天包含在两个日期之间
        return startDate == null || endDate == null
                || DateUtil.isIn(DateUtil.parse(DateUtil.today()), DateUtil.parse(startDate), DateUtil.parse(endDate));

    }

    /**
     * 数据匹配规则
     *
     * @param config 配置
     * @param data   数据
     * @return 数据符合规则返回true，否则返回false
     */
    private static boolean match(WarnInfoVO config, String data) {
        if (WarnAlertRuleTypeEnum.ALL.is(config.getWarnAlertRuleType())) {
            return true;
        }
        if (WarnAlertRuleTypeEnum.EXPRESSION_MATCH.is(config.getWarnAlertRuleType())) {
            String exp = config.getWarnAlertExpression();
            if (StrUtil.isBlank(exp)) {
                log.debug("express is empty.");
                return false;
            }
            DefaultContext<String, Object> context = new DefaultContext<>();
            context.putAll(JsonUtils.toMap(data));
            boolean match = RuleUtil.execute(exp, context);
            log.debug("match result: {} express: {}, data= {} ", match, exp, data);
            return match;
        }
        return false;
    }

    /**
     * 替换表头
     *
     * @param metricId 指标id
     * @param dataList 转换完的数据列表
     */
    private static void replaceHead(String metricId, LinkedList<Map<String, Object>> dataList) {
        if (dataList.isEmpty()) {
            return;
        }
        List<SqlColumnInfoVO> aliasMap = SpringUtil.getBean(OpsMetricResultConfigService.class).tableHead(metricId);
        if (aliasMap.isEmpty()) {
            return;
        }
        for (var data : dataList) {
            for (var alias : aliasMap) {
                String fieldName = alias.getColumnName();
                if (data.containsKey(fieldName)) {
                    data.put(alias.getColumnComment(), data.get(fieldName));
                    data.remove(fieldName);
                }
            }
        }
    }

    /**
     * 查找字典，类型不传时返回所有字典
     *
     * @param dictTypes 字典类型，可选参数
     * @return 字典map, dictId -> dictType
     */
    private static Map<Long, String> findDictMap(List<String> dictTypes) {
        Set<OpsSysDictItem> dictItems = new HashSet<>(0);
        R<List<OpsSysDictItem>> resp = SpringUtil.getBean(SystemFeignService.class).dictListByTypes(dictTypes);
        if (resp.isSuccess()) {
            dictItems.addAll(resp.getData());
        }
        return dictItems.stream()
                .collect(Collectors.toMap(OpsSysDictItem::getId, OpsSysDictItem::getDictType, (a, b) -> b));
    }

//    public static void main(String[] args) {
////        String data = "{\"name\":\"陈伟明\",\"age\":18,\"sex\":\"男\"}";
//        String data = "{\"name\":\"许飞飞\",\"age\":18,\"sex\":\"女\"}";
//        WarnInfoVO config = new WarnInfoVO();
//        config.setWarnAlertRuleType(WarnAlertRuleTypeEnum.EXPRESSION_MATCH.getCode());
//        config.setWarnAlertExpression("age >= 18 && name == '陈伟明' ");
//        System.out.println("match : " + match(config, data));
//    }
}