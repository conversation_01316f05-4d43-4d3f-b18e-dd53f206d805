package sdata.ops.indicator.handler.source.config;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class DBCommonWriteHandler {

    public enum WriteMode {
        SIMPLE_INSERT,  // 简单插入(默认)
        DELETE_THEN_INSERT,  // 先删后写
        INSERT_UPDATE,  // 更新写入
        INCREMENTAL     // 增量写入
    }

    protected int processWriteOperation(JdbcTemplate template, JSONObject params, WriteMode mode) {
        switch (mode) {
            case DELETE_THEN_INSERT:
                return deleteThenInsert(template, params);
            case INSERT_UPDATE:
                // return insertOrUpdate(template, params);
            case INCREMENTAL:
                return incrementalWrite(template, params);
            default:
                return writeDataWithTransaction(template, params, params.getInt("batchSize", 1000));
        }
    }

    /**
     * 安全的读数据方法
     *
     * @param template JdbcTemplate
     * @param params   参数
     * @return 查询结果
     */
    protected Object readData(JdbcTemplate template, JSONObject params) {
        String sql = params.getStr("sql");
        JSONObject sqlParams = params.getJSONObject("params");

        // 使用命名参数防止SQL注入
        NamedParameterJdbcTemplate namedTemplate = new NamedParameterJdbcTemplate(template);
        SqlParameterSource paramSource = new MapSqlParameterSource(sqlParams);

        // 根据返回类型进行映射
        String resultType = params.getStr("resultType", "map");

        if (sql.toLowerCase().startsWith("select")) {
            switch (resultType.toLowerCase()) {
                case "map":
                case "array":
                    return namedTemplate.query(sql, paramSource, (ResultSet rs, int rowNum) -> {
                        Map<String, Object> resultMap = new LinkedHashMap<>();
                        ResultSetMetaData metaData = rs.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        for (int i = 1; i <= columnCount; i++) {
                            resultMap.put(metaData.getColumnLabel(i), rs.getObject(i));
                        }
                        return resultMap;
                    });
                case "object":
                    return namedTemplate.queryForObject(sql, paramSource, (ResultSet rs, int rowNum) -> {
                        Map<String, Object> resultMap = new LinkedHashMap<>();
                        ResultSetMetaData metaData = rs.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        for (int i = 1; i <= columnCount; i++) {
                            resultMap.put(metaData.getColumnLabel(i), rs.getObject(i));
                        }
                        return resultMap;
                    });
                default:
                    throw new IllegalArgumentException("不支持的返回类型: " + resultType);
            }
        } else {
            return namedTemplate.update(sql, paramSource);
        }
    }



    /**
     * 增量写入(只写入不存在的数据)
     *
     * @param template JdbcTemplate
     * @param params   参数(必须包含uniqueKeys用于判断记录是否已存在)
     * @return 成功插入的行数
     */
    private int incrementalWrite(JdbcTemplate template, JSONObject params) {
        String tableName = params.getStr("table");
        JSONArray insertData = params.getJSONArray("data");
        JSONArray uniqueKeys = params.getJSONArray("uniqueKeys"); // 唯一性判断字段
        JSONArray fields = params.getJSONArray("fields");
        // 1. 筛选出需要插入的数据
        List<JSONArray> filteredData = filterExistingRecords(template, tableName, insertData, uniqueKeys,fields);

        if (filteredData.isEmpty()) {
            return 0;
        }

        // 2. 执行批量插入
        return writeDataWithTransaction(template,
                new JSONObject().set("sql", params.getStr("sql")).set("data",filteredData), 1000);
    }

    // 过滤已存在记录
    private List<JSONArray> filterExistingRecords(JdbcTemplate template, String tableName,
                                                   JSONArray insertData, JSONArray uniqueKeys,JSONArray fields) {
        List<JSONArray> filtered = new ArrayList<>();

        NamedParameterJdbcTemplate namedTemplate = new NamedParameterJdbcTemplate(template);

        // 构建检查SQL
        StringBuilder checkSql = new StringBuilder("SELECT COUNT(1) FROM ")
                .append(tableName)
                .append(" WHERE ");

        for (int i = 0; i < uniqueKeys.size(); i++) {
            if (i > 0) checkSql.append(" AND ");
            checkSql.append(uniqueKeys.getStr(i)).append(" = :").append(uniqueKeys.getStr(i));
        }

        // 检查每条记录是否已存在
        for (int i = 0; i < insertData.size(); i++) {
            JSONArray record = insertData.getJSONArray(i);
            MapSqlParameterSource params = new MapSqlParameterSource();

            for (Object key : uniqueKeys) {
                int index=fields.indexOf(key);
                params.addValue((String) key, record.get(index));
            }

            int count = namedTemplate.queryForObject(checkSql.toString(), params, Integer.class);
            if (count == 0) {
                filtered.add(record);
            }
        }

        return filtered;
    }

    /**
     * 高效批量写入数据（事务管理版本）
     *
     * @param template  JdbcTemplate
     * @param params    参数
     * @param batchSize 每批次处理的数量（可选，默认1000）
     * @return 影响行数
     */
    private int writeDataWithTransaction(JdbcTemplate template, JSONObject params, Integer batchSize) {
        // 参数校验
        if (template == null || params == null || !params.containsKey("sql") || !params.containsKey("data")) {
            throw new IllegalArgumentException("参数不完整");
        }
        DataSourceTransactionManager transactionManager =
                new DataSourceTransactionManager(Objects.requireNonNull(template.getDataSource()));
        TransactionTemplate transactionTemplate =
                new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        String sql = params.getStr("sql");
        JSONArray data = params.getJSONArray("data");
        int defaultBatchSize = batchSize != null ? batchSize : 1000; // 默认批次大小

        // 使用事务模板执行
        return transactionTemplate.execute(status -> {
            try {
                return template.execute((Connection con) -> {
                    try (PreparedStatement ps = con.prepareStatement(sql)) {
                        int totalCount = 0;
                        boolean originalAutoCommit = con.getAutoCommit();
                        if (originalAutoCommit) {
                            throw new IllegalStateException("自动提交未关闭，事务控制失效！");
                        }

                        // 分批处理数据
                        for (int i = 0; i < data.size(); i++) {
                            JSONArray row = data.getJSONArray(i);

                            // 填充预编译语句参数
                            AtomicInteger paramIndex = new AtomicInteger(0);
                            row.forEach(value -> {
                                try {
                                    paramIndex.incrementAndGet();
                                    ps.setObject(paramIndex.get(), value);
                                } catch (SQLException e) {
                                    throw new RuntimeException("设置SQL参数失败", e);
                                }
                            });

                            ps.addBatch();

                            // 达到批次大小或最后一条记录时执行批量操作
                            if ((i + 1) % defaultBatchSize == 0 || i == data.size() - 1) {
                                int[] batchResult = ps.executeBatch();
                                for (int count : batchResult) {
                                    if (count == Statement.EXECUTE_FAILED) {
                                        throw new SQLException("批量执行失败，行号: " + (i + 1 - batchResult.length + 1));
                                    }
                                    totalCount += count;
                                }
                                ps.clearBatch(); // 清除已执行的批次
                            }
                        }
                        return totalCount;
                    } catch (SQLException e) {
                        status.setRollbackOnly(); // 标记事务回滚
                        throw new RuntimeException("批量写入失败", e);
                    }
                });
            } catch (Exception e) {
                status.setRollbackOnly(); // 标记事务回滚
                throw e;
            }
        });
    }


    /**
     * 使用优化的批量插入方式
     *
     * @param template  JdbcTemplate
     * @param sql       SQL语句
     * @param batchArgs 批量参数
     * @return 影响行数数组
     */
    private int[] batchInsert(JdbcTemplate template, String sql, List<Object[]> batchArgs) {
        return template.batchUpdate(sql, batchArgs);
    }

    /**
     * 先执行删除再执行插入操作
     *
     * @param template JdbcTemplate
     * @param params   参数(必须包含deleteCondition和insertData)
     * @return 影响行数
     */
    private int deleteThenInsert(JdbcTemplate template, JSONObject params) {
        String tableName = params.getStr("table");
        String deleteCondition = params.getStr("deleteCondition");
        JSONArray insertData = params.getJSONArray("data");

        // 共享事务管理器（确保同一事务）
        DataSourceTransactionManager transactionManager =
                new DataSourceTransactionManager(Objects.requireNonNull(template.getDataSource()));
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);

        return transactionTemplate.execute(status -> {
            try {
                // 1. 执行删除
                int deleted = 0;
                if (deleteCondition != null && !deleteCondition.isEmpty()) {
                    StringBuilder deleteSql = new StringBuilder("DELETE FROM ").append(tableName).append(" WHERE ");
                    List<Object> paramValues = new ArrayList<>();
                    deleteSql.append(deleteCondition);
                    deleted = template.update(deleteSql.toString(), paramValues.toArray(new Object[0])); // 修复警告
                }

                // 2. 执行批量插入（复用事务）
                int inserted = writeDataWithTransaction(template,
                        new JSONObject()
                                .set("sql", buildInsertSql(tableName, params.getBeanList("fields",String.class)))
                                .set("data", insertData),
                        null);

                return deleted + inserted;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("先删后写操作失败", e);
            }
        });
    }


    // 构建INSERT SQL工具方法
    private String buildInsertSql(String tableName, List<String> sampleData) {
        StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
        StringBuilder placeholders = new StringBuilder("VALUES (");

        int i = 0;
        for (String column : sampleData) {
            if (i++ > 0) {
                sql.append(", ");
                placeholders.append(", ");
            }
            sql.append(column);
            placeholders.append("?");
        }

        return sql.append(") ").append(placeholders).append(")").toString();
    }
}
