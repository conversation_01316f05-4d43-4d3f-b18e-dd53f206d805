package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.WarnInfoQueryDto;
import sdata.ops.base.indicator.model.entity.OpsWarnDataToday;
import sdata.ops.base.indicator.model.vo.WarnBenchVo;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.base.indicator.model.vo.WarnResultVo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【ops_warn_data_today(当前预警数据表)】的数据库操作Service
* @createDate 2025-08-16
*/
public interface OpsWarnDataTodayService extends IService<OpsWarnDataToday> {

    /**
     * 保存预警数据
     *
     * @param dataList 告警数据
     * @param vo     监控单元信息
     */
    void saveData(List<String> dataList, WarnInfoVO vo);

    /**
     * 迁移当天数据到历史
     */
    void transferTodayToHistory();

    /**
     * 工作台列表
     *
     * @return 监控数据
     */
    List<WarnBenchVo> workbenchList(WarnInfoQueryDto dto);

    /**
     * 监控概览列表
     */
    List<WarnResultVo> centerList(WarnInfoQueryDto dto);

    /**
     * 告警详情
     */
    List<Map<String, Object>> detail(String id, String startDate, String endDate);

}