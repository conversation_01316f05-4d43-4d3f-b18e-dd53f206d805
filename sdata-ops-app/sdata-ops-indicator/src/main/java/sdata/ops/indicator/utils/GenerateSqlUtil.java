package sdata.ops.indicator.utils;

import org.apache.commons.lang3.StringUtils;
import sdata.ops.base.indicator.model.entity.OpsModelTableColumn;

import java.util.ArrayList;
import java.util.List;

public class GenerateSqlUtil {
    /**
     * 生成创建表的SQL语句(优化版)，返回SQL语句列表
     *
     * @param columns 字段列表
     * @param tableName 表名
     * @param tableComment 表注释
     * @param subSourceType 数据库类型(mysql/oracle/dm)
     * @return SQL语句列表
     */
    public List<String> generateCreateTableSQLs(List<OpsModelTableColumn> columns, String tableName,
                                                String tableComment, String subSourceType) {
        if (columns == null || columns.isEmpty()) {
            throw new IllegalArgumentException("至少需要一个字段来创建表");
        }
        List<String> sqls = new ArrayList<>();
        StringBuilder createTableSQL = new StringBuilder();

        // 创建表结构SQL
        createTableSQL.append("CREATE TABLE ").append(formatTableName(tableName, subSourceType)).append(" (\n");
        // 主键字段集合
        List<String> primaryKeys = new ArrayList<>();
        // 构建字段定义
        for (int i = 0; i < columns.size(); i++) {
            OpsModelTableColumn column = columns.get(i);
            createTableSQL.append("    ").append(formatColumnName(column.getColumnName(), subSourceType)).append(" ")
                    .append(getColumnTypeWithDefault(column, subSourceType));
            // 非空约束
            if (column.getIsNullable() != null && column.getIsNullable() == 0) {
                createTableSQL.append(" NOT NULL");
            }
            // 默认值
            if (StringUtils.isNotBlank(column.getDefaultValue())) {
                createTableSQL.append(" DEFAULT ").append(formatDefaultValue(column, subSourceType));
            }
            // 自增(根据不同数据库特殊处理)
            if (column.getIsAutoIncrement() != null && column.getIsAutoIncrement() == 1) {
                createTableSQL.append(getAutoIncrementClause(subSourceType));
            }
            // 记录主键
            if (column.getIsPrimary() != null && column.getIsPrimary() == 1) {
                primaryKeys.add(formatColumnName(column.getColumnName(), subSourceType));
            }
            if (i < columns.size() - 1 || !primaryKeys.isEmpty()) {
                createTableSQL.append(",");
            }
            createTableSQL.append("\n");
        }
        // 添加主键约束
        if (!primaryKeys.isEmpty()) {
            createTableSQL.append("    ").append(getPrimaryKeyClause(primaryKeys, subSourceType))
                    .append("\n");
        }
        createTableSQL.append(")");
        sqls.add(createTableSQL.toString());
        // 表注释(根据不同数据库处理)
        if (StringUtils.isNotBlank(tableComment)) {
            sqls.add(getTableCommentClause(tableComment, subSourceType, tableName));
        }
        // 字段注释(根据不同数据库处理)
        for (OpsModelTableColumn column : columns) {
            if (StringUtils.isNotBlank(column.getDescription()) &&
                    ("oracle".equals(subSourceType) || "dm".equals(subSourceType))) {
                sqls.add(getColumnCommentStatement(column, subSourceType, tableName));
            }
        }
        return sqls;
    }

    /**
     * 获取带默认长度的列类型
     */
    private String getColumnTypeWithDefault(OpsModelTableColumn column, String dbType) {
        String columnType = column.getColumnType().toLowerCase();
        Integer length = column.getFieldLength();

        // 处理JSON类型转换为BLOB
        if ("json".equals(columnType) && ("oracle".equals(dbType) || "dm".equals(dbType))) {
            return "BLOB";
        }

        // 处理不同类型和不同数据库的默认长度
        switch (columnType) {
            case "int":
                if ("dm".equals(dbType)) {
                    return "INT"; // DM不需要指定长度
                }
                if (length == null) {
                    return "oracle".equals(dbType) ? "NUMBER(10)" : "INT";
                }
                return "oracle".equals(dbType) ? "NUMBER(" + length + ")" : "INT(" + length + ")";

            case "bigint":
                if ("dm".equals(dbType)) {
                    return "BIGINT"; // DM不需要指定长度
                }
                if (length == null) {
                    return "oracle".equals(dbType) ? "NUMBER(19)" : "BIGINT";
                }
                return "oracle".equals(dbType) ? "NUMBER(" + length + ")" : "BIGINT(" + length + ")";

            case "varchar":
                if (length == null) {
                    return "oracle".equals(dbType) ? "VARCHAR2(4000)" :
                            "dm".equals(dbType) ? "VARCHAR(32672)" : "VARCHAR(255)";
                }
                return "oracle".equals(dbType) ? "VARCHAR2(" + length + ")" : "VARCHAR(" + length + ")";

            case "char":
                if (length == null) {
                    return "CHAR(1)";
                }
                return "CHAR(" + length + ")";

            case "number":
            case "numeric":
                if (length == null) {
                    return "oracle".equals(dbType) ? "NUMBER" : "DECIMAL(10,0)";
                }
                return "oracle".equals(dbType) ? "NUMBER(" + length + ")" : "DECIMAL(" + length + ",0)";

            case "float":
                if (length == null) {
                    return "oracle".equals(dbType) ? "BINARY_FLOAT" : "FLOAT";
                }
                return "FLOAT(" + length + ")";

            case "double":
                if (length == null) {
                    return "oracle".equals(dbType) ? "BINARY_DOUBLE" : "DOUBLE";
                }
                return "DOUBLE(" + length + ")";

            default:
                return column.getColumnType(); // 其他类型原样返回
        }
    }

    /**
     * 获取自增子句
     */
    private String getAutoIncrementClause(String dbType) {
        switch (dbType) {
            case "mysql": return " AUTO_INCREMENT";
            case "oracle": return " ";
            case "dm": return " IDENTITY(1,1)"; // 修改为IDENTITY(1,1)
            default: return "";
        }
    }

    /**
     * 获取主键约束子句
     */
    private String getPrimaryKeyClause(List<String> primaryKeys, String dbType) {
        if ("oracle".equals(dbType)) {
            return "CONSTRAINT PK_" + primaryKeys.get(0).toUpperCase() +
                    " PRIMARY KEY (" + String.join(", ", primaryKeys) + ")";
        }
        return "PRIMARY KEY (" + String.join(", ", primaryKeys) + ")";
    }

    /**
     * 获取列注释语句(Oracle/DM专用)
     */
    private String getColumnCommentStatement(OpsModelTableColumn column, String dbType, String tableName) {
        return "COMMENT ON COLUMN " + formatTableName(tableName, dbType) + "." +
                formatColumnName(column.getColumnName(), dbType) +
                " IS '" + column.getDescription() + "'";
    }

    /**
     * 获取表注释子句(修改为单独语句)
     */
    private String getTableCommentClause(String comment, String dbType, String tableName) {
        if ("oracle".equals(dbType) || "dm".equals(dbType)) {
            return "COMMENT ON TABLE " + formatTableName(tableName, dbType) +
                    " IS '" + comment + "'";
        }
        return "ALTER TABLE " + formatTableName(tableName, dbType) +
                " COMMENT='" + comment + "'";
    }
    /**
     * 格式化表名(根据数据库类型)
     */
    private String formatTableName(String tableName, String dbType) {
        if ("oracle".equals(dbType) || "dm".equals(dbType)) {
            return tableName.toUpperCase();
        }
        return tableName;
    }

    /**
     * 格式化列名(根据数据库类型)
     */
    private String formatColumnName(String columnName, String dbType) {
        if ("oracle".equals(dbType) || "dm".equals(dbType)) {
            return columnName.toUpperCase();
        }
        return columnName;
    }

    /**
     * 格式化默认值
     */
    private String formatDefaultValue(OpsModelTableColumn column, String dbType) {
        String defaultValue = column.getDefaultValue();
        String columnType = column.getColumnType().toLowerCase();

        // 字符串类型和日期类型需要加引号
        if (columnType.contains("char") || columnType.contains("text") ||
                columnType.contains("date") || columnType.contains("time") || "json".equals(columnType)) {
            return "'" + defaultValue + "'";
        }

        // Oracle的特殊处理，如SYSDATE
        if ("oracle".equals(dbType) && "SYSDATE".equalsIgnoreCase(defaultValue)) {
            return "SYSDATE";
        }

        return defaultValue;
    }
}
