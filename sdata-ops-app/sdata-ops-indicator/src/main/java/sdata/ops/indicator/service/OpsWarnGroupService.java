package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.WarnGroupListVO;
import sdata.ops.base.indicator.model.entity.OpsWarnGroup;
import sdata.ops.common.api.R;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_warn_group(预警分类表)】的数据库操作Service
* @createDate 2025-08-16
*/
public interface OpsWarnGroupService extends IService<OpsWarnGroup> {
    /**
     * 分组树
     */
    List<WarnGroupListVO> tree();

    /**
     * 删除分组
     *
     * @param id 分组id
     * @return
     */
    R<Object> deleteGroup(String id);
}