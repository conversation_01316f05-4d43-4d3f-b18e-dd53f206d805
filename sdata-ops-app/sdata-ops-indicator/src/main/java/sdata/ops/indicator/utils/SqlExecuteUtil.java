package sdata.ops.indicator.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONObject;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/3/3 11:21
 */
public class SqlExecuteUtil {

    private final static String start = "<xml>", end = "</xml>";



    /**
     * SQL变量替换
     *
     * @param sql      SQL语句
     * @param paramObj 参数值
     * @return
     * @throws Exception
     */
    public static String parse(String sql, JSONObject paramObj) throws Exception {
        // 解析为XML
        String xml = buildXML(sql);
        // 参数替换
        for (Map.Entry<String, Object> keyValue : paramObj.entrySet()) {
            String key = keyValue.getKey();
            Object value = keyValue.getValue();
            if (null != value && !StrUtil.isEmptyIfStr(value.toString())) {
                xml = xml.replace("${" + key + "}", value.toString().trim());
            }
        }
        StringJoiner joiner = new StringJoiner(" ");
        // XML解析
        Document document = XmlUtil.parseXml(xml);
        Node xmlChild = document.getFirstChild();
        NodeList childNodeList = xmlChild.getChildNodes();
        parseNodeList(childNodeList, joiner, paramObj, new ArrayList<>());
        return joiner.toString();
    }

    /**
     * SQL变量替换,简单根据占位符与入参对等替换
     * @param sql      SQL语句
     * @param paramObj 参数值
     * @return str
     */
    public static String simpleParse(String sql, JSONObject paramObj) {
        // 参数替换
        for (Map.Entry<String, Object> keyValue : paramObj.entrySet()) {
            String key = keyValue.getKey();
            Object value = keyValue.getValue();
            if (null != value && !StrUtil.isEmptyIfStr(value.toString())) {
                sql = sql.replace("${" + key + "}", value.toString().trim());
            }
        }
        return sql;
    }



    /**
     * 递归解析
     *
     * @param childNodeList 节点列表
     * @param joiner        SQL拼接
     * @param paramObj      参数对象
     * @param nameList      标签name列表
     * @return
     */
    public static void parseNodeList(NodeList childNodeList, StringJoiner joiner, JSONObject paramObj, List<String> nameList) throws Exception {
        for (int i = 0; i < childNodeList.getLength(); i++) {
            // 获取节点
            Node node = childNodeList.item(i);
            if (node.getNodeType() == 3) {
                // 类型是文本,直接拼接
                joiner.add(node.getNodeValue());
                continue;
            }
            if (node.getNodeType() == 1) {
                String nodeName = node.getNodeName();
                if (nodeName.equals("where")) {
                    // 校验where标签是否跳过
                    boolean skip = validateName(node, paramObj, nameList);
                    if (skip) {
                        // 是否跳过处理
                        continue;
                    }
                }
                if (nodeName.equals("if")) {
                    // 校验if标签是否跳过
                    boolean skip = validateTest(node);
                    if (!skip) {
                        // 是否跳过处理
                        continue;
                    }
                }
                NodeList newChildNodeList = node.getChildNodes();
                if (newChildNodeList.getLength() > 0) {
                    // 如果存在子节点，遍历获取内容
                    parseNodeList(newChildNodeList, joiner, paramObj, nameList);
                }
            }
        }
    }


    /**
     * 构建标准XML
     *
     * @param sql
     * @return
     */
    public static String buildXML(String sql) {
        sql = sql.trim();
        // xml语句替换
        sql = sql.replace("<", "&lt;");
        sql = sql.replace("&lt;where", "<where").replace("&lt;/where", "</where");
        sql = sql.replace("&lt;if", "<if").replace("&lt;/if", "</if");
        return start + sql + end;
    }


    public static boolean validateName(Node node, JSONObject param, List<String> nameList) throws Exception {
        Element element = (Element) node;
        String name = element.getAttribute("name");
        if (StrUtil.isEmptyIfStr(name)) {
            throw new Exception("标签name不能为空");
        }
        if (nameList.contains(name)) {
            throw new Exception("标签name值唯一");
        }
        nameList.add(name);
        // 判断参数是否必填
        boolean require = isRequire(element);
        Object value = param.get(name);
        if (require) {
            // 必填，如果为空，异常
            if (value == null || StrUtil.isEmptyIfStr(value.toString())) {
                throw new Exception("必填参数为空:" + name);
            }
        } else {
            // 非必填，如果为空，过滤
            if (value == null || StrUtil.isEmptyIfStr(value.toString())) {
                return true;
            }
        }
        return false;
    }

    public static boolean validateTest(Node node) throws Exception {
        Element element = (Element) node;
        String test = element.getAttribute("test");
        if (StrUtil.isEmptyIfStr(test)) {
            throw new Exception("标签test不能为空");
        }
        return compare(test);
    }


    /**
     * 获取必填项字段
     *
     * @param element
     * @return
     */
    public static boolean isRequire(Element element) {
        try {
            String require = element.getAttribute("require");
            return Boolean.parseBoolean(require);
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean compare(String param) throws Exception {
        if (param.contains("${")) {
            throw new Exception("标签if解析失败:" + param);
        }
        if (param.contains("||")) {
            String[] split = param.split("[|][|]");
            for (String p : split) {
                boolean b = compareTo(p);
                if (b) {
                    return true;
                }
            }
            return false;
        } else if (param.contains("&&")) {
            String[] split = param.split("[&][&]");
            for (String p : split) {
                boolean b = compareTo(p);
                if (!b) {
                    return false;
                }
            }
            return true;
        } else {
            return compareTo(param);
        }
    }

    public static boolean compareTo(String param) throws Exception {
        try {
            if (param.contains("!=")) {
                // 不等于
                String[] split = param.split("!=");
                return !split[0].trim().equals(split[1].trim());
            } else if (param.contains("==")) {
                // 等于
                String[] split = param.split("==");
                return split[0].trim().equals(split[1].trim());
            } else if (param.contains("&gt;=")) {
                // 大于等于
                String[] split = param.split("&gt;=");
                return (split[0].trim().compareTo(split[1].trim())) >= 0;
            } else if (param.contains("&gt;")) {
                // 大于
                String[] split = param.split("&gt;");
                return (split[0].trim().compareTo(split[1].trim())) > 0;
            } else if (param.contains("&lt;=")) {
                // 小于等于
                String[] split = param.split("&lt;=");
                return (split[0].trim().compareTo(split[1].trim())) <= 0;
            } else if (param.contains("&lt;")) {
                String[] split = param.split("&lt;");
                return (split[0].trim().compareTo(split[1].trim())) < 0;
            }
        } catch (Exception e) {
            throw new Exception("标签if解析失败:" + param);
        }
        return false;
    }
}
