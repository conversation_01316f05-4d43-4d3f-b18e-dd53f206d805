package sdata.ops.indicator.handler.flow.node;

import cn.hutool.json.JSONUtil;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import reactor.util.function.Tuple2;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MailReceiveNode extends BaseNode {

    private final JSONObject data;

    public MailReceiveNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        //输出容器
        Map<String, Object> res = new HashMap<>();
        JSONObject nodeConfig = data.getJSONObject("config");
        //获取json内容
        String sourceId = nodeConfig.getString("sourceId");
        //获取执行结果
        DataSourceOperationHandler operationHandler= SpringBeanUtil.getBean(DataSourceOperationHandler.class);
        Object callResult=operationHandler.invokeExecute(sourceId,null);
        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            for (Parameter def : outputDefs) {
                res.put(def.getName(), JSONUtil.parse(callResult));
            }
        }
        return res;
    }

    /**
     * 对json中的 动态参数进行替换
     *
     * @param json 原始json
     * @param map  引入参数
     */
    private String replacerFactVal(String json, Map<String, Object> map) {

        Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}");
        Matcher matcher = pattern.matcher(json);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String key = matcher.group(1);
            if (!map.containsKey(key)) {
                throw new IllegalArgumentException("未定义的占位符: ${" + key + "}");
            }
            matcher.appendReplacement(result, Matcher.quoteReplacement(map.get(key).toString()));
        }
        matcher.appendTail(result);
        return result.toString();
    }


}
