package sdata.ops.indicator.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.base.indicator.model.dto.WarnInfoQueryDto;
import sdata.ops.base.indicator.model.vo.WarnBenchVo;
import sdata.ops.base.indicator.model.vo.WarnResultVo;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.OpsWarnDataTodayService;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 当前预警数据表控制器
* @createDate 2025-08-16
*/
@RestController
@RequestMapping("/opsWarnDataToday")
@RequiredArgsConstructor
public class OpsWarnDataTodayController extends PageCustomController {

    private final OpsWarnDataTodayService opsWarnDataTodayService;

    /**
     * 工作台预警列表
     * @return 告警列表
     */
    @ControllerAuditLog(value = "工作台告警列表", operateType = OperateType.QUERY, moduleName = ModuleName.WARN)
    @GetMapping("/workbench-list")
    public R<List<WarnBenchVo>> workbenchList(WarnInfoQueryDto dto) {
        var vos = opsWarnDataTodayService.workbenchList(dto);
        return R.data(vos);
    }

    /**
     * 监控中心列表
     */
    @ControllerAuditLog(value = "监控中心列表", operateType = OperateType.QUERY, moduleName = ModuleName.WARN)
    @GetMapping("/center-list")
    public R<List<WarnResultVo>> centerList(WarnInfoQueryDto dto) {
        var vos = opsWarnDataTodayService.centerList(dto);
        return R.data(vos);
    }

    /**
     * 监控概览查看详情
     *
     * @param id        监控单元id
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 明细数据列表
     */
    @ControllerAuditLog(value = "监控中心详情", operateType = OperateType.QUERY, moduleName = ModuleName.WARN)
    @GetMapping("/detail")
    public R<List<Map<String, Object>>> detail(@RequestParam String id, String startDate, String endDate) {
        return R.data(opsWarnDataTodayService.detail(id, startDate, endDate));
    }

    /**
     * 迁移数据到历史表
     */
    @ControllerAuditLog(value = "监控中心-数据迁移", operateType = OperateType.EXECUTE, moduleName = ModuleName.WARN)
    @GetMapping("/_transfer")
    public R<Void> _transfer() {
        opsWarnDataTodayService.transferTodayToHistory();
        return R.status(true);
    }
}