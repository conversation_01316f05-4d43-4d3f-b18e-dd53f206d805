package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.DDLResult;
import sdata.ops.base.indicator.model.entity.OpsModelTableChangeLog;
import sdata.ops.base.indicator.model.entity.OpsModelTableIndex;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_model_table_change_log(数据表变更记录表)】的数据库操作Service
* @createDate 2025-08-01 14:15:54
*/
public interface OpsModelTableChangeLogService extends IService<OpsModelTableChangeLog> {

    void saveLogByColumnChange(DDLResult ddlResult, OpsModelTableInfo opsInfo);

    void saveLogByIndexChange(OpsModelTableInfo info, List<OpsModelTableIndex> indexesToAdd, List<OpsModelTableIndex> indexesToUpdate, List<OpsModelTableIndex> indexesToRemove, List<OpsModelTableIndex> oldIndexs);

    void saveLogByDeleted(OpsModelTableInfo info);
}
