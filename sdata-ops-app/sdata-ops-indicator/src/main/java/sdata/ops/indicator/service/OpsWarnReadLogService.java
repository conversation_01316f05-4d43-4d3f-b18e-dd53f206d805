package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsWarnReadLog;

public interface OpsWarnReadLogService extends IService<OpsWarnReadLog> {
    /**
     * 删除已读记录
     *
     * @param warnId 监控单元id
     * @param userId 用户id
     */
    void removeOld(String warnId, String userId);

    /**
     * 新增已读记录
     *
     * @param warnId 监控单元id
     * @param userId 用户id
     */
    void addNew(String warnId, String userId);
}
