package sdata.ops.indicator.handler.flow.node;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import sdata.ops.base.indicator.model.dto.WriteNodePrimary;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DataBaseWriteNode extends BaseNode {

    private static final long serialVersionUID = 1L;


    private final JSONObject data;

    public DataBaseWriteNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        Map<String, Object> res = new HashMap<>();
        log.info("执行-写入-start--");

        Map<String, Object> map = chain.getParameterValues(this);
        JSONObject nodeConfig = data.getJSONObject("config");

        String sourceId = nodeConfig.getStr("sourceId");
        DataSourceOperationHandler operationHandler = SpringBeanUtil.getBean(DataSourceOperationHandler.class);

        String tableName = nodeConfig.getStr("table");
        List<JSONObject> modelKey = nodeConfig.getBeanList("keywords", JSONObject.class);
        WriteNodePrimary primary = new WriteNodePrimary();
        String insertSql = generateInsertSql(tableName, modelKey, primary);

        String dataKey = nodeConfig.getStr("dataRefKey");
        if (dataKey == null) {
            throw new RuntimeException("数据源写入错误,参数引用值找不到!");
        }

//        String type = String.valueOf(chain.getParameters().stream()
//                .filter(p -> dataKey.equals(p.getName()))
//                .map(Parameter::getDataType)
//                .findFirst()
//                .orElseThrow(() -> new RuntimeException("参数引用值的类型找不到: " + dataKey)));

        JSONArray insertDatas = map.get(dataKey) == null ? new JSONArray() : convertPoly(map.get(dataKey));
        if (insertDatas.isEmpty()) {
            throw new RuntimeException("数据源写入数据为空!");
        }

        JSONArray orgiesData = convertToBatchArray(insertDatas, modelKey, primary);

        JSONObject push = buildPushConfig(insertSql, orgiesData, nodeConfig, map, primary);

        Object callResult = operationHandler.invokeExecute(sourceId, push);
        log.info("数据源执行写入操作-流程id:{} 写入结果:{}", getId(), callResult);

        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            for (Parameter def : outputDefs) {
                res.put(def.getName(), callResult);
            }
        }

        return res;
    }

    private JSONObject buildPushConfig(String insertSql, JSONArray orgiesData, JSONObject nodeConfig, Map<String, Object> map, WriteNodePrimary primary) {
        JSONObject push = new JSONObject();
        push.set("sql", insertSql);
        push.set("data", orgiesData);
        push.set("type", "write");
        push.set("writeModel", nodeConfig.getStr("writeModel"));
        String deleteCondition = nodeConfig.getStr("deleteCondition");
        if (deleteCondition != null && deleteCondition.contains("${")) {
            deleteCondition = replaceDeleteConditionVariables(deleteCondition, map);
        }
        push.set("deleteCondition", deleteCondition);
        push.set("uniqueKeys", nodeConfig.getJSONArray("uniqueKeys"));
        push.set("table", nodeConfig.getStr("table"));
        push.set("fields", primary.getFields());
        return push;
    }


    /**
     * 替换删除条件中的变量占位符
     *
     * @param deleteCondition 删除条件语句
     * @param map             当前节点执行中的参数内容
     * @return 替换后的删除条件语句
     */
    private String replaceDeleteConditionVariables(String deleteCondition, Map<String, Object> map) {
        String result = deleteCondition;

        // 使用正则表达式匹配 ${variable} 格式的占位符
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{([^}]+)}");
        java.util.regex.Matcher matcher = pattern.matcher(deleteCondition);

        // 存储找到的所有变量名
        java.util.List<String> variables = new java.util.ArrayList<>();
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }

        // 替换每个变量
        for (String variable : variables) {
            if (map.containsKey(variable)) {
                Object value = map.get(variable);
                // 只处理字符串和数字类型
                if (value instanceof String) {
                    result = result.replace("${" + variable + "}", value.toString());
                } else if (value instanceof Number) {
                    result = result.replace("${" + variable + "}", value.toString());
                } else if (value instanceof Boolean) {
                    result = result.replace("${" + variable + "}", value.toString());
                }
                // 其他类型不处理，保持原样
            }
        }

        return result;
    }

    private JSONArray convertPoly(Object o) {
        if (o == null) {
            throw new NullPointerException("输入数据不能为空");
        }

        if (o instanceof ArrayList) {
            return new JSONArray(o);
        } else if (o instanceof JSONObject || o instanceof com.alibaba.fastjson2.JSONObject) {
            return new JSONArray().put(o);
        } else {
            throw new ClassCastException("此节点不支持的转换类型: " + o.getClass());
        }
    }


    /**
     * 将JSON对象数组转换为二维数组结构的JSONArray（按指定字段顺序）
     *
     * @param insertDatas 输入数据（JSON对象数组，每个对象包含字段键值对）
     * @param modelKey    SQL字段定义（指定字段顺序和校验规则）
     * @return 二维数组结构的JSONArray，可直接用于JDBC批量操作
     * @throws RuntimeException 如果数据缺失必填字段或格式错误
     */
    public static JSONArray convertToBatchArray(JSONArray insertDatas, List<JSONObject> modelKey, WriteNodePrimary primary) {
        if (insertDatas == null || insertDatas.isEmpty()) {
            throw new RuntimeException("输入数据不能为空");
        }
        if (modelKey == null || modelKey.isEmpty()) {
            throw new RuntimeException("字段定义不能为空");
        }
        JSONArray result = new JSONArray();
        for (Object item : insertDatas) {
            JSONObject itemObj = (JSONObject) item;
            // 1. 校验字段完整性
            for (JSONObject fieldDef : modelKey) {
                String fieldName = fieldDef.getStr("column");
                if (!itemObj.containsKey(fieldName)) {
                    throw new RuntimeException(
                            String.format("数据源字段缺失: %s (当前数据: %s)", fieldName, item)
                    );
                }
            }
            // 2. 按modelKey顺序提取字段值，生成行数组
            JSONArray rowData = new JSONArray();
            for (JSONObject fieldDef : modelKey) {
                String fieldName = fieldDef.getStr("column");
                if (fieldName.equals(primary.getColumn())) {
                    rowData.put(IdWorker.getId());
                } else {
                    rowData.put(itemObj.get(fieldName));
                } // 按顺序放入值
            }
            // 3. 将行数组添加到结果中
            result.put(rowData);
        }
        return result;
    }


    /**
     * 动态生成插入SQL语句
     *
     * @param tableName 表名（需自行防范SQL注入）
     * @param fields    字段配置列表（每个字段包含key和name）
     * @return 安全的预编译SQL语句 例如：INSERT INTO table(f1,f2) VALUES(?,?)
     */
    public static String generateInsertSql(String tableName, List<JSONObject> fields, WriteNodePrimary primary) {
        // 校验参数
        if (tableName == null || tableName.trim().isEmpty() || fields == null || fields.isEmpty()) {
            throw new IllegalArgumentException("表名和字段列表不能为空");
        }
        // 使用StringBuilder更高效
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName).append("(");

        StringBuilder placeholders = new StringBuilder(") VALUES(");

        // 遍历字段列表
        for (int i = 0; i < fields.size(); i++) {
            JSONObject field = fields.get(i);
            String columnName = field.getStr("column");
            Boolean isPrimaryKey = field.getBool("primary", false);
            String primaryType = field.getStr("primaryType");
            //如果该字段为主键且是自增类型，则插入语句不需要该key
            if (isPrimaryKey && primaryType.equals("auto")) {
                continue;
            }
            //如果该字段为主键且是序列 - 则系统生成id --其他默认类型按照获取数据方式执行
            if (isPrimaryKey && primaryType.equals("idGen")) {
                primary.setColumn(columnName);
                primary.setType("idGen");
            }
            // 添加字段名
            sql.append(columnName);
            primary.getFields().add(columnName);
            // 添加占位符
            placeholders.append("?");

            // 不是最后一个字段时添加逗号
            if (i < fields.size() - 1) {
                sql.append(",");
                placeholders.append(",");
            }
        }

        placeholders.append(")");
        sql.append(placeholders);

        return sql.toString();
    }
}
