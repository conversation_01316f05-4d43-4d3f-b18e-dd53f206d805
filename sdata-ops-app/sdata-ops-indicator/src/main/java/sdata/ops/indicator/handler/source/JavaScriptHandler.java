package sdata.ops.indicator.handler.source;


import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Source;
import org.graalvm.polyglot.Value;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.indicator.config.js.ContextCache;
import sdata.ops.indicator.config.js.JsProcessException;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

import java.io.IOException;

@Component("javaScript")
@Slf4j
public class JavaScriptHandler implements ScriptExecutor {

    public static <T> T executeJsFunction(String script, Class<T> returnType, Object... args) {
        Context context = ContextCache.getInstance().getContext();
        try {
            context.eval(Source.newBuilder("js", script, "src.js").build());
            Value function = context.getBindings("js").getMember(CommonConstant.JS_METHOD_NAME);
            Value execute = function.execute(args);
            return execute.as(returnType);
        } catch (IOException e) {
            log.error("js代码编译执行异常:源代码{}", script);
            throw new JsProcessException(e);
        } finally {
            ContextCache.getInstance().returnContext(context);
        }
    }


    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        return executeJsFunction(vo.getScript(), Object.class, params);
    }
}
