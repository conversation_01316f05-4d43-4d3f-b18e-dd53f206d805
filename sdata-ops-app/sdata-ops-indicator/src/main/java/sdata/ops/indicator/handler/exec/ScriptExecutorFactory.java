package sdata.ops.indicator.handler.exec;

import org.springframework.stereotype.Component;

@Component
public class ScriptExecutorFactory {

    private final ScriptExecutorContext context;

    public ScriptExecutorFactory(ScriptExecutorContext context) {
        this.context = context;
    }

    public ScriptExecutor createExecutor(String type) {
        ScriptExecutor executor = context.getExecutor(type);
        if (executor == null) {
            throw new IllegalArgumentException("Unsupported script type: " + type);
        }
        return executor;
    }
}

