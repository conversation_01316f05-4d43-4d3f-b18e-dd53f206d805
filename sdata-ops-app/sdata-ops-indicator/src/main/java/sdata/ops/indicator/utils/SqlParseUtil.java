package sdata.ops.indicator.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.*;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import sdata.ops.base.indicator.model.vo.SqlColumnInfoVO;
import sdata.ops.base.indicator.model.vo.SqlParamInfoVO;
import sdata.ops.indicator.enums.DataSourceType;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/3/3 11:20
 */
public class SqlParseUtil {


    private static final String PLACEHOLDER_REGEX = "\\$\\{(\\w+)}";

    private SqlParseUtil() {

    }

    /**
     * SQL解析参数和字段
     *
     * @param sql              SQL语句
     * @param paramList        参数列表
     * @param columnInfoVoList 字段列表
     * @throws Exception
     */
    public static void parse(String sql, List<SqlParamInfoVO> paramList, List<SqlColumnInfoVO> columnInfoVoList, String dbTypeStr) throws Exception {
        // 解析为XML
        String xml = SqlExecuteUtil.buildXML(sql);
        // XML解析
        Document document = XmlUtil.parseXml(xml);
        Node xmlChild = document.getFirstChild();
        NodeList childNodeList = xmlChild.getChildNodes();

        List<String> nameList = new ArrayList<>(16);
        StringJoiner joiner = new StringJoiner(" ");
        parseNodeList(childNodeList, joiner, paramList, nameList);
        // 获取查询语句
        sql = joiner.toString().trim();
        // 字段解析
        parseColumnInfo(sql, columnInfoVoList, dbTypeStr);
    }


    public static void parseNodeList(NodeList childNodeList, StringJoiner joiner, List<SqlParamInfoVO> paramList, List<String> nameList) throws Exception {
        for (int i = 0; i < childNodeList.getLength(); i++) {
            Node node = childNodeList.item(i);
            if (node.getNodeType() == 3) {
                // 类型是文本
                joiner.add(node.getNodeValue());
            } else if (node.getNodeType() == 1) {
                String nodeName = node.getNodeName();
                if (nodeName.equals("where")) {
                    // 节点类型
                    Element element = (Element) node;
                    String name = element.getAttribute("name");
                    if (StrUtil.isEmptyIfStr(name)) {
                        throw new Exception("标签name不能为空");
                    }
                    if (nameList.contains(name)) {
                        throw new Exception("标签name值唯一");
                    }
                    nameList.add(name);
                    // 封装字段参数信息
                    SqlParamInfoVO paramInfoVo = new SqlParamInfoVO();
                    paramInfoVo.setParamName(name);
                    paramInfoVo.setParamDesc(element.getAttribute("desc"));
                    paramInfoVo.setParamRequire(element.getAttribute("require"));
                    paramInfoVo.setParamRef(element.getAttribute("ref"));
                    paramList.add(paramInfoVo);
                }
                NodeList newChildNodeList = node.getChildNodes();
                if (newChildNodeList.getLength() > 0) {
                    // 如果存在子节点，遍历获取内容
                    parseNodeList(newChildNodeList, joiner, paramList, nameList);
                }
            }
        }
    }


    /**
     * 通过sql解析出返回结果集的信息
     *
     * @param sql
     * @return
     */
    private static List<SqlColumnInfoVO> parseColumnInfo(String sql, List<SqlColumnInfoVO> columnInfoVoList, String dbTypeStr) {
        List<SqlColumnInfoVO> sqlColumnInfoVoList = new ArrayList<>();
        try {
            //获取通过ast对象语法树读取参数的信息
            DbType dbType = DataSourceType.valueOf(dbTypeStr).getDbType();
            List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, dbType);
            SQLStatement sqlStatement = sqlStatements.get(0);
            SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) sqlStatement;
            SQLSelectQuery sqlSelectQuery = sqlSelectStatement.getSelect().getQuery();
            SQLSelectQueryBlock sqlSelectQueryBlock = null;
            if (sqlSelectQuery instanceof SQLSelectQueryBlock) {
                sqlSelectQueryBlock = (SQLSelectQueryBlock) sqlSelectStatement.getSelect().getQuery();
            } else if (sqlSelectQuery instanceof SQLUnionQuery) {
                SQLUnionQuery sqlUnionQuery = (SQLUnionQuery) sqlSelectQuery;
                List<SQLSelectQuery> children = sqlUnionQuery.getChildren();
                sqlSelectQueryBlock = (SQLSelectQueryBlock) children.get(0);
            }
            List<SQLSelectItem> items = sqlSelectQueryBlock.getSelectList();

            String columnStr = sql;
            //1.首先截取到sql的数据集部分
            for (SQLSelectItem item : items) {
                String columnName = String.valueOf(item.getExpr());
                if (columnName.equals("*")) {
                    return sqlColumnInfoVoList;
                }
                String columnAlias = item.getAlias();
                if (columnAlias == null) {
                    columnAlias = "";
                }
                //根据字段去获取对应的注释
                String columnComment = "";
                String columnSeparatorStart = "";
                String columnSeparatorStop = ("\\s*[*]\\s*/\\s*");
                if (columnAlias != null && !columnAlias.isEmpty()) {
                    columnSeparatorStart = "[\\s*]{1,1}".concat(columnAlias).concat("\\s*[,]{0,1}\\s*/\\s*[*]\\s*");
                } else {
                    columnSeparatorStart = "[\\s*]{1,1}".concat(columnName).concat("\\s*[,]{0,1}\\s*/\\s*[*]\\s*");
                }
                String[] commentListOne = columnStr.split(columnSeparatorStart);
                if (commentListOne != null && commentListOne.length > 1) {
                    String[] commentListTwo = commentListOne[1].split(columnSeparatorStop);
                    if (commentListTwo != null && commentListTwo.length >= 1) {
                        columnComment = commentListTwo[0];
                    }
                }
                SqlColumnInfoVO sqlColumnInfoVo = new SqlColumnInfoVO();
                if (StrUtil.isEmptyIfStr(columnAlias)) {
                    String[] split = columnName.split("[.]");
                    sqlColumnInfoVo.setColumnName(split[split.length - 1]);
                } else {
                    sqlColumnInfoVo.setColumnName(columnAlias);
                }
                sqlColumnInfoVo.setColumnComment(columnComment);
                columnInfoVoList.add(sqlColumnInfoVo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sqlColumnInfoVoList;
    }

    public static List<String> extractPlaceholders(String input) {
        List<String> placeholders = new ArrayList<>();
        Pattern pattern = Pattern.compile(PLACEHOLDER_REGEX);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            placeholders.add(placeholder);
        }

        HashSet<String> set = new HashSet<>(placeholders);
        if (set.isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(set);
    }

    public static void extractSqlParameter(String sqlScript, List<SqlParamInfoVO> params) {
        List<String> placeholder = extractPlaceholders(sqlScript);
        if (placeholder.isEmpty()) {
            return;
        }
        for (String keyword : placeholder) {
            SqlParamInfoVO sqlParamInfo = new SqlParamInfoVO();
            sqlParamInfo.setParamName(keyword);
            params.add(sqlParamInfo);
        }
    }

}
