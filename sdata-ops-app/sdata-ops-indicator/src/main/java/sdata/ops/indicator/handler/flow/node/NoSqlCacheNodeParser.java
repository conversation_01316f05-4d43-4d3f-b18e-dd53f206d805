package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class NoSqlCacheNodeParser extends BaseNodeParser {


    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        NoSqlCacheNode cacheNode = new NoSqlCacheNode(new cn.hutool.json.JSONObject(data));
        // 添加输入参数
        addParameters(cacheNode, data);
        // 添加输出参数
        addOutputDefs(cacheNode, data);
        return cacheNode;
    }

    public String getNodeName() {
        return "nosql-node";
    }
}
