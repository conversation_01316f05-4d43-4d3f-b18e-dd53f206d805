package sdata.ops.indicator.config.js;

import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.graalvm.polyglot.Context;

/***
 * <AUTHOR>
 * @version 1.0
 *  This class implements the PooledObject interface for  repeat use jsEngine
 */
public class ContextCachePoolFactory implements PooledObjectFactory<Context> {
    @Override
    public void activateObject(PooledObject<Context> pooledObject) throws Exception {
        // TODO
    }

    @Override
    public void destroyObject(PooledObject<Context> pooledObject) throws Exception {
        Context context = pooledObject.getObject();
        if (context != null) context.close();
    }

    @Override
    public PooledObject<Context> makeObject() throws Exception {
        Context context = Context.newBuilder("js").allowAllAccess(true).build();
        return new DefaultPooledObject<>(context);
    }

    @Override
    public void passivateObject(PooledObject<Context> pooledObject) throws Exception {
        // TODO
    }

    @Override
    public boolean validateObject(PooledObject<Context> pooledObject) {
        return false;
    }
}
