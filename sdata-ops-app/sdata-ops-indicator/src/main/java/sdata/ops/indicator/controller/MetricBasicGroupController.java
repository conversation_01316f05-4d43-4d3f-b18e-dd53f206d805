package sdata.ops.indicator.controller;


import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.DataSourceGroup;
import sdata.ops.base.indicator.model.entity.OpsMetricGroup;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.DataSourceGroupService;
import sdata.ops.indicator.service.OpsMetricGroupService;

@RestController
@RequestMapping("/metric-group")
@RequiredArgsConstructor
public class MetricBasicGroupController {

    private final OpsMetricGroupService metricGroupService;

    @ControllerAuditLog(value = "指标分组-树", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tree")
    public R<Object> tree() {
        return R.data(metricGroupService.tree());
    }

    @ControllerAuditLog(value = "指标分组-新增组", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/save")
    public R<Object> save(@RequestBody OpsMetricGroup group) {
        if (StrUtil.isEmpty(group.getPId())) {
            group.setPId("0");
        }
        metricGroupService.saveOrUpdate(group);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "指标分组-删除组", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) {
        return metricGroupService.deleteGroup(id);
    }


}
