package sdata.ops.indicator.config.mail;

import cn.hutool.core.date.DateUtil;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.pop3.POP3Folder;
import com.sun.mail.pop3.POP3Store;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.mail.MailAuthenticationException;
import sdata.ops.base.indicator.model.dto.MessageDTO;

import javax.mail.*;
import javax.mail.search.AndTerm;
import javax.mail.search.SearchTerm;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

@Slf4j
public class POP3MailReader {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

    public static List<MessageDTO> readTodayMails(@NotNull MailModel mailModel) {
        Properties props = new Properties();
        props.put("mail.pop3.host", mailModel.getHost());
        props.put("mail.pop3.port", mailModel.getPort());
        props.put("mail.pop3.ssl.enable", mailModel.getSslEnable() != 0);
        props.put("mail.store.protocol", "pop3");

        List<MessageDTO> resMsg = new ArrayList<>();

        Session session = Session.getInstance(props,
                new Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(mailModel.getUsername(), mailModel.getPassword());
                    }
                });

        try {
            Store store = session.getStore("pop3");
            store.connect();

            Folder inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);
            Message[] mes = null;
            if (mailModel.getDataScope() == 0) {
                Date today = DateUtil.offsetDay(new Date(), -1);
                // 获取今天的邮件
                DateFilter filter = new DateFilter(today);
                SearchTerm searchTerm = new AndTerm(new SearchTerm[]{filter});
                mes = inbox.search(searchTerm);
            } else {
                mes = inbox.getMessages();
            }
            for (Message me : mes) {
                String uid = ((POP3Folder) inbox).getUID(me);
                resMsg.add(MailManager.convert(me, uid));
            }
            if (mailModel.getScanFull() == 1) {
                if (mailModel.getDataScope() == 0) {
                    Date today = DateUtil.offsetDay(new Date(), -1);
                    // 获取今天的邮件
                    DateFilter filter = new DateFilter(today);
                    SearchTerm searchTerm = new AndTerm(new SearchTerm[]{filter});
                    getSubFolder((POP3Store) store, resMsg, searchTerm);
                } else {
                    getSubFolder((POP3Store) store, resMsg, null);
                }
            }
            inbox.close(true);
            store.close();
        } catch (Exception e) {
            log.error("pop3协议邮箱执行查收错误", e);
            throw new MailAuthenticationException(e);
        }
        return resMsg;
    }

    private static void getSubFolder(POP3Store store, List<MessageDTO> resMsg, SearchTerm today) {
        try {
            Folder[] folders = store.getDefaultFolder().list();
            for (Folder folder : folders) {
                if (!folder.getName().equalsIgnoreCase("inbox") && folder.getParent().getName().equalsIgnoreCase("inbox")) {
                    folder.open(Folder.READ_ONLY);
                    Message[] messages = today == null ? folder.getMessages() : folder.search(today);
                    for (Message me : messages) {
                        String uid = String.valueOf(((IMAPFolder) folder).getUID(me));
                        resMsg.add(MailManager.convert(me, uid));
                    }
                    folder.close(true);
                }
            }
        } catch (Exception e) {
            log.error("IMAP协议读取子文件夹内容异常", e);
        }
    }

    static class DateFilter extends SearchTerm {
        private final Date dateStr;

        public DateFilter(Date dateStr) {
            this.dateStr = dateStr;
        }

        @Override
        public boolean match(Message message) {
            try {
                Date receivedDate = message.getSentDate();
                return receivedDate.after(dateStr);
            } catch (MessagingException e) {
                log.error("邮件查询按时间过滤匹配y异常", e);
                return false;
            }
        }
    }
}

