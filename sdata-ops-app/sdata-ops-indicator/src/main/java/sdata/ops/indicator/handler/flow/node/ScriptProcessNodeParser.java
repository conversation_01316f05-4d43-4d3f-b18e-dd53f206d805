package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class ScriptProcessNodeParser extends BaseNodeParser {

    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        ScriptProcessNode scriptProcessNode = new ScriptProcessNode(data);
        // 添加输入参数
        addParameters(scriptProcessNode, data);
        // 添加输出参数
        addOutputDefs(scriptProcessNode, data);
        return scriptProcessNode;
    }

    public String getNodeName() {
        return "logic-exec-node";
    }
}
