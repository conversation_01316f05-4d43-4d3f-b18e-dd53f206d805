package sdata.ops.indicator.controller;

import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.IndicatorHistory;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.IndicatorHistoryService;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/2/21 14:05
 */
@RestController
@RequestMapping("/indicator-history")
@RequiredArgsConstructor
public class IndicatorHistoryController {

    private final IndicatorHistoryService historyService;

    @ControllerAuditLog(value = "指标历史-列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/list")
    public R<Object> list(@RequestParam String indicatorId) {
        LambdaQueryWrapper<IndicatorHistory> historyWrapper = Wrappers.<IndicatorHistory>lambdaQuery().eq(IndicatorHistory::getIndicatorId, indicatorId);
        historyWrapper.select(
                IndicatorHistory::getId,
                IndicatorHistory::getIndicatorId,
                IndicatorHistory::getExecuteUser,
                IndicatorHistory::getType,
                IndicatorHistory::getFromSystem,
                IndicatorHistory::getExecuteTime
        );
        historyWrapper.orderByDesc(IndicatorHistory::getExecuteTime);
        List<IndicatorHistory> dataList = historyService.list(historyWrapper);
        return R.data(dataList);
    }

    @ControllerAuditLog(value = "指标历史-详情", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/detail")
    public R<Object> detail(@RequestParam String historyId) {
        return R.data(historyService.getById(historyId));
    }

    @ControllerAuditLog(value = "指标历史-删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/delete")
    public R<Object> remove(@RequestBody IndicatorHistory history) {
        if (StrUtil.isEmptyIfStr(history.getId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        historyService.removeById(history.getId());
        return R.fail(MessageConstant.DELETE_SUCCESS);
    }
    @ControllerAuditLog(value = "指标历史-清理历史", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)

    @PostMapping("/clear")
    public R<Object> clear(@RequestBody IndicatorHistory history) {
        if (StrUtil.isEmptyIfStr(history.getIndicatorId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        LambdaQueryWrapper<IndicatorHistory> deleteWrapper = Wrappers.<IndicatorHistory>lambdaQuery().eq(IndicatorHistory::getIndicatorId, history.getIndicatorId());
        historyService.remove(deleteWrapper);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}