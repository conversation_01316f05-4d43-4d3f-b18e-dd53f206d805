package sdata.ops.indicator.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.IndicatorRecycleService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 17:21
 */
@RestController
@RequestMapping("/indicator-recycle")
@RequiredArgsConstructor
public class IndicatorRecycleController {

    private final IndicatorRecycleService recycleService;

    @ControllerAuditLog(value = "指标回收站-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/list")
    public R<Object> page(@RequestParam(required = false) String name) {
        LambdaQueryWrapper<IndicatorInfo> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotEmpty(name)) {
            wrapper.like(IndicatorInfo::getName, name);
        }
        wrapper.eq(IndicatorInfo::getDelFlag, CommonConstant.DEL_FLAG_1);
        wrapper.orderByDesc(IndicatorInfo::getUpdateTime);
        wrapper.select(
                IndicatorInfo::getId,
                IndicatorInfo::getName,
                IndicatorInfo::getCreateTime,
                IndicatorInfo::getCreateBy,
                IndicatorInfo::getUpdateBy,
                IndicatorInfo::getScript,
                IndicatorInfo::getUpdateTime,
                IndicatorInfo::getGroupId,
                IndicatorInfo::getType
        );
        return R.data(recycleService.list(wrapper));
    }

    @ControllerAuditLog(value = "指标回收站-恢复", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/reduction")
    public R<Object> reduction(@RequestBody IndicatorInfo info) {
        return recycleService.reduction(info.getId());
    }

    @ControllerAuditLog(value = "指标回收站-彻底删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/delete")
    public R<Object> delete(@RequestBody IndicatorInfo info) {
        if (null == info.getId()) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        return recycleService.delete(List.of(info.getId()));
    }
}
