package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsScriptTemp;

/**
* <AUTHOR>
* @description 针对表【OPS_SCRIPT_TEMP(脚本执行结果暂存表)】的数据库操作Service
* @createDate 2024-09-23 14:49:25
*/
public interface OpsScriptTempService extends IService<OpsScriptTemp> {


     int scriptIdInsertAndQueryCounter(String thirdId,String indicatorId);

}
