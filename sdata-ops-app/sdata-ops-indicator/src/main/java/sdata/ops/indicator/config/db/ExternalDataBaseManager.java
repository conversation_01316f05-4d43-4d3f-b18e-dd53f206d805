package sdata.ops.indicator.config.db;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.StatementUtil;
import cn.hutool.db.handler.HandleHelper;
import cn.hutool.db.sql.SqlExecutor;
import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import sdata.ops.base.indicator.model.vo.DataSourceConfigVO;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 外部数据源管理
 *
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@Slf4j
public class ExternalDataBaseManager {

    private static final Map<String, Lock> lockMap = new ConcurrentHashMap<>(512);

    private final static Lock LOCK_OBJ = new ReentrantLock();

    /**
     * 从缓存中获取外部数据源连接，未存在则新建后保存至缓存
     *
     * @param vo  数据源配置对象
     * @return datasource
     */
    public static DataSource getExternalDataSource(DataSourceConfigVO vo) {
        try {
            String sourceId = vo.getDataSourceId();
            DataBasePoolCache dataSourcePoolCache = DataBasePoolCache.getInstance();
            //判断是否已有该链接缓存
            if (dataSourcePoolCache.containsKey(sourceId)) {
                return dataSourcePoolCache.getDataSourcePool(sourceId);
            }
            synchronized (LOCK_OBJ) {
                if (!lockMap.containsKey(sourceId)) {
                    lockMap.put(sourceId, new ReentrantLock());
                }
            }
            Lock lock = lockMap.get(sourceId);
            if (lock.tryLock(20, TimeUnit.SECONDS)) {
                try {
                    //判断是否已有该链接缓存
                    if (dataSourcePoolCache.containsKey(sourceId)) {
                        return dataSourcePoolCache.getDataSourcePool(sourceId);
                    }
                    //未存在则新建后添加至缓存
                    DataSource dataSource = createDataSource(vo);
                    //ConcurrentHashMap的Key与Value不可存储null
                    if (dataSource != null) {
                        dataSourcePoolCache.addDataSourcePool(vo.getDataSourceId(), dataSource);
                        return dataSource;
                    }
                    log.error("创建数据源失败，数据源配置信息:" + vo);
                    return null;
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            log.error("创建数据源失败，数据源配置信息"+vo);
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 创建数据源
     *
     * @param sourceConfig 数据源配置信息
     * @return  datasource
     */
    private static DataSource createDataSource(DataSourceConfigVO sourceConfig) {
        //根据数据库类型获取该数据库基本信息
        DataBaseSupportEnum dataSourceSupport = getDataBaseSupportEnum(sourceConfig.getSourceType());
        if (dataSourceSupport == null) {
            log.error("抱歉，当前系统暂不支持{}的数据库连接", sourceConfig.getSourceType());
        }
        try {
            //实例化Druid数据源并设置数据库连接信息
            DruidDataSource druidDataSource = new DruidDataSource();
            //基本必填信息，驱动、连接URL、用户名、密码
            if (dataSourceSupport != null) {
                druidDataSource.setDriverClassName(dataSourceSupport.getDriverClass());
            }
            if (dataSourceSupport != null) {
                druidDataSource.setUrl(sourceConfig.getUrl());
            }
            druidDataSource.setUsername(sourceConfig.getUsername());
            druidDataSource.setPassword(sourceConfig.getPassword());
            //设置连接池基础信息
            druidDataSource.setInitialSize(5);
            druidDataSource.setMinIdle(5);
            druidDataSource.setMaxActive(300);
            druidDataSource.setMaxWait(60000L);
            //数据库验证Sql不为空时添加验证
            if (dataSourceSupport != null && StrUtil.isNotEmpty(dataSourceSupport.getValidSql())) {
                druidDataSource.setValidationQuery(dataSourceSupport.getValidSql());
                druidDataSource.setTestWhileIdle(true);
                druidDataSource.setTestOnBorrow(false);
                druidDataSource.setTestOnReturn(false);
            }
            //###################连接池扩展配置###################
            //连接失败后重连时间间隔
            druidDataSource.setTimeBetweenConnectErrorMillis(60000L);
            //检测需要关闭空闲连接的时间间隔
            druidDataSource.setTimeBetweenEvictionRunsMillis(60000L);
            //连接在池中最小生存的时间
            druidDataSource.setMinEvictableIdleTimeMillis(300000L);
            //是否缓存PreparedStatement（游标），可避免相同SQL多次连接数据库
            druidDataSource.setPoolPreparedStatements(true);
            //设置最大缓存20游标
            druidDataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
//            druidDataSource.setFilters("stat,slf4j");
//            //初始化
//            WallFilter wallFilter = new WallFilter();
//            WallConfig wallConfig = new WallConfig();
//            wallConfig.setCommentAllow(true);
//            wallFilter.setConfig(wallConfig);
//            druidDataSource.setProxyFilters(Stream.of(wallFilter).collect(Collectors.toList()));
            //连接失败后是否中断连接
            druidDataSource.setBreakAfterAcquireFailure(true);
            //连接失败重连次数
            druidDataSource.setConnectionErrorRetryAttempts(0);
            druidDataSource.init();
            return druidDataSource;
        } catch (SQLException e) {
            log.error("使用Druid创建外部数据源失败", e);
        }
        return null;
    }

    /**
     * @param dataSourceSupport 数据库类型基本信息
     * @param sourceConfig      数据库实例配置
     * @return  full url
     */
    private static String getDriverUrl(DataBaseSupportEnum dataSourceSupport, DataSourceConfigVO sourceConfig) {
        //将匹配数据源中的URL关键字符进行替换
        String driverUrl = dataSourceSupport.getDriverUrl();
        driverUrl = driverUrl.replace("[host]", sourceConfig.getHost());
        driverUrl = driverUrl.replace("[port]", StrUtil.isNotEmpty(sourceConfig.getPort()) ? sourceConfig.getPort() : String.valueOf(dataSourceSupport.getDefaultPort()));
        driverUrl = driverUrl.replace("[dbname]", StrUtil.isEmpty(sourceConfig.getServerName()) ? "" : StrUtil.trim(sourceConfig.getServerName()));
        return driverUrl;
    }

    /**
     * 根据数据库名称获取数据源信息
     *
     * @param dataSourceType 数据源类型
     * @return  数据源url模板信息
     */
    private static DataBaseSupportEnum getDataBaseSupportEnum(String dataSourceType) {
        //遍历枚举信息，获取支持数据库的信息
        for (DataBaseSupportEnum dataSourceSupportEnum : DataBaseSupportEnum.values()) {
            //比较该信息中名称是否为需要的数据源
            if (dataSourceSupportEnum.getName().equals(dataSourceType)) {
                return dataSourceSupportEnum;
            }
        }
        return null;
    }


    /**
     * 测试连接是否成功
     *
     * @return
     */
    public static Object testConnect(DataSourceConfigVO sourceConfig, String executeSql) {
        //根据数据源标识获取该实现类
        DataBaseSupportEnum dataSourceSupport = getDataBaseSupportEnum(sourceConfig.getSubSourceType());
        //未获取到该数据源类型实现
        if (dataSourceSupport == null) {
            log.error("抱歉，当前系统暂不支持{}的数据库连接", sourceConfig.getSourceType());
            return false;
        }
        Connection connection = null;
        try {
            //加载数据库驱动类
            Class.forName(dataSourceSupport.getDriverClass());
            //获取数据库连接并校验是否可用
            connection = DriverManager.getConnection(sourceConfig.getUrl(), sourceConfig.getUsername(), sourceConfig.getPassword());
            if (connection == null) {
                return false;
            }
            //无可执行的Sql则直接返回有效
            if (StrUtil.isEmptyIfStr(executeSql)) {
                connection.close();
                return true;
            }
            //执行SQL封装返回结果，并手动关闭连接
            List<Map<String, String>> result = executeSqlAndResultToArr(connection, executeSql);
            connection.close();
            return true;
        } catch (Exception exception) {
            log.error("测试数据库连接出现异常", exception);
            return false;
        } finally {
            //存在连接且未关闭时重新关闭
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭数据库连接异常", e);
            }
        }
    }

    /**
     * 执行Insert、Update、Delete操作
     *
     * @param executeSql     执行SQL
     * @param sourceConfigVo 数据源配置
     * @return 影响行数
     */
    public static int executeSql(String executeSql, DataSourceConfigVO sourceConfigVo) throws Exception {
        Connection connection = null;
        try {
            //根据配置获取数据源并过滤
            DataSource dataSource = getExternalDataSource(sourceConfigVo);
            if (Objects.isNull(dataSource)) {
                return -1;
            }
            connection = dataSource.getConnection();
            //执行Update操作，返回影响行数
            return SqlExecutor.execute(connection, executeSql);
        } catch (Exception e) {
            log.error("SQL执行失败，SQL：" + executeSql, e);
            throw e;
        } finally {
            //关闭链接
            if (Objects.nonNull(connection)) {
                DbUtil.close(new Object[]{connection});
            }
        }
    }

    /**
     * 执行SQL并封装返回结果
     *
     * @param executeSql   sql脚本
     * @param sourceConfigVo  sql数据源对应类型
     * @return   rs
     */
    public static List<Map<String, String>> executeSqlAndResultToArr(String executeSql, DataSourceConfigVO sourceConfigVo) throws Exception {
        try {
            //根据配置获取数据源并过滤
            DataSource dataSource = getExternalDataSource(sourceConfigVo);
            if (Objects.isNull(dataSource)) {
                return new ArrayList<>();
            }
            //执行SQL封装返回结果
            return executeSqlAndResultToArr(dataSource.getConnection(), executeSql);
        } catch (Exception e) {
            log.error("SQL执行失败，SQL：" + executeSql, e);
            throw e;
        }
    }

    /**
     * 执行SQL并将结果重新封装
     *
     * @param connection 数据库连接
     * @param executeSql 执行SQL
     * @return 数据列表
     */
    private static List<Map<String, String>> executeSqlAndResultToArr(Connection connection, String executeSql) throws SQLException {
        //使用hutool相关类执行SQL并进行处理
        List<Entity> entityList = executeSql(connection, executeSql);
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        //遍历查询结果并重新封装
        List<Map<String, String>> result = new ArrayList<>(entityList.size());
        //遍历行数据
        for (Entity entity : entityList) {
            //遍历列数据
            Map<String, String> map = new LinkedHashMap<>(entity.keySet().size());
            for (String colName : entity.keySet()) {
                map.put(colName, entity.getStr(colName));
            }
            result.add(map);
        }
        return result;
    }

    /**
     * 执行SQL，数据封装至实体类中
     *
     * @param conn 数据库链接
     * @param sql  执行SQL
     * @return 数据
     */
    private static List<Entity> executeSql(Connection conn, String sql) throws SQLException {
        //声明变量
        List<Entity> entityList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            //预编译执行器
            ps = StatementUtil.prepareStatement(conn, sql);
            //执行SQL并获取元数据
            rs = ps.executeQuery();
            ResultSetMetaData meta = rs.getMetaData();
            //获取列数并遍历数据封装至实体类
            int columnCount = meta.getColumnCount();
            while (rs.next()) {
                entityList.add(HandleHelper.handleRow(new Entity(), columnCount, meta, rs, false));
            }
        } finally {
            //关闭链接
            DbUtil.close(ps, rs, conn);
        }
        return entityList;
    }

}