package sdata.ops.indicator.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsWorkflowLogSummary;
import sdata.ops.base.indicator.model.entity.OpsWorkflowNodeLogErrorDetail;
import sdata.ops.indicator.mapper.OpsWorkflowLogSummaryMapper;
import sdata.ops.indicator.mapper.OpsWorkflowNodeLogErrorDetailMapper;
import sdata.ops.indicator.service.OpsWorkflowLogSummaryService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_workflow_log_summary(工作流运行日志表)】的数据库操作Service实现
 * @createDate 2025-07-01 16:13:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpsWorkflowLogSummaryServiceImpl extends ServiceImpl<OpsWorkflowLogSummaryMapper, OpsWorkflowLogSummary>
        implements OpsWorkflowLogSummaryService {


    private final OpsWorkflowNodeLogErrorDetailMapper nodeLogErrorDetailMapper;

    @Override
    public void saveWorkflowLogSummaryByWorkflowExecute(JSONObject workflowLogData, List<JSONObject> events) {
        saveWorkflowLogSummary(workflowLogData);
        saveWorkflowNodeErrorDetails(
                workflowLogData.getStr("scheduleId"),
                workflowLogData.getStr("flowId"),
                events
        );
    }
    public void saveWorkflowLogSummary(JSONObject workflowLogData) {
        OpsWorkflowLogSummary summary = new OpsWorkflowLogSummary();
        summary.setScheduleId(workflowLogData.getStr("scheduleId"));
        summary.setFlowId(Long.valueOf(workflowLogData.getStr("flowId")));
        summary.setFlowName(workflowLogData.getStr("flowName"));
        summary.setStatus(workflowLogData.getStr("status"));
        summary.setStartTime(workflowLogData.getDate("startTime"));
        summary.setEndTime(workflowLogData.getDate("endTime"));
        summary.setDuration(workflowLogData.getInt("duration"));
        summary.setTriggerType(workflowLogData.getStr("triggerType"));
        summary.setInitParams(workflowLogData.get("initParams"));
        summary.setResultSummary(workflowLogData.getStr("resultSummary"));
        summary.setErrorCode(workflowLogData.getStr("errorCode"));
        summary.setErrorMsg(workflowLogData.getStr("errorMsg"));
        summary.setHasDetail(workflowLogData.getInt("hasDetail"));
        summary.setExecutionId(workflowLogData.getStr("executionId"));

        this.save(summary);
        log.info("保存工作流运行日志成功,流程id=={}", summary.getFlowId());
    }

    public void saveWorkflowNodeErrorDetails(String scheduleId, String flowId, List<JSONObject> events) {
        if (events == null || events.isEmpty()) {
            return;
        }

        for (JSONObject event : events) {
            OpsWorkflowNodeLogErrorDetail detail = new OpsWorkflowNodeLogErrorDetail();
            detail.setScheduleId(scheduleId);
            detail.setFlowId(flowId);
            detail.setNodeId(event.getStr("nodeId"));
            detail.setNodeName(event.getStr("nodeName"));
            detail.setErrorMessage(event.getStr("errorMsg"));
            detail.setStatus(event.getStr("status"));
            nodeLogErrorDetailMapper.insert(detail);
        }
        log.info("保存工作流节点错误日志成功,流程id=={}", flowId);
    }

}




