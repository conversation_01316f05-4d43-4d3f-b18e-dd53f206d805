package sdata.ops.indicator.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.base.indicator.model.entity.OpsQuartzJobLog;
import sdata.ops.common.annotation.SaveGroup;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.handler.quartz.exception.TaskException;
import sdata.ops.indicator.service.OpsQuartzJobLogService;
import sdata.ops.indicator.service.OpsQuartzJobService;
import sdata.ops.indicator.utils.CronUtils;


@RestController
@RequestMapping("/indicator/scheduler")
@RequiredArgsConstructor
public class SchedulerController extends PageCustomController {

    private final OpsQuartzJobService opsQuartzJobService;

    private final OpsQuartzJobLogService logService;

    @ControllerAuditLog(value = "调度任务-分页", operateType = OperateType.QUERY, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/page")
    public R<Object> list(@RequestParam(required = false) String jobName,
                          @RequestParam(required = false) String status,
                          @RequestParam(required = false, defaultValue = "1") int page,
                          @RequestParam(required = false, defaultValue = "10") int pageSize) {
        LambdaQueryWrapper<OpsQuartzJob> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StringUtils.isNoneBlank(jobName), OpsQuartzJob::getJobName, jobName);
        calendarWrapper.eq(StringUtils.isNoneBlank(status), OpsQuartzJob::getStatus, status);
        Page<OpsQuartzJob> iPage = new Page<>(page, pageSize);
        return R.data(opsQuartzJobService.page(iPage, calendarWrapper));
    }

    @ControllerAuditLog(value = "调度任务-新增", operateType = OperateType.INSERT, moduleName = ModuleName.SCHEDULER)
    @PostMapping("/add")
    public R<Object> add(@Validated(SaveGroup.class) @RequestBody OpsQuartzJob job) throws SchedulerException, TaskException {
        opsQuartzJobService.addJob(job);
        return R.success("添加成功");
    }

    @ControllerAuditLog(value = "调度任务-修改", operateType = OperateType.UPDATE, moduleName = ModuleName.SCHEDULER)
    @PostMapping("/edit")
    public R<Object> edit(@RequestBody OpsQuartzJob job) throws SchedulerException, TaskException {
        opsQuartzJobService.editJob(job);
        return R.success("修改成功");
    }

    @ControllerAuditLog(value = "调度任务-启动", operateType = OperateType.UPDATE, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/resume")
    public R<Object> start(@RequestParam("id") String id) throws SchedulerException {
        opsQuartzJobService.resumeJob(id);
        return R.success("启动成功");
    }

    @ControllerAuditLog(value = "调度任务-暂停", operateType = OperateType.UPDATE, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/pause")
    public R<Object> stop(@RequestParam("id") String id) throws SchedulerException {
        opsQuartzJobService.pause(id);
        return R.success("暂停成功");
    }

    @ControllerAuditLog(value = "调度任务-删除", operateType = OperateType.DELETE, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) throws SchedulerException {
        opsQuartzJobService.deletedJob(id);
        return R.success("删除成功");
    }

    @ControllerAuditLog(value = "调度任务-执行一次", operateType = OperateType.EXECUTE, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/run")
    public R<Object> run(@RequestParam("id") String id) throws SchedulerException {
        opsQuartzJobService.run(id);
        return R.success("执行一次成功");
    }

    @ControllerAuditLog(value = "调度任务-运行日志", operateType = OperateType.QUERY, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/log")
    public R<Object> log(@RequestParam("id") String id,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        Page<OpsQuartzJobLog> pageEntity = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<OpsQuartzJobLog> logWrapper = Wrappers.lambdaQuery();
        logWrapper.eq(OpsQuartzJobLog::getJobId, id);
        logWrapper.orderByDesc(OpsQuartzJobLog::getStartTime);
        return R.data(customPage(logService.page(pageEntity, logWrapper), log -> log));
    }

    @ControllerAuditLog(value = "调度任务-工作流日志", operateType = OperateType.QUERY, moduleName = ModuleName.SCHEDULER)
    @GetMapping("/log/wfLog")
    public R<Object> wfLog(@RequestParam("executionId")String executionId) {
        return R.data(logService.wfDetail(executionId));
    }


}
