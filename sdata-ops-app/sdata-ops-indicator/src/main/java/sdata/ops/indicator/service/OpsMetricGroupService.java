package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsMetricGroup;
import sdata.ops.common.api.R;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_metric_group】的数据库操作Service
* @createDate 2025-08-15 15:54:13
*/
public interface OpsMetricGroupService extends IService<OpsMetricGroup> {


    List<OpsMetricGroup> tree();

    R<Object> deleteGroup(String id);
}
