package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.indicator.model.entity.OpsWarnDataToday;
import sdata.ops.base.indicator.model.vo.WarnBenchVo;

import java.util.List;

/**
 * 当前预警数据表的MyBatis Mapper接口
 * 继承BaseMapper，提供基础CRUD操作
 *
 * <AUTHOR>
 */
public interface OpsWarnDataTodayMapper extends BaseMapper<OpsWarnDataToday> {
    /**
     * 工作台告警列表
     *
     * @param userId      当前登录人id
     * @param warnGroupId 监控分组id
     * @param warnName    监控单元名称，支持模糊搜索
     * @return 工作台监控单元列表
     */
    List<WarnBenchVo> workbenchList(@Param("userId") String userId,
                                    @Param("warnGroupId") String warnGroupId,
                                    @Param("warnName") String warnName);
}
