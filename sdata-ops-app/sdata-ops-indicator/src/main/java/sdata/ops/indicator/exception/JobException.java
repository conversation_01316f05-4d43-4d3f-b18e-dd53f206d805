package sdata.ops.indicator.exception;

import sdata.ops.common.api.IResultCode;

import javax.validation.constraints.NotNull;

public class JobException extends RuntimeException {
    /**
     * 异常代码
     */
    private final IResultCode resultCode;

    public JobException(@NotNull IResultCode resultCode, Object... args) {
        super(String.format(resultCode.getMessage(), args));
        this.resultCode = resultCode;
    }

    public JobException(@NotNull IResultCode resultCode, Throwable cause, Object... args) {
        super(String.format(resultCode.getMessage(), args), cause);
        this.resultCode = resultCode;
    }
}
