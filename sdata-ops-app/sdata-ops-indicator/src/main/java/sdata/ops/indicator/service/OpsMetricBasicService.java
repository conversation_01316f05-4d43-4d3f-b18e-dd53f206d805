package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.OpsMetricFieldVO;
import sdata.ops.base.indicator.model.entity.OpsMetricApiConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricTag;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicApiVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicFieldVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicVO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【ops_metric_basic(指标基本信息表)】的数据库操作Service
* @createDate 2025-08-15 15:54:13
*/
public interface OpsMetricBasicService extends IService<OpsMetricBasic> {

    void deleted(String id);

    void saveOrUpdateOp(OpsMetricBasicVO metricBasic);

    void saveOrUpdateApiConf(OpsMetricBasicApiVO metricBasic);


    Object executeMetricForApi(String httpMethod, String apiPath, Map<String, String> queryParams,Map<String, Object> bodyParams);

    void saveOrUpdateBasicResultConfig(OpsMetricFieldVO fieldVO);

    OpsMetricApiConfig apiConfByMetricId(String metricId);

    List<OpsMetricResultConfig> fieldDetailByMeticId(String metricId);

    Map<String, List<OpsMetricTag>> findTagsByMetricId(List<String> metricId);

    void saveAsById(String id);

    Object executeMetricByMonitorCenter(String metricId,Map<String, Object> input);

    long countPerm(String permId);

    List<OpsMetricBasicFieldVO> queryAllFields();
}
