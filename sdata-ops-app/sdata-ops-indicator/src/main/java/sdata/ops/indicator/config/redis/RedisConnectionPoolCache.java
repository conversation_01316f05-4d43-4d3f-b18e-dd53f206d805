package sdata.ops.indicator.config.redis;

import io.lettuce.core.RedisClient;
import io.lettuce.core.api.StatefulRedisConnection;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RedisConnectionPoolCache {
    private static final RedisConnectionPoolCache INSTANCE = new RedisConnectionPoolCache();
    private final Map<String, StatefulRedisConnection<String, String>> pool = new ConcurrentHashMap<>();


    private final Map<String, RedisClient> clientPool = new ConcurrentHashMap<>();

    private RedisConnectionPoolCache() {
    }

    public static RedisConnectionPoolCache getInstance() {
        return INSTANCE;
    }

    public boolean containsConnectionKey(String key) {
        return pool.containsKey(key);
    }

    public void putConnection(String key, StatefulRedisConnection<String, String> conn) {
        pool.put(key, conn);
    }

    public StatefulRedisConnection<String, String> getConnection(String key) {
        if (pool.containsKey(key))
            return pool.get(key);
        return RedisClient.create().connect();
    }

    public void destroySingle(String key) {
        pool.remove(key);
        clientPool.get(key).shutdown();
    }

    public void destroyAll() {
        pool.forEach((key, conn) -> conn.close());
        pool.clear();
    }

    public void putClient(String key, RedisClient client) {
        clientPool.put(key, client);
    }

    public boolean containsClientKey(String key) {
        return clientPool.containsKey(key);
    }

    public RedisClient getClient(String key) {
        if (clientPool.containsKey(key))
            return clientPool.get(key);
        return null;
    }

    public void removeClient(String key) {
        clientPool.get(key).shutdown();
        clientPool.remove(key);
    }
}
