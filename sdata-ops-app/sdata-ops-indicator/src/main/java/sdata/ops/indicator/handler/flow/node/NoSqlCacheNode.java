package sdata.ops.indicator.handler.flow.node;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import lombok.extern.slf4j.Slf4j;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class NoSqlCacheNode extends BaseNode {

    private static final long serialVersionUID = 1L;


    private final JSONObject data;

    public NoSqlCacheNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        //输出容器
        Map<String, Object> res = new HashMap<>();
        // 获取输入参数
        Map<String, Object> map = chain.getParameterValues(this);

        JSONObject nodeConfig = data.getJSONObject("config");
        //获取数据源id
        String sourceId = nodeConfig.getStr("sourceId");
        //获取数据源操作对象
        DataSourceOperationHandler operationHandler = SpringBeanUtil.getBean(DataSourceOperationHandler.class);
        //获取操作对象
        JSONArray params = nodeConfig.getJSONArray("confList");
        Map<String, Object> sourceExec = new HashMap<>();
        foreachExecCommand(params, sourceExec, map, operationHandler, sourceId);
        // 获取输出参数定义
        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            //处理输出参数内容
            for (Parameter def : outputDefs) {
                String findKey = def.getValue();
                res.put(def.getName(), sourceExec.get(findKey));
            }
        }
        //返回结果
        return res;
    }

    /**
     * 循环执行命令
     *
     * @param params           节点指令配置内容
     * @param sourceExec       指令执行结果
     * @param map              节点参数内容
     * @param operationHandler 数据源执行器
     * @param sourceId         数据源id
     */
    private void foreachExecCommand(JSONArray params, Map<String, Object> sourceExec,
                                    Map<String, Object> map,
                                    DataSourceOperationHandler operationHandler,
                                    String sourceId) {
        for (Object param : params) {
            JSONObject nodeConfig = (JSONObject) param;
            //查看命令类型，set or  get ,仅支持两种类型
            String command = nodeConfig.getStr("command");
            //获取redis操作key
            String keyType = nodeConfig.getStr("keyType");
            String key = null;
            if ("ref".equals(keyType)) {
                String keyRefVal = nodeConfig.getStr("keyRefVal");
                key = (String) map.get(keyRefVal);
            }
            if ("fixed".equals(keyType)) {
                key = nodeConfig.getStr("keyDefaultVal");
            }
            //获取value的值类型
            String valueType = nodeConfig.getStr("valueType");
            //如果是引用，则数据存在与parameter中，需要对应key值get
            Object value = null;
            //引用类型
            if ("ref".equals(valueType)) {
                String valueRefKey = nodeConfig.getStr("valueRefVal");
                value = map.get(valueRefKey);
            }
            //默认类型
            if ("fixed".equals(valueType)) {
                value = nodeConfig.get("valueDefaultVal");
            }
            Long expire = nodeConfig.getLong("expire", 0L);
            JSONObject push = new JSONObject();
            push.set("command", command);
            push.set("key", key);
            push.set("value", value);
            push.set("ex", (expire <= 0L) ? null : expire);
            Object callResult = operationHandler.invokeExecute(sourceId, push);
            sourceExec.put(key, callResult);
        }

    }
}
