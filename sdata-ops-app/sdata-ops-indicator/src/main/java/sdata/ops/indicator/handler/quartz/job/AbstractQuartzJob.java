package sdata.ops.indicator.handler.quartz.job;

import java.util.Date;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.base.indicator.model.entity.OpsQuartzJobLog;
import sdata.ops.common.api.ScheduleConstants;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.service.OpsQuartzJobLogService;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 */
public abstract class AbstractQuartzJob implements Job
{
    private static final Logger log = LoggerFactory.getLogger(AbstractQuartzJob.class);

    /**
     * 线程本地变量
     */
    private static ThreadLocal<Date> threadLocal = new ThreadLocal<>();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        String executionId= String.valueOf(IdWorker.getId());
        context.getJobDetail().getJobDataMap().put(ScheduleConstants.EXECUTION_ID, executionId);
        OpsQuartzJob sysJob = new OpsQuartzJob();
        BeanUtils.copyProperties(context.getMergedJobDataMap().get(ScheduleConstants.TASK_PROPERTIES),sysJob);
        try
        {
            before(context, sysJob);
            if (sysJob != null)
            {
                doExecute(context, sysJob);
            }
            after(context, sysJob, null);
        }
        catch (Exception e)
        {
            log.error("任务执行异常  - ：", e);
            after(context, sysJob, e);
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected void before(JobExecutionContext context, OpsQuartzJob sysJob)
    {
        threadLocal.set(new Date());
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected void after(JobExecutionContext context, OpsQuartzJob sysJob, Exception e)
    {
        Date startTime = threadLocal.get();
        threadLocal.remove();
        final OpsQuartzJobLog sysJobLog = new OpsQuartzJobLog();
        sysJobLog.setJobName(sysJob.getJobName());
        sysJobLog.setJobGroup(sysJob.getJobGroup());
        sysJobLog.setInvokeTarget(sysJob.getInvokeTarget());
        sysJobLog.setStartTime(startTime);
        sysJobLog.setStopTime(new Date());
        sysJobLog.setJobId(String.valueOf(sysJob.getId()));
        sysJobLog.setExecutionId((String) context.getJobDetail().getJobDataMap().get(ScheduleConstants.EXECUTION_ID));
        long runMs = sysJobLog.getStopTime().getTime() - sysJobLog.getStartTime().getTime();
        sysJobLog.setJobMessage(sysJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
        if (e != null)
        {
            sysJobLog.setStatus("1");
            String errorMsg = StringUtils.substring(ExceptionUtil.getRootCauseMessage(e), 0, 2000);
            sysJobLog.setExceptionInfo(errorMsg);
        }
        else
        {
            sysJobLog.setStatus("0");
        }

        // 写入数据库当中
        SpringBeanUtil.getBean(OpsQuartzJobLogService.class).save(sysJobLog);
    }

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, OpsQuartzJob sysJob) throws Exception;
}
