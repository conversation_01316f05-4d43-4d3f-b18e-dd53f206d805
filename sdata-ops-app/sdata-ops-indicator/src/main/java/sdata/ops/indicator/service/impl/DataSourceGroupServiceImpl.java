package sdata.ops.indicator.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.DataSourceGroup;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.IndicatorGroupVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.DataSourceGroupMapper;
import sdata.ops.indicator.mapper.OpsDataSourceMapper;
import sdata.ops.indicator.service.DataSourceGroupService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
@RequiredArgsConstructor
@Service
public class DataSourceGroupServiceImpl extends ServiceImpl<DataSourceGroupMapper, DataSourceGroup> implements DataSourceGroupService {

    private final OpsDataSourceMapper dataSourceMapper;

    /**
     * 指标分组树状查询
     *
     * @return constr
     */
    @Override
    public List<DataSourceGroup> tree() {
        // 查询全部指标
        LambdaQueryWrapper<OpsDataSource> infoWrapper = Wrappers.<OpsDataSource>lambdaQuery()
                .select(OpsDataSource::getId, OpsDataSource::getGroupId)
                .orderByAsc(OpsDataSource::getOrderSort);
        List<OpsDataSource> dataSources = dataSourceMapper.selectList(infoWrapper);
        Map<String, Integer> groupViewCountMap = dataSources.stream()
                .collect(Collectors.groupingBy(OpsDataSource::getGroupId, Collectors.summingInt(e -> 1)));        // 查询全部分组
        List<DataSourceGroup> dataSourceGroups = this.list();
        Map<String, DataSourceGroup> metaMap = dataSourceGroups.stream().collect(Collectors.toMap(DataSourceGroup::getId, i -> i));
        for (DataSourceGroup meta : dataSourceGroups) {
            meta.setCount(groupViewCountMap.getOrDefault(meta.getId(), 0));
            String parentId = meta.getPId();
            if (parentId != null) { // 根节点的parentId为0
                DataSourceGroup parent = metaMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(meta);
                }
            }
        }
        // 收集根节点
        List<DataSourceGroup> result = new ArrayList<>();
        for (DataSourceGroup meta : dataSourceGroups) {
            if (meta.getPId().equals("0")) { // 假设根节点的parentId为0
                result.add(meta);
            }
        }
        // 树形结构转换
        return result;
    }

    /**
     * 删除指标分组
     *
     * @param groupId 指标id
     * @return bool
     */
    @Override
    public R<Object> deleteGroup(String groupId) {
        if (StrUtil.isEmptyIfStr(groupId)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        // 判断是否存在未删除的子分组
        LambdaQueryWrapper<DataSourceGroup> groupWrapper = Wrappers.<DataSourceGroup>lambdaQuery().eq(DataSourceGroup::getPId, groupId);
        List<DataSourceGroup> childList = this.list(groupWrapper);
        if (!CollectionUtils.isEmpty(childList)) {
            return R.fail(MessageConstant.DELETE_FAIL_GROUP + " : " + childList.get(0).getName());
        }
        // 逻辑删除分组
        removeById(groupId);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 批量更新排序
     *
     * @param groupList 更新数据
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Object> batchSaveGroup(List<IndicatorGroupVO> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return R.success(MessageConstant.SAVE_SUCCESS);
        }
        List<DataSourceGroup> updateList = new ArrayList<>(16);
        for (IndicatorGroupVO group : groupList) {
            String groupId = group.getId();
            Integer orderSort = group.getOrderSort();
            if (StrUtil.isNotEmpty(groupId) && orderSort != null) {
                DataSourceGroup update = new DataSourceGroup();
                update.setId(groupId);
                update.setOrderSort(orderSort);
                updateList.add(update);
            }
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

}
