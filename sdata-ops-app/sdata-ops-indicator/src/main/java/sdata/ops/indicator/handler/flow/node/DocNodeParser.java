package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class DocNodeParser extends BaseNodeParser {

    private final ReadDocService readDocService;

    public DocNodeParser(ReadDocService readDocService) {
        this.readDocService = readDocService;
    }


    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        DocNode docNode = new DocNode(readDocService);
        addParameters(docNode, data);
        addOutputDefs(docNode, data);
        return docNode;
    }

    public String getNodeName() {
        return "document-node";
    }
}
