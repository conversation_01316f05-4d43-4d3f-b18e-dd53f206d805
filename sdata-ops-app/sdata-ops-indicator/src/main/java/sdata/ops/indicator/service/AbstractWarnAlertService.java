package sdata.ops.indicator.service;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.Getter;
import sdata.ops.base.indicator.model.entity.OpsWarnAlert;
import sdata.ops.base.indicator.model.vo.WarnAlertContext;
import sdata.ops.indicator.enums.WarnAlertStatusEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知类模板
 * <AUTHOR>
 * @since 2022/09/05
 */
public abstract class AbstractWarnAlertService {

    protected final OpsWarnAlertService warnAlertService;

    protected AbstractWarnAlertService(OpsWarnAlertService warnAlertService) {
        this.warnAlertService = warnAlertService;
    }

    /**
     * 是否支持该通知渠道
     *
     * @param channels 渠道列表
     * @return true表示支持，false表示不支持
     */
    public abstract boolean support(List<String> channels);

    /**
     * 渠道名
     *
     * @return 渠道的名字, 例如 "短信"
     */
    public abstract String channelName();

    /**
     * 告警
     *
     * @param context 告警上下文
     */
    public abstract void alert(WarnAlertContext context);

    /**
     * 计算 新告警数据、需要更新的告警数据、需要删除的告警数据
     *
     * @param warnId   监控单元id
     * @param dataList 告警数据
     * @return 新告警数据、需要更新的告警数据、需要删除的告警数据
     */
    protected Result calc(String warnId, List<String> dataList) {
        if (CharSequenceUtil.isBlank(warnId)) {
            return Result.empty();
        }

        List<OpsWarnAlert> existEntityList = warnAlertService.lambdaQuery()
                .eq(OpsWarnAlert::getWarnId, warnId)
                .eq(OpsWarnAlert::getWarnChannel, this.channelName())
                .list();

        // 新的
        List<OpsWarnAlert> news = calcNews(existEntityList, dataList, warnId);
        // 重复的
        List<OpsWarnAlert> updates = calcUpdates(existEntityList, dataList);
        // 已解决的
        List<OpsWarnAlert> deletes = calcDeletes(existEntityList, dataList);

        return new Result(news, updates, deletes);
    }

    /**
     * 执行结果，把新的增加到数据库，把需要更新的更新到数据库，把需要删除的从数据库删除掉
     *
     * @param dto 包含了三个列表的结果
     */
    protected void executeResult(Result dto) {
        if (!dto.getNews().isEmpty()) {
            warnAlertService.saveBatch(dto.getNews());
        }
        if (!dto.getUpdates().isEmpty()) {
            warnAlertService.updateBatchById(dto.getUpdates());
        }
        if (!dto.getDeletes().isEmpty()) {
            warnAlertService.removeBatchByIds(dto.getDeletes());
        }
    }

    private List<OpsWarnAlert> calcUpdates(List<OpsWarnAlert> existEntityList, List<String> dataList) {
        Set<String> dataSet = new HashSet<>(dataList);
        List<OpsWarnAlert> updates = new ArrayList<>(existEntityList.size());
        for (var exist : existEntityList) {
            if (dataSet.contains(exist.getWarnContent())) {
                exist.setWarnStatus(WarnAlertStatusEnum.REPEAT.getCode());
                updates.add(exist);
            }
        }
        return updates;
    }

    private List<OpsWarnAlert> calcDeletes(List<OpsWarnAlert> existEntityList, List<String> dataList) {
        if (dataList.isEmpty()) {
            return existEntityList;
        }
        Date now = new Date();
        for (OpsWarnAlert e : existEntityList) {
            e.setWarnStatus(WarnAlertStatusEnum.RESOLVED.getCode());
            e.setWarnEndTime(now);
            // 标记删除
            e.setDeleted(1);
        }
        Set<String> dataSet = new HashSet<>(dataList);
        return existEntityList.stream()
                .filter(e -> !dataSet.contains(e.getWarnContent()))
                .collect(Collectors.toList());
    }

    private List<OpsWarnAlert> calcNews(List<OpsWarnAlert> existEntityList, List<String> dataList, String warnId) {
        if (existEntityList.isEmpty()) {
            return dataList.stream().map(e -> createEntity(e, warnId)).collect(Collectors.toList());
        }
        Set<String> existContents = existEntityList.stream().map(OpsWarnAlert::getWarnContent).collect(Collectors.toSet());
        return dataList.stream()
                .filter(data -> !existContents.contains(data))
                .map(data -> createEntity(data, warnId))
                .collect(Collectors.toList());
    }

    /**
     * 创建实体
     *
     * @param data   数据
     * @param warnId 监控单元id
     * @return 通知数据
     */
    private OpsWarnAlert createEntity(String data, String warnId) {
        OpsWarnAlert entity = new OpsWarnAlert();
        entity.setWarnId(warnId);
        entity.setWarnContent(data);
        entity.setWarnStartTime(new Date());
        entity.setWarnStatus(WarnAlertStatusEnum.NEWS.getCode());
        entity.setWarnChannel(this.channelName());
        return entity;
    }

    @Getter
    protected static class Result {
        public Result(List<OpsWarnAlert> news, List<OpsWarnAlert> updates, List<OpsWarnAlert> deletes) {
            this.news = news;
            this.deletes = deletes;
            this.updates = updates;
        }

        /**
         * 新列表
         */
        public final List<OpsWarnAlert> news;
        /**
         * 更新列表
         */
        public final List<OpsWarnAlert> updates;
        /**
         * 删除列表
         */
        public final List<OpsWarnAlert> deletes;

        public static Result empty() {
            return new Result(Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        }
    }
}
