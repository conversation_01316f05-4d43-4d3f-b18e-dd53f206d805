package sdata.ops.indicator.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.IndicatorRelease;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.IndicatorReleaseService;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/2/21 14:05
 */
@RestController
@RequestMapping("/indicator-release")
@RequiredArgsConstructor
public class IndicatorReleaseController {

    private final IndicatorReleaseService releaseService;

    @ControllerAuditLog(value = "指标版本-列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/list")
    public R<Object> list(@RequestParam String indicatorId) {
        LambdaQueryWrapper<IndicatorRelease> releaseWrapper = Wrappers.<IndicatorRelease>lambdaQuery().eq(IndicatorRelease::getIndicatorId, indicatorId);
        releaseWrapper.select(
                IndicatorRelease::getId,
                IndicatorRelease::getIndicatorId,
                IndicatorRelease::getName,
                IndicatorRelease::getCreateTime,
                IndicatorRelease::getCreateBy,
                IndicatorRelease::getIndicatorDesc
        );
        releaseWrapper.orderByDesc(IndicatorRelease::getCreateTime);
        List<IndicatorRelease> dataList = releaseService.list(releaseWrapper);
        return R.data(dataList);
    }

    @ControllerAuditLog(value = "指标版本-详情", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/detail")
    public R<Object> detail(@RequestParam String releaseId) {
        IndicatorRelease release = releaseService.getById(releaseId);
        if (null != release && StrUtil.isEmptyIfStr(release.getScript())) {
            release.setScript("");
        }
        return R.data(release);
    }

    @ControllerAuditLog(value = "指标版本-恢复", operateType = OperateType.UPDATE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/reduction")
    public R<Object> reduction(@RequestBody IndicatorRelease release) {
        return releaseService.reduction(release.getId());
    }

    @ControllerAuditLog(value = "指标版本-删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/delete")
    public R<Object> remove(@RequestBody IndicatorRelease release) {
        if (StrUtil.isEmptyIfStr(release.getId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        releaseService.removeById(release.getId());
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    @ControllerAuditLog(value = "指标版本-清除版本", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/clear")
    public R<Object> clear(@RequestBody IndicatorRelease release) {
        if (StrUtil.isEmptyIfStr(release.getIndicatorId())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        LambdaQueryWrapper<IndicatorRelease> deleteWrapper = Wrappers.<IndicatorRelease>lambdaQuery().eq(IndicatorRelease::getIndicatorId, release.getIndicatorId());
        releaseService.remove(deleteWrapper);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}