package sdata.ops.indicator.service;



import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.common.api.R;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface IndicatorRecycleService extends IService<IndicatorInfo> {

    /**
     * 指标恢复
     *
     * @param id
     * @return
     */
    R<Object> reduction(String id);

    /**
     * 物理删除指标
     *
     * @param id
     * @return
     */
    R<Object> delete(List<String> id);

}
