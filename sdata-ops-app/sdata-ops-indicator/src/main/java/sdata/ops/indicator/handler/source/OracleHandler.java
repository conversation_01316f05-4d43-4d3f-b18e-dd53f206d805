package sdata.ops.indicator.handler.source;

import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.indicator.config.db.DbClientManager;
import sdata.ops.indicator.handler.exec.ScriptExecutor;
import sdata.ops.indicator.handler.source.config.DBCommonWriteHandler;

@Component("oracle")
@RequiredArgsConstructor
public class OracleHandler extends DBCommonWriteHandler implements ScriptExecutor {

    private final DbClientManager dbClientManager;


    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        // 获取jdbcTemplate
        JdbcTemplate template = dbClientManager.query(vo.getDataSourceId());
        if (template == null) {
            throw new RuntimeException("未找到相关数据库操作对象");
        }

        // 查看类型是读取还是写入
        String type = params.getStr("type");
        try {
            if ("read".equals(type)) {
                return readData(template, params);
            } else if ("write".equals(type)) {
                WriteMode mode = WriteMode.valueOf(params.getStr("writeModel", "SIMPLE_INSERT"));
                return processWriteOperation(template, params, mode);
            }
        } catch (Exception e) {
            throw new RuntimeException("数据库操作失败", e);
        }

        throw new IllegalArgumentException("不支持的数据库操作类型: " + type);
    }



}
