package sdata.ops.indicator.config.mail;

import cn.hutool.core.date.DateUtil;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.mail.MailAuthenticationException;
import sdata.ops.base.indicator.model.dto.MessageDTO;

import javax.mail.*;
import javax.mail.search.*;
import java.util.*;

@Slf4j
public class IMAPMailReader {


    public static List<MessageDTO> readTodayMails(@NotNull MailModel mailModel) {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imap");
        props.put("mail.imap.host", mailModel.getHost());
        props.put("mail.imap.port", mailModel.getPort());
        if (mailModel.getSslEnable() == 1) {
            props.put("mail.imap.ssl.enable", "ture");
            props.put("mail.imap.auth", "true");
            props.put("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        }
        List<MessageDTO> resMsg = new ArrayList<>();
        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(mailModel.getUsername(), mailModel.getPassword());
            }
        });
        final Map<String, String> params = new HashMap<>();
        params.put("name", "ops");
        params.put("version", "1.0.0");

        try {
            IMAPStore store = (IMAPStore) session.getStore("imap");
            store.connect();
            store.id(params);

            Folder inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);
            Message[] messages = null;
            if (mailModel.getDataScope() == 0) {
                Date today = DateUtil.offsetDay(new Date(), -1);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(today);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);

                // 搜索今天的邮件，这里使用比较宽松的条件，假设邮件的ReceivedDate准确
                SearchTerm searchTerm = new ReceivedDateTerm(ComparisonTerm.GE, calendar.getTime());
                messages = inbox.search(searchTerm);
            } else {
                messages = inbox.getMessages();
            }
            for (Message me : messages) {
                String uid = String.valueOf(((IMAPFolder) inbox).getUID(me));
                resMsg.add(MailManager.convert(me, uid));
            }
            inbox.close(true);
            //获取所有子文件夹内容
            if (mailModel.getScanFull() == 1) {
                if (mailModel.getDataScope() == 0) {
                    Date today = DateUtil.offsetDay(new Date(), -1);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(today);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);

                    // 搜索今天的邮件，这里使用比较宽松的条件，假设邮件的ReceivedDate准确
                    SearchTerm searchTerm = new ReceivedDateTerm(ComparisonTerm.GE, calendar.getTime());
                    getSubFolder(store, resMsg, searchTerm);
                } else {
                    getSubFolder(store, resMsg, null);
                }
            }
            store.close();
        } catch (Exception e) {
            log.error("imap协议邮箱执行查收错误", e);
            throw new MailAuthenticationException(e);
        }
        return resMsg;
    }

    private static void getSubFolder(IMAPStore store, List<MessageDTO> resMsg, SearchTerm today) {
        try {
            Folder[] folders = store.getDefaultFolder().list();
            for (Folder folder : folders) {
                if (!folder.getName().equalsIgnoreCase("inbox") && folder.getParent().getName().equalsIgnoreCase("inbox"))
                    ;
                {
                    folder.open(Folder.READ_ONLY);
                    Message[] messages = today == null ? folder.getMessages() : folder.search(today);
                    for (Message me : messages) {
                        String uid = String.valueOf(((IMAPFolder) folder).getUID(me));
                        resMsg.add(MailManager.convert(me, uid));
                    }
                    folder.close(true);
                }
            }
        } catch (Exception e) {
            log.error("IMAP协议读取子文件夹内容异常", e);
        }
    }

    public static void main(String[] args) {
        MailModel mailModel = new MailModel();
        mailModel.setHost("pop.163.com").setPort("993").setUsername("<EMAIL>").setPassword("FZCUCXHBOCWECQNB")
                .setSslEnable(1).setScanFull(1).setDataScope(0);
        List<MessageDTO> resMsg = IMAPMailReader.readTodayMails(mailModel);
        System.out.println(resMsg);
    }

}