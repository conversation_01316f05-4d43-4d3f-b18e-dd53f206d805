package sdata.ops.indicator.handler.flow.node;


import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

/**
 * Sql查询节点解析
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public class SqlNodeParser extends BaseNodeParser {


    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        String sql = data.getString("sql");
        SqlNode sqlNode = new SqlNode(sql);
        addParameters(sqlNode, data);
        addOutputDefs(sqlNode, data);
        return sqlNode;
    }

    public String getNodeName() {
        return "sql-node";
    }
}
