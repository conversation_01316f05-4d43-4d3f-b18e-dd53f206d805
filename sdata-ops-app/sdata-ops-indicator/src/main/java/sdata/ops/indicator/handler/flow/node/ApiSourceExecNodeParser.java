package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class ApiSourceExecNodeParser extends BaseNodeParser {


    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        ApiSourceExecNode apiSourceExecNode = new ApiSourceExecNode(data);
        // 添加输入参数
        addParameters(apiSourceExecNode, data);
        // 添加输出参数
        addOutputDefs(apiSourceExecNode, data);
        return apiSourceExecNode;
    }

    public String getNodeName() {
        return "api-data-source-node";
    }
}
