package sdata.ops.indicator.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.dto.DDLResult;
import sdata.ops.base.indicator.model.entity.OpsModelTableIndex;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.indicator.mapper.OpsModelTableChangeLogMapper;
import sdata.ops.base.indicator.model.entity.OpsModelTableChangeLog;
import sdata.ops.indicator.service.OpsModelTableChangeLogService;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_model_table_change_log(数据表变更记录表)】的数据库操作Service实现
 * @createDate 2025-08-01 14:15:54
 */
@Service
public class OpsModelTableChangeLogServiceImpl extends ServiceImpl<OpsModelTableChangeLogMapper, OpsModelTableChangeLog>
        implements OpsModelTableChangeLogService {

    @Override
    public void saveLogByColumnChange(DDLResult ddlResult, OpsModelTableInfo opsInfo) {
        if (ddlResult.hasChanges()) {
            OpsModelTableChangeLog log = new OpsModelTableChangeLog();
            log.setTableId(opsInfo.getId());
            log.setOperator(SecureUtil.currentUserName());
            log.setOperateTime(new Date());
            log.setTableName(opsInfo.getTableName());
            if (ddlResult.getCreateTableSQL() != null) log.setChangeType("create");
            if (!ddlResult.getAlterSQLs().isEmpty()) log.setChangeType("alter");
            log.setChangeContent(JSONUtil.toJsonStr(ddlResult.getNewContent()));
            log.setOldContent(JSONUtil.toJsonStr(ddlResult.getPevContent()));
            log.setLogType("column");
            save(log);
        }
    }

    @Override
    public void saveLogByIndexChange(OpsModelTableInfo info, List<OpsModelTableIndex> indexesToAdd,
                                     List<OpsModelTableIndex> indexesToUpdate,
                                     List<OpsModelTableIndex> indexesToRemove, List<OpsModelTableIndex> oldIndexs) {
        if (!indexesToAdd.isEmpty() || !indexesToUpdate.isEmpty() || !indexesToRemove.isEmpty()) {
            OpsModelTableChangeLog log = new OpsModelTableChangeLog();
            log.setTableId(info.getId());
            log.setOperator(SecureUtil.currentUserName());
            log.setOperateTime(new Date());
            log.setTableName(info.getTableName());
            log.setLogType("index");
            if (!indexesToAdd.isEmpty()) log.setChangeType("add");
            if (!indexesToUpdate.isEmpty() || !indexesToRemove.isEmpty()) log.setChangeType("alter");
            List<JSONObject> newIndexes = indexesToAdd.stream().map(index -> {
                JSONObject json = new JSONObject();
                json.set("indexName", index.getIndexName());
                json.set("indexType", "add");
                return json;
            }).collect(Collectors.toList());
            List<JSONObject> updateIndexes = indexesToUpdate.stream().map(index -> {
                JSONObject json = new JSONObject();
                json.set("indexName", index.getIndexName());
                json.set("indexType", "update");
                return json;
            }).collect(Collectors.toList());
            List<JSONObject> removeIndexes = indexesToRemove.stream().map(index -> {
                JSONObject json = new JSONObject();
                json.set("indexName", index.getIndexName());
                json.set("indexType", "remove");
                return json;
            }).collect(Collectors.toList());
            newIndexes.addAll(updateIndexes);
            newIndexes.addAll(removeIndexes);
            log.setChangeContent(JSONUtil.toJsonStr(newIndexes));
            log.setOldContent(JSONUtil.toJsonStr(oldIndexs.stream().map(
                    index -> {
                        JSONObject json = new JSONObject();
                        json.set("indexName", index.getIndexName());
                        json.set("indexType", "old");
                        return json;
                    }
            ).collect(Collectors.toList())));
            save(log);
        }

    }

    @Override
    public void saveLogByDeleted(OpsModelTableInfo info) {
        OpsModelTableChangeLog log = new OpsModelTableChangeLog();
        log.setTableId(info.getId());
        log.setOperator(SecureUtil.currentUserName());
        log.setOperateTime(new Date());
        log.setTableName(info.getTableName());
        log.setLogType("delete");
        log.setChangeType("delete");
        save(log);
    }

}




