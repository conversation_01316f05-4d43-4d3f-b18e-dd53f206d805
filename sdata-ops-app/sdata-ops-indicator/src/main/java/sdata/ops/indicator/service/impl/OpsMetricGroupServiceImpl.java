package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricGroup;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.mapper.OpsMetricBasicMapper;
import sdata.ops.indicator.mapper.OpsMetricGroupMapper;
import sdata.ops.indicator.service.OpsMetricGroupService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_metric_group】的数据库操作Service实现
 * @createDate 2025-08-15 15:54:13
 */
@Service
@RequiredArgsConstructor
public class OpsMetricGroupServiceImpl extends ServiceImpl<OpsMetricGroupMapper, OpsMetricGroup>
        implements OpsMetricGroupService {

    private final OpsMetricBasicMapper basicMapper;

    @Override
    public List<OpsMetricGroup> tree() {
        LambdaQueryWrapper<OpsMetricBasic> infoWrapper = Wrappers.<OpsMetricBasic>lambdaQuery()
                .select(OpsMetricBasic::getId, OpsMetricBasic::getGroupId).isNotNull(OpsMetricBasic::getGroupId)
                .orderByAsc(OpsMetricBasic::getOrderSort);
        List<OpsMetricBasic> metricBasics = basicMapper.selectList(infoWrapper);
        Map<String, Integer> groupViewCountMap = metricBasics.stream()
                .collect(Collectors.groupingBy(OpsMetricBasic::getGroupId, Collectors.summingInt(e -> 1)));        // 查询全部分组
        List<OpsMetricGroup> metricGroups = this.list();
        Map<String, OpsMetricGroup> metaMap = metricGroups.stream().collect(Collectors.toMap(OpsMetricGroup::getId, i -> i));
        for (OpsMetricGroup meta : metricGroups) {
            meta.setCount(groupViewCountMap.getOrDefault(meta.getId(), 0));
            String parentId = meta.getPId();
            if (parentId != null) { // 根节点的parentId为0
                OpsMetricGroup parent = metaMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(meta);
                }
            }
        }
        // 收集根节点
        List<OpsMetricGroup> result = new ArrayList<>();
        for (OpsMetricGroup meta : metricGroups) {
            if (meta.getPId().equals("0")) { // 假设根节点的parentId为0
                result.add(meta);
            }
        }
        // 树形结构转换
        return result;
    }

    @Override
    public R<Object> deleteGroup(String id) {
        // 判断分组下是否有直播
        LambdaQueryWrapper<OpsMetricBasic> groupWrapper = Wrappers.<OpsMetricBasic>lambdaQuery().eq(OpsMetricBasic::getGroupId, id);
        long childList = basicMapper.selectCount(groupWrapper);
        if (childList != 0L) {
            return R.fail(MessageConstant.DELETE_FAIL_INDICATOR);
        }
        // 逻辑删除分组
        removeById(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}




