package sdata.ops.indicator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import sdata.ops.base.indicator.model.dto.WarnGroupListVO;
import sdata.ops.base.indicator.model.entity.OpsWarnGroup;

import java.util.List;

/**
 * 预警分类表的MyBatis Mapper接口
 * 继承BaseMapper，提供基础CRUD操作
 *
 * <AUTHOR>
 */
public interface OpsWarnGroupMapper extends BaseMapper<OpsWarnGroup> {
    /**
     * 分组列表
     *
     * @return 分组列表
     */
    List<WarnGroupListVO> groupList();
}
