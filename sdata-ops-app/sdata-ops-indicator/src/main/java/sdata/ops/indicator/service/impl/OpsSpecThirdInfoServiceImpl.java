package sdata.ops.indicator.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo;
import sdata.ops.indicator.mapper.OpsSpecThirdInfoMapper;
import sdata.ops.indicator.service.OpsSpecThirdInfoService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @description 针对表【OPS_SPEC_THIRD_INFO(读取oa或者读取邮件的存储表，提供明细任务使用)】的数据库操作Service实现
 * @createDate 2024-10-16 10:35:29
 */
@Slf4j
@Service
public class OpsSpecThirdInfoServiceImpl extends ServiceImpl<OpsSpecThirdInfoMapper, OpsSpecThirdInfo>
        implements OpsSpecThirdInfoService {

    /**
     * @param userId      用户id
     * @param dataId      数据来源唯一id (oa有workflowId,邮件有 mail唯一id)
     * @param dataContent 精简主要数据内容
     * @param fullContent 原始完整数据内容
     *                    <p>
     *                    <p>
     *                        todo  提供给本地脚本使用，具体方法支持逻辑为:
     *                        1.获取脚本轮询查询到的命中消息
     *                        2.存入消息，包含数据内容，用户信息，标题，解析后的处理
     *                        -------
     *                        3.触发一次任务模板逻辑更新数据读取生成明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(String userId, String dataId, String dataContent, String fullContent) {
        //先查询数据是否已经存在
        LambdaQueryWrapper<OpsSpecThirdInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsSpecThirdInfo::getDataId, dataId);
        queryWrapper.eq(OpsSpecThirdInfo::getUserId, userId);
        if (!list(queryWrapper).isEmpty()) {
            return;
        }
        //插入
        OpsSpecThirdInfo saveItem = new OpsSpecThirdInfo();
        saveItem.setDataId(dataId);
        saveItem.setUserId(userId);
        saveItem.setContent(dataContent);
        saveItem.setMetaInfo(fullContent);
        saveItem.setBizDate(DateUtil.format(new Date(), "yyyyMMdd"));
        saveItem.setCreateTime(new Date());
        save(saveItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDataBatch(List<OpsSpecThirdInfo> array) {
        List<OpsSpecThirdInfo> nes=new ArrayList<>();
        for (OpsSpecThirdInfo item : array) {
            //先查询数据是否已经存在
            LambdaQueryWrapper<OpsSpecThirdInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OpsSpecThirdInfo::getDataId, item.getDataId());
            queryWrapper.eq(OpsSpecThirdInfo::getUserId, item.getUserId());
            if (!list(queryWrapper).isEmpty()) {
                continue;
            }
            item.setBizDate(DateUtil.format(new Date(), "yyyyMMdd"));
            item.setCreateTime(new Date());
            nes.add(item);
        }
        if(!nes.isEmpty()){
            log.info("脚本工具类执行三方系统数据入库:::{}",nes.size());
            saveBatch(nes);
        }
    }

    @Override
    public List<OpsSpecThirdInfo> scriptObtUnlessInfo(String userId, String indicatorId) {
        String bizDate=DateUtil.format(new Date(),"yyyyMMdd");
        return baseMapper.scriptObtUnlessInfo(userId,indicatorId,bizDate);
    }
}




