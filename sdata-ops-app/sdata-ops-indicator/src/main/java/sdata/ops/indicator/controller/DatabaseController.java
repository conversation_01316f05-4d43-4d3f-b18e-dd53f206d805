package sdata.ops.indicator.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.OpsDataSourceFrontVO;
import sdata.ops.base.indicator.model.vo.OpsDataSourceVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.config.db.DataBasePoolCache;
import sdata.ops.indicator.config.redis.RedisConnectionPoolCache;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;
import sdata.ops.indicator.service.IndicatorInfoService;
import sdata.ops.indicator.service.OpsDataSourceService;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dataSourceManager/")
@RequiredArgsConstructor
public class DatabaseController extends PageCustomController {


    private final OpsDataSourceService dataSourceService;

    private final IndicatorInfoService infoService;

    private final DataSourceOperationHandler dataSourceOperationHandler;

    private final SystemFeignService userService;

    @ControllerAuditLog(value = "数据源-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/page")
    public Object page(@RequestParam(required = false) String sourceName,
                       @RequestParam(required = false) String groupId,
                       @RequestParam(required = false, defaultValue = "1") int pageNo,
                       @RequestParam(required = false, defaultValue = "10") int pageSize) {
        LambdaQueryWrapper<OpsDataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.hasText(sourceName), OpsDataSource::getSourceName, sourceName);
        wrapper.eq(StringUtils.hasText(groupId), OpsDataSource::getGroupId, groupId);
        wrapper.orderByDesc(OpsDataSource::getCreateTime);
        Page<OpsDataSource> pages = new Page<>(pageNo, pageSize);
        Map<String, Object> res = customPage(dataSourceService.page(pages, wrapper), i -> new OpsDataSourceFrontVO().convert(i, userService.idNameMapper()));
        return R.data(res);
    }
    @ControllerAuditLog(value = "数据源-列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/list")
    public R<List<OpsDataSourceFrontVO>> list(@RequestParam(required = false) String sourceName,
                                              @RequestParam(required = false) String groupId) {
        LambdaQueryWrapper<OpsDataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.hasText(sourceName), OpsDataSource::getSourceName, sourceName);
        wrapper.eq(StringUtils.hasText(groupId), OpsDataSource::getGroupId, groupId);
        wrapper.orderByDesc(OpsDataSource::getCreateTime);
        List<OpsDataSourceFrontVO> dataList = dataSourceService.list(wrapper).stream().map(i -> new OpsDataSourceFrontVO().convert(i, userService.idNameMapper())).collect(Collectors.toList());
        return R.data(dataList);
    }

    @ControllerAuditLog(value = "数据源-保存", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/save")
    public R<Object> save(@RequestBody OpsDataSourceFrontVO dataSource) {

        if (!StrUtil.isEmptyIfStr(dataSource.getId())) {
            //修改数据源连接时，移除数据库缓存
            DataBasePoolCache dataSourcePoolCache = DataBasePoolCache.getInstance();
            if (dataSourcePoolCache.containsKey(dataSource.getId())) {
                dataSourcePoolCache.removeDataSourcePool(dataSource.getId());
            }
            //修改数据源连接时，移除redis缓存
            if (RedisConnectionPoolCache.getInstance().containsClientKey(dataSource.getId())) {
                RedisConnectionPoolCache.getInstance().destroySingle(dataSource.getId());
            }
        }
        dataSourceService.saveOrUpdate(dataSource.convert(dataSource));
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "数据源-详情", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/get")
    public R<Object> detail(@RequestParam String id) {
        return R.data(new OpsDataSourceFrontVO().convert(dataSourceService.getById(id), new HashMap<>()));
    }

    @ControllerAuditLog(value = "数据源-删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") String id) {
        if (StrUtil.isEmptyIfStr(id)) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        LambdaQueryWrapper<IndicatorInfo> infoWrapper = Wrappers.<IndicatorInfo>lambdaQuery()
                .eq(IndicatorInfo::getDataSourceId, id);
        //todo 删除数据源时，判断数据源是否被工作流引用
        List<IndicatorInfo> list = infoService.list(infoWrapper);
        if (CollectionUtils.isEmpty(list)) {
            //删除数据库中配置
            dataSourceService.removeById(id);
            //删除内存中数据库连接池对象
            DataBasePoolCache.getInstance().removeDataSourcePool(id);
            return R.success(MessageConstant.DELETE_SUCCESS);
        }
        return R.fail(MessageConstant.DELETE_FAIL_SOURCE + " : " + list.get(0).getName());
    }

    @ControllerAuditLog(value = "数据源-测试连通性", operateType = OperateType.EXECUTE, moduleName = ModuleName.INDICATOR)
    @PostMapping("/test")
    public R<Object> testConnect(@RequestBody OpsDataSourceVO source) {
        return dataSourceOperationHandler.testConnect(source);
    }


}
