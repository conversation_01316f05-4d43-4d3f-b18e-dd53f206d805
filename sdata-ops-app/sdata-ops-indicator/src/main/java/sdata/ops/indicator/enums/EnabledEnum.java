package sdata.ops.indicator.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum EnabledEnum {
    DISABLED(0, "禁用"),
    ENABLED(1, "启用"),
    ;
    private final int code;
    private final String desc;

    EnabledEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean is(Integer code) {
        return Objects.equals(this.code, code);
    }
}
