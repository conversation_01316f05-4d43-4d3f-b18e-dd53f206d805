package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsMetricApiConfig;
import sdata.ops.indicator.mapper.OpsMetricApiConfigMapper;
import sdata.ops.indicator.service.OpsMetricApiConfigService;

/**
* <AUTHOR>
* @description 针对表【ops_metric_api_config(指标API配置表)】的数据库操作Service实现
* @createDate 2025-08-18 09:42:13
*/
@Service
public class OpsMetricApiConfigServiceImpl extends ServiceImpl<OpsMetricApiConfigMapper, OpsMetricApiConfig>
    implements OpsMetricApiConfigService {

}




