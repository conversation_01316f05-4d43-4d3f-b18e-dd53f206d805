package sdata.ops.indicator.handler.source;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ql.util.express.DefaultContext;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.entity.ExpressScriptModel;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.config.rule.RuleUtil;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

import java.util.List;


@Component("express")
public class ExpressHandler implements ScriptExecutor {
    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        //script 分三部分 1部分是表达式 2 部分是map对象 key表达式变量 value是key在数据中的位置配置 jsonpath或者其他规则
        List<ExpressScriptModel> press = JSONUtil.toList(vo.getScript(), ExpressScriptModel.class);
        Object data = params.get("data");
        if (data instanceof JSONObject) {
            return executeQlProcess(data, press);
        }
        if (data instanceof List) {
            List<Object> source = (List<Object>) data;
            long count = source.parallelStream().filter(i -> executeQlProcess(i, press)).count();
            return count > 0;
        }
        return false;
    }

    private boolean executeQlProcess(Object data, List<ExpressScriptModel> press) {
        boolean endRc = false;
        for (ExpressScriptModel model : press) {
            DefaultContext<String, Object> context = new DefaultContext<>();
            model.getConf().forEach((k, v) -> {
                context.put(k, JSONUtil.getByPath((JSONObject) data, v));
            });
            if (RuleUtil.execute(model.getExpress(), context)) {
                endRc = true;
                break;
            }
        }
        return endRc;
    }

}
