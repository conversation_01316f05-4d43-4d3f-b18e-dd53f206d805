package sdata.ops.indicator.service;



import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.entity.IndicatorHistory;
import sdata.ops.common.api.R;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface IndicatorHistoryService extends IService<IndicatorHistory> {

    /**
     * 保存执行历史
     *
     * @param info       指标信息
     * @param execScript 执行脚本
     * @param result     执行结果
     */
    void saveHistory(IndicatorInfoDTO info, String execScript, R<Object> result);
}
