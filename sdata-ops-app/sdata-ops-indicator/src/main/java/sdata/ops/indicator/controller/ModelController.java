package sdata.ops.indicator.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.entity.*;
import sdata.ops.base.indicator.model.vo.ModelTableFieldsVO;
import sdata.ops.base.indicator.model.vo.ModelTableIndexVO;
import sdata.ops.base.indicator.model.vo.OpsModelTableChangeLogVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.*;

@RestController
@RequestMapping("/indicator-model")
@RequiredArgsConstructor
public class ModelController extends PageCustomController {


    private final OpsModelTableInfoService opsModelTableInfoService;

    private final OpsModelTableColumnService opsModelTableColumnService;

    private final OpsModelTableIndexService opsModelTableIndexService;

    private final OpsModelTableChangeLogService opsModelTableChangeLogService;

    private final OpsModelTableCategoryService categoryService;

    @ControllerAuditLog(value = "数据表基础信息-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tableInfo/page")
    public R<Object> tableInfoPageList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                       @RequestParam(value = "tableName", required = false) String tableName,
                                       @RequestParam(value = "categoryId", required = false) String categoryId,
                                       @RequestParam(value = "dataSourceId", required = false) String dataSourceId) {
        Page<OpsModelTableInfo> pageObject = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsModelTableInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(tableName), OpsModelTableInfo::getTableName, tableName);
        wrapper.eq(StrUtil.isNotEmpty(categoryId), OpsModelTableInfo::getCategoryId, categoryId);
        wrapper.eq(StrUtil.isNotEmpty(dataSourceId), OpsModelTableInfo::getDatasourceId, dataSourceId);
        wrapper.orderByDesc(OpsModelTableInfo::getCreateTime);
        return R.data(opsModelTableInfoService.pageDynamic(pageObject, wrapper));
    }

    @ControllerAuditLog(value = "数据表基础信息-字段列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tableInfo/fields/detail")
    public R<Object> tableInfoDetail(@RequestParam(value = "id") String id) {
        return R.data(opsModelTableColumnService.list(Wrappers.lambdaQuery(OpsModelTableColumn.class).
                eq(OpsModelTableColumn::getTableId, id)
                .orderByAsc(OpsModelTableColumn::getSort)));
    }


    @ControllerAuditLog(value = "数据表基础信息-索引列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tableInfo/indexs/detail")
    public R<Object> tableInfoIndexsDetail(@RequestParam(value = "id") String id) {
        return R.data(opsModelTableIndexService.list(Wrappers.lambdaQuery(OpsModelTableIndex.class).
                eq(OpsModelTableIndex::getTableId, id)));
    }

    @ControllerAuditLog(value = "数据表基础信息-新增表", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/tableInfo/save")
    public R<Object> tableInfoSave(@RequestBody OpsModelTableInfo opsModelTableInfo) {
        return R.data(opsModelTableInfoService.saveNewTableOrUpdate(opsModelTableInfo));
    }

    @ControllerAuditLog(value = "数据表基础信息-新增字段", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/tableInfo/fields/save")
    public R<Object> tableInfoFieldsSave(@RequestBody ModelTableFieldsVO fieldsVO) {
        opsModelTableInfoService.fieldsSaveOrUpdateWithChangeLog(fieldsVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "数据表基础信息-新增索引", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/tableInfo/indexs/save")
    public R<Object> tableInfoIndexsSave(@RequestBody ModelTableIndexVO vo) {
        opsModelTableInfoService.indexsSaveOrUpdateWithChangeLog(vo);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "数据表基础信息-删除表", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @GetMapping("/tableInfo/delete")
    public R<Object> tableInfoDelete(@RequestParam(value = "id") String id) {
        opsModelTableInfoService.deleteTableForReal(id);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "数据表基础信息-查询变更历史", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("changeLog")
    public R<Object> changeLog(@RequestParam(value = "page", defaultValue = "1") Integer page,
                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                               @RequestParam(value = "tableId", required = false) String tableId,
                               @RequestParam(value = "changeType", required = false) String changeType,
                               @RequestParam(value = "logType", required = false) String logType) {
        Page<OpsModelTableChangeLog> pageObject = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsModelTableChangeLog> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(tableId), OpsModelTableChangeLog::getTableId, tableId);
        wrapper.like(StrUtil.isNotEmpty(changeType), OpsModelTableChangeLog::getChangeType, changeType);
        wrapper.like(StrUtil.isNotEmpty(logType), OpsModelTableChangeLog::getLogType, logType);
        wrapper.orderByDesc(OpsModelTableChangeLog::getOperateTime);
        return R.data(opsModelTableChangeLogService.page(pageObject, wrapper).convert(i -> new OpsModelTableChangeLogVO().convert(i)));
    }

    @ControllerAuditLog(value = "数据表基础信息-查询表名", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("tableInfo/shortList")
    public R<Object> tableInfoShortList() {
        return R.data(opsModelTableInfoService.list(Wrappers.lambdaQuery(OpsModelTableInfo.class).select(OpsModelTableInfo::getTableName, OpsModelTableInfo::getId)));
    }

    @ControllerAuditLog(value = "数据表分类-列表", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("category/shortList")
    public R<Object> categoryShortList() {
        return R.data(categoryService.list(Wrappers.lambdaQuery(OpsModelTableCategory.class).select(OpsModelTableCategory::getId, OpsModelTableCategory::getName)));
    }

    @ControllerAuditLog(value = "数据表分类-查询分类（分页）", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("category/page")
    public R<Object> categoryPageList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                      @RequestParam(value = "name", required = false) String name) {
        Page<OpsModelTableCategory> pageObject = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsModelTableCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(name), OpsModelTableCategory::getName, name);
        wrapper.orderByDesc(OpsModelTableCategory::getSort);
        return R.data(categoryService.page(pageObject, wrapper));
    }

    @ControllerAuditLog(value = "数据表分类-新增", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("category/save")
    public R<Object> categorySave(@RequestBody OpsModelTableCategory opsModelTableCategory) {
        return R.data(categoryService.saveOrUpdate(opsModelTableCategory));
    }

    @ControllerAuditLog(value = "数据表分类-删除", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("category/delete")
    public R<Object> categoryDelete(@RequestParam(value = "id") String id) {
        if (!opsModelTableInfoService.list(Wrappers.lambdaQuery(OpsModelTableInfo.class).eq(OpsModelTableInfo::getCategoryId, id)).isEmpty()) {
            return R.fail("请先删除该分类下的表");
        }
        return R.data(categoryService.removeById(id));
    }
}
