package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.Parameter;
import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.PropertyPlaceholderHelper;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.handler.source.config.DataSourceOperationHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ApiSourceExecNode extends BaseNode {

    private final JSONObject data;

    public ApiSourceExecNode(JSONObject data) {
        this.data = data;
    }

    @Override
    protected Map<String, Object> execute(Chain chain) {
        //输出容器
        Map<String, Object> res = new HashMap<>();
        // 获取输入参数
        Map<String, Object> map = chain.getParameterValues(this);

        JSONObject nodeConfig = data.getJSONObject("config");
        //获取API数据源id
        String sourceId = nodeConfig.getString("sourceId");
        //获取数据源操作对象
        DataSourceOperationHandler operationHandler = SpringBeanUtil.getBean(DataSourceOperationHandler.class);
        //执行入参
        cn.hutool.json.JSONObject push = new cn.hutool.json.JSONObject(map);
        Object callResult = operationHandler.invokeExecute(sourceId, push);
        List<Parameter> outputDefs = getOutputDefs();
        if (outputDefs != null &&!outputDefs.isEmpty()) {
            for (Parameter def : outputDefs) {
                res.put(def.getName(), callResult);
            }
        }
        return res;
    }

}
