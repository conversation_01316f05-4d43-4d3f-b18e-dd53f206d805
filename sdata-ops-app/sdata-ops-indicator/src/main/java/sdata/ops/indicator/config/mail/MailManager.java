package sdata.ops.indicator.config.mail;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import sdata.ops.base.indicator.model.dto.EMailFilterDTO;
import sdata.ops.base.indicator.model.dto.EMailFilterRuleDTO;
import sdata.ops.base.indicator.model.dto.MessageDTO;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.indicator.enums.EMailFilterConditionEnum;
import sdata.ops.indicator.enums.EMailFilterRuleTypeEnum;
import sdata.ops.indicator.enums.EMailFilterTargetEnum;
import sdata.ops.indicator.enums.EMailFilterTypeEnum;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邮箱客户端管理工具
 */
@Slf4j
public class MailManager {

    /***
     * 获取邮箱当日邮件
     * @param mailModel 邮件配置实体类
     */
    public static List<MessageDTO> executeMailReceiver(MailModel mailModel) {
        if (mailModel.getProtocol().equalsIgnoreCase("pop3")) {
            return POP3MailReader.readTodayMails(mailModel);
        }
        if (mailModel.getProtocol().equalsIgnoreCase("imap")) {
            return IMAPMailReader.readTodayMails(mailModel);
        }
        return new ArrayList<>();
    }

    /**
     * 邮箱规则验证,返回命中的邮件
     *
     * @return map
     */
    public static List<MessageDTO> ruleValidate(OpsDataSource dataSource, EMailFilterDTO filters) {
        //执行邮件揽收
        MailModel model = JSONUtil.toBean(dataSource.getSourceConfig(), MailModel.class);
        List<MessageDTO> todayMail = executeMailReceiver(model);
        //对邮件内容进行解析，匹配规则是否命中
        return todayMail.stream().filter(item -> !getHitDealMessageFilterIndex(item, filters)).collect(Collectors.toList());
    }

    /**
     * 邮箱规则验证,返回命中的邮件
     *
     * @return map
     */
    public static List<MessageDTO> ruleValidate(List<MessageDTO> dataSource, EMailFilterDTO filters) {
        //执行邮件揽收
        //对邮件内容进行解析，匹配规则是否命中
        dataSource.removeIf(message -> !getHitDealMessageFilterIndex(message, filters));
        return dataSource;
    }

    private static boolean getHitDealMessageFilterIndex(MessageDTO message, EMailFilterDTO filters) {
        boolean matched = false;
        if (filters != null) {
            matched = isFilterRuleMatched(message, filters);
        }
        return matched;
    }

    private static boolean isFilterRuleMatched(MessageDTO message, EMailFilterDTO filter) {
        boolean matched = false;
        log.info(EMailFilterTypeEnum.Receive + " " + filter.getFilterType());
        // 非接收时
        if (!EMailFilterTypeEnum.Receive.toString().equals(filter.getFilterType())) {
            return false;
        }
        List<EMailFilterRuleDTO> rules = filter.getRules();

        List<Boolean> matches = new ArrayList<>();

        // 匹配规则
        rules.forEach(r -> matches.add(matchRule(message, r)));

        if (EMailFilterConditionEnum.None.toString().equals(filter.getFilterCondition())) {
            matched = true;
        }

        // And matches 全部为 true
        if (EMailFilterConditionEnum.And.toString().equals(filter.getFilterCondition())) {
            matched = matches.stream().allMatch(match -> match);
        }

        // Or mathes 有一个为true就行
        if (EMailFilterConditionEnum.Or.toString().equals(filter.getFilterCondition())) {
            matched = matches.stream().anyMatch(match -> match);
        }
        return matched;
    }

    private static Boolean matchRule(MessageDTO message, EMailFilterRuleDTO rule) {
        boolean matched = false;
        String target = rule.getTarget();
        String content = null;
        String type = rule.getType();
        String condition = rule.getCondition();
        List<String> markList = rule.getMarkList();
        // 具体内容目标
        try {
            // 收件人
            if (EMailFilterTargetEnum.Receiver.toString().equals(target)) {
                content = JSONUtil.toJsonStr(message.getReplayTo());
            }
            // 发件人
            if (EMailFilterTargetEnum.Sender.toString().equals(target)) {
                content = JSONUtil.toJsonStr(message.getFrom());
            }
            // 抄送人
            if (EMailFilterTargetEnum.Recipient.toString().equals(target)) {
                content = JSONUtil.toJsonStr(message.getAllRecipients());
            }
            // 主题
            if (EMailFilterTargetEnum.Subject.toString().equals(target)) {
                content = JSONUtil.toJsonStr(message.getSubject());
            }
            // 内容
            if (EMailFilterTargetEnum.Content.toString().equals(target)) {
                content = JSONUtil.toJsonStr(message.getContent());
            }
            //附件名todo
            if (StrUtil.isBlankIfStr(content)) {
                return false;
            }

            List<Boolean> matches = new ArrayList<>();

            // 匹配规则
            String finalContent = content;
            markList.forEach(mark -> matches.add(
                    EMailFilterRuleTypeEnum.Include.toString().equals(type) == finalContent.contains(mark))
            );

            // And matches 全部为 true
            if (EMailFilterConditionEnum.And.toString().equals(condition)) {
                matched = matches.stream().allMatch(match -> match);
            }

            // Or mathes 有一个为true就行
            if (EMailFilterConditionEnum.Or.toString().equals(condition)) {
                matched = matches.stream().anyMatch(match -> match);
            }
        } catch (Exception e) {
            log.error("邮件匹配出现错误", e);
            return false;
        }
        return matched;
    }


    public static MessageDTO convert(Message message, String uid) throws Exception {
        MessageDTO res = new MessageDTO();
        res.setSubject(message.getSubject());
        //res.setContent(message.getContent());
        parseEmail(message, res);
        res.setAttFileNames(parseAtt(message.getContent()));
        res.setUid(uid);
        res.setPart(getMessageContent(message));
        return res;
    }


    public static String getMessageContent(Message message) throws Exception {
        String result = "";
        if (message.isMimeType("text/plain")) {
            result = (String) message.getContent();
        } else if (message.isMimeType("text/html")) {
            result = (String) message.getContent();
        } else if (message.isMimeType("multipart/*")) {
            MimeMultipart mimeMultipart = (MimeMultipart) message.getContent();
            result = getTextFromMimeMultipart(mimeMultipart);
        }
        return result;
    }

    public static String getTextFromMimeMultipart(MimeMultipart mimeMultipart) throws Exception {
        StringBuilder result = new StringBuilder();
        int count = mimeMultipart.getCount();
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                result.append((String) bodyPart.getContent());
            } else if (bodyPart.isMimeType("text/html")) {
                result.append((String) bodyPart.getContent());
            } else if (bodyPart.getContent() instanceof MimeMultipart) {
                result.append(getTextFromMimeMultipart((MimeMultipart) bodyPart.getContent()));
            }
        }
        return result.toString();
    }

    /**
     * 解析附件名称
     *
     * @param content message 内容
     * @return 数组
     * @throws MessagingException 邮件异常
     * @throws IOException        文件流异常
     */
    private static List<String> parseAtt(Object content) throws MessagingException, IOException {
        List<String> attachmentNames = new ArrayList<>();
        if (content instanceof String) {
            return attachmentNames;
        }
        Multipart multipart = ((Multipart) content);
        for (int i = 0; i < multipart.getCount(); i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("multipart/*")) {
                Multipart multipart2 = (Multipart) bodyPart.getContent();
                int count2 = multipart2.getCount();
                for (int j = 0; j < count2; j++) {
                    BodyPart bodyPart2 = multipart2.getBodyPart(j);
                    if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart2.getDisposition())) {
                        attachmentNames.add(bodyPart2.getFileName());
                    }
                }
            } else if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                attachmentNames.add(bodyPart.getFileName());
            }
        }
        return attachmentNames.stream().map(i -> {
            try {
                return MimeUtility.decodeText(i);
            } catch (UnsupportedEncodingException e) {
                return "无效文件名";
            }
        }).collect(Collectors.toList());

    }

    // 将 InternetAddress 数组转换为字符串数组
    private static List<String> toStringArray(Address[] addresses) throws UnsupportedEncodingException {
        if (addresses == null) {
            return new ArrayList<>();
        }
        List<String> res = new ArrayList<>();
        if (addresses.length > 0) {
            InternetAddress internetAddress = (InternetAddress) addresses[0];
            String personalStr = StringUtils.defaultIfEmpty(internetAddress.getPersonal(), StringUtils.EMPTY);
            String personal = MimeUtility.decodeText(personalStr);
            res.add(personal);
        }
        return res;
    }

    /**
     * 解析邮件内容，获取发件人、收件人和抄送人，并解析邮箱地址和名称
     *
     * @param message 邮件对象
     */
    public static void parseEmail(Message message, MessageDTO dto) {
        try {
            // 获取发件人
            Address[] fromAddresses = message.getFrom();
            if (fromAddresses != null) {
                for (Address address : fromAddresses) {
                    if (address instanceof InternetAddress) {
                        InternetAddress internetAddress = (InternetAddress) address;
                        String personal = internetAddress.getPersonal();
                        String email = internetAddress.getAddress();
                        dto.getFrom().add(email == null ? "" : MimeUtility.decodeText(email));
                    }
                }
            }

            // 获取收件人
            Address[] toAddresses = message.getRecipients(Message.RecipientType.TO);
            if (toAddresses != null) {
                for (Address address : toAddresses) {
                    if (address instanceof InternetAddress) {
                        InternetAddress internetAddress = (InternetAddress) address;
                        String personal = internetAddress.getPersonal();
                        String email = internetAddress.getAddress();
                        dto.getAllRecipients().add(email == null ? "" : MimeUtility.decodeText(email));
                    }
                }
            }

            // 获取抄送人
            Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
            if (ccAddresses != null) {
                for (Address address : ccAddresses) {
                    if (address instanceof InternetAddress) {
                        InternetAddress internetAddress = (InternetAddress) address;
                        String personal = internetAddress.getPersonal();
                        String email = internetAddress.getAddress();
                        dto.getReplayTo().add(email == null ? "" : MimeUtility.decodeText(email));
                    }
                }
            }

        } catch (Exception e) {
            log.error("邮箱抄送，接收，发送信息解析异常", e);
        }
    }
}
