package sdata.ops.indicator.handler.source.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.lettuce.core.api.StatefulRedisConnection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.DataSourceConfigVO;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.base.indicator.model.vo.OpsDataSourceVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.indicator.config.db.ExternalDataBaseManager;
import sdata.ops.indicator.config.redis.RedisReadManager;
import sdata.ops.indicator.handler.exec.ScriptExecutor;
import sdata.ops.indicator.handler.exec.ScriptExecutorFactory;
import sdata.ops.indicator.service.OpsDataSourceService;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceOperationHandler {

    private final ScriptExecutorFactory scriptExecutorFactory;

    private final OpsDataSourceService opsDataSourceService;


    public Object invokeExecute(String sourceId,JSONObject push) {
        OpsDataSource source = opsDataSourceService.getById(sourceId);
        if (source == null) {
            throw new RuntimeException("数据源不存在");
        }
        //执行处理逻辑
        ScriptExecutor executor = scriptExecutorFactory.createExecutor(source.getSubSourceType());
        return executor.execute(new ExecuteVO().setScript(source.getSourceConfig()).setDataSourceId(sourceId), push);
    }

    public Object invokeExecuteByDynamicJava(JSONObject push,String source) {
        //执行处理逻辑
        ScriptExecutor executor = scriptExecutorFactory.createExecutor("javaSource");
        return executor.execute(new ExecuteVO().setScript(source), push);
    }

    /**
     * 测试数据源连接
     *
     * @param source 数据源对象
     * @return
     */
    public R<Object> testConnect(OpsDataSourceVO source) {
        String sourceType = source.getSourceType();
        //判定必要条件不为空
        if (StrUtil.isEmptyIfStr(sourceType) || StrUtil.isEmptyIfStr(source.getSourceConfig())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        //序列化VO
        //根据类型，获取对应数据源，根据sub字段判定如何处理
        ExecuteVO setVo = new ExecuteVO();
        if (("api").equals(sourceType)) {
            // API连接测试
            ScriptExecutor executor = scriptExecutorFactory.createExecutor(source.getSubSourceType());
            setVo.setScript(JSONUtil.toJsonStr(source.getSourceConfig()));
            Map<String, Object> params = source.getVariableConfig();
            return executor.execute(setVo, new JSONObject(params)) == null ? R.fail(MessageConstant.CONN_FAIL) : R.success(MessageConstant.CONN_SUCCESS);
        }
        if (("db").equals(sourceType)) {
            //数据库链接测试
            DataSourceConfigVO vo = JSONUtil.toBean(source.getSourceConfig(), DataSourceConfigVO.class);
            vo.setSubSourceType(source.getSubSourceType());
            if (source.getSubSourceType().equals("mysql")) {
                setVo.setScript("SELECT 1");
            }
            if (source.getSubSourceType().equals("oracle")) {
                setVo.setScript("SELECT 1 FROM DUAL");
            }
            if (source.getSubSourceType().equals("dm")) {
                setVo.setScript("SELECT 1 FROM DUAL");
            }
            return (Boolean) ExternalDataBaseManager.testConnect(vo, setVo.getScript()) ? R.success(MessageConstant.CONN_SUCCESS) : R.fail(MessageConstant.CONN_FAIL);

        }
        if (("script").equals(sourceType)) {
            //脚本链接测试
            ScriptExecutor executor = scriptExecutorFactory.createExecutor(source.getSubSourceType());
            setVo.setScript(source.getSourceConfig().getStr("scriptStr"));
            JSONObject params = JSONUtil.parseObj(source.getVariableConfig());
            return R.data(executor.execute(setVo, params));
        }
        if (("mail").equals(sourceType)) {
            //邮件链接测试
            ScriptExecutor executor = scriptExecutorFactory.createExecutor(source.getSubSourceType());
            setVo.setScript(JSONUtil.toJsonStr(source.getSourceConfig()));
            return R.data(executor.execute(setVo, null));
        }
        if (("nosql").equals(sourceType)) {
            //redis链接测试
            DataSourceConfigVO vo = JSONUtil.toBean(source.getSourceConfig(), DataSourceConfigVO.class);
            try (StatefulRedisConnection<String, String> executor = RedisReadManager.testConnection(vo)) {
                executor.sync().set("s-data-valid", "1");
                executor.close();
                return R.success(MessageConstant.CONN_SUCCESS);
            } catch (Exception e) {
                log.error("redis链接测试失败", e);
                return R.fail(MessageConstant.CONN_FAIL);
            }

        }

        return R.fail(MessageConstant.PARAM_FAIL);
    }
}
