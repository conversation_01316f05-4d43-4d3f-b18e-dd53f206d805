package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.common.api.R;
import sdata.ops.common.api.ScheduleConstants;
import sdata.ops.indicator.handler.quartz.exception.TaskException;
import sdata.ops.indicator.mapper.OpsQuartzJobMapper;
import sdata.ops.indicator.service.OpsQuartzJobService;
import sdata.ops.indicator.utils.CronUtils;
import sdata.ops.indicator.utils.ScheduleUtils;

/**
 * <AUTHOR>
 * @description 针对表【ops_quartz_job(定时任务调度表)】的数据库操作Service实现
 * @createDate 2025-07-28 19:20:32
 */
@Service
@RequiredArgsConstructor
public class OpsQuartzJobServiceImpl extends ServiceImpl<OpsQuartzJobMapper, OpsQuartzJob>
        implements OpsQuartzJobService {

    private final Scheduler scheduler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addJob(OpsQuartzJob job) throws SchedulerException, TaskException {
        //新增任务默认暂停状态
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        //任务必须不能并发执行
        job.setConcurrent("1");
        job.setCronExpression(createCronExpression(job));
        if (!CronUtils.isValid(job.getCronExpression())) {
            throw new RuntimeException("新增任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        if (this.save(job)) {
            ScheduleUtils.createScheduleJob(scheduler, job);
        }
        return String.valueOf(job.getId());
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editJob(OpsQuartzJob job) throws SchedulerException, TaskException {
        OpsQuartzJob properties = getById(job.getId());
        job.setCronExpression(createCronExpression(job));
        if (!CronUtils.isValid(job.getCronExpression())) {
            throw new RuntimeException("新增任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        if (updateById(job)) {
            updateSchedulerJob(job, properties.getJobGroup());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeJob(String id) throws SchedulerException {
        OpsQuartzJob job = getById(id);
        Long jobId = job.getId();
        String jobGroup = job.getJobGroup();
        job.setStatus(ScheduleConstants.Status.NORMAL.getValue());
        if (updateById(job)) {
            scheduler.resumeJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pause(String id) throws SchedulerException {
        OpsQuartzJob job = getById(id);
        Long jobId = job.getId();
        String jobGroup = job.getJobGroup();
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        if (updateById(job)) {
            scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletedJob(String id) throws SchedulerException {
        OpsQuartzJob job = getById(id);
        Long jobId = job.getId();
        String jobGroup = job.getJobGroup();
        if (removeById(id))
        {
            scheduler.deleteJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
    }

    @Override
    public void run(String id) throws SchedulerException {
        OpsQuartzJob job = getById(id);
        Long jobId = job.getId();
        String jobGroup = job.getJobGroup();
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(ScheduleConstants.TASK_PROPERTIES, job);
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey))
        {
            scheduler.triggerJob(jobKey, dataMap);
        }
    }

    public void updateSchedulerJob(OpsQuartzJob job, String jobGroup) throws SchedulerException, TaskException {
        Long jobId = job.getId();
        // 判断是否存在
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, job);
    }
    private String createCronExpression(OpsQuartzJob job) {
        if(job.getTimeType().equals("custom")){
            return job.getCronExpression();
        }
        return CronUtils.generateCron(job.getFrequency(), job.getStartTime(), job.getEndTime());
    }

}