package sdata.ops.indicator.handler.source;


import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.exception.JavaSourceComException;
import sdata.ops.indicator.config.java.BaseJavaService;
import sdata.ops.indicator.config.java.CompileUtil;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

import java.util.Objects;

@Slf4j
@Component("javaSource")
public class JavaSourceHandler implements ScriptExecutor {

    /**
     * @param script JAVA代码
     * @return 执行结果
     */
    private Object executeJavaCode(String script, JSONObject param) {
        //获取Java对象
        Object obj = CompileUtil.javaCode2Object(script);
        if (Objects.isNull(obj) || !(obj instanceof BaseJavaService)) {
            log.error("java源代码编译错误: 源代码{}", script);
            throw new JavaSourceComException("java源代码编译错误");
        }
        //执行逻辑
        return ((BaseJavaService) obj).execute(param);
    }


    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        return executeJavaCode(vo.getScript(), params);
    }
}
