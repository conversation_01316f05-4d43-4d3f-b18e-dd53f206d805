package sdata.ops.indicator.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.EnableDTO;
import sdata.ops.base.indicator.model.entity.OpsQuartzJob;
import sdata.ops.base.indicator.model.entity.OpsWarnConfig;
import sdata.ops.base.indicator.model.entity.OpsWarnDataToday;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;
import sdata.ops.base.indicator.model.vo.WarnAlertContext;
import sdata.ops.base.indicator.model.vo.WarnInfoVO;
import sdata.ops.common.api.ScheduleConstants;
import sdata.ops.indicator.enums.EnabledEnum;
import sdata.ops.indicator.enums.WarnConfigKeyEnum;
import sdata.ops.indicator.enums.WarnSyncTypeEnum;
import sdata.ops.indicator.handler.quartz.exception.TaskException;
import sdata.ops.indicator.mapper.OpsWarnInfoMapper;
import sdata.ops.indicator.service.*;
import sdata.ops.indicator.utils.CronUtils;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_warn_info(预警信息表)】的数据库操作Service实现
 * @createDate 2025-08-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OpsWarnInfoServiceImpl extends ServiceImpl<OpsWarnInfoMapper, OpsWarnInfo>
        implements OpsWarnInfoService {
    private final OpsWarnConfigService warnConfigService;
    private final OpsMetricBasicService metricBasicService;
    private final OpsQuartzJobService quartzJobService;
    private final List<AbstractWarnAlertService> warnAlertServiceList;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWarnInfo(String id) throws SchedulerException {
        // 删除基本信息
        super.removeById(id);

        WarnInfoVO vo = new WarnInfoVO();
        vo.setId(id);
        warnConfigService.fillConfig(List.of(vo));
        // 删除任务
        if (StringUtils.isNotBlank(vo.getSyncJobId())) {
            quartzJobService.deletedJob(vo.getSyncJobId());
        }
        if (StringUtils.isNotBlank(vo.getAlertJobId())) {
            quartzJobService.deletedJob(vo.getAlertJobId());
        }
        // 删除配置
        warnConfigService.lambdaUpdate().eq(OpsWarnConfig::getWarnId, id).remove();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWarnInfo(WarnInfoVO vo) throws SchedulerException, TaskException {
        OpsWarnInfo entity = vo.toSaveEntity();
        // 保存时候默认禁用
        entity.setWarnStatus(0);
        super.save(entity);
        String cron = calcCron(vo);
        vo.setWarnSyncCron(cron);

        OpsQuartzJob syncJob = createJob(
                vo.getWarnName() + "_取数任务",
                cron,
                entity.getId(),
                vo.getMarket(),
                "sdata.ops.indicator.service.OpsWarnInfoService.syncData()"
        );

        OpsQuartzJob alertJob = createJob(
                vo.getWarnName() + "_告警任务",
                cron,
                entity.getId(),
                vo.getMarket(),
                "sdata.ops.indicator.service.OpsWarnInfoService.alert()"
        );

        // 添加取数任务
        String syncJobId = quartzJobService.addJob(alertJob);
        // 添加告警任务
        String alertJobId = quartzJobService.addJob(syncJob);

        vo.setSyncJobId(syncJobId);
        vo.setAlertJobId(alertJobId);

        // 保存配置
        warnConfigService.saveWarnConfig(entity.getId(), vo);
    }

    @Override
    public WarnInfoVO getWarnInfo(String id) {
        OpsWarnInfo entity = super.getById(id);
        if (entity == null) {
            return null;
        }
        WarnInfoVO vo = BeanUtil.copyProperties(entity, WarnInfoVO.class);
        warnConfigService.fillConfig(List.of(vo));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWarnInfo(WarnInfoVO vo) throws SchedulerException, TaskException {
        WarnInfoVO oldVo = new WarnInfoVO();
        oldVo.setId(vo.getId());
        warnConfigService.fillConfig(List.of(oldVo));
        String syncJobId = oldVo.getSyncJobId();
        String alertJobId = oldVo.getAlertJobId();

        OpsWarnInfo entity = vo.toSaveEntity();
        super.updateById(entity);

        String cron = calcCron(vo);
        vo.setWarnSyncCron(cron);
        if (syncJobId != null) {
            OpsQuartzJob syncJob = createJob(
                    vo.getWarnName() + "_取数任务",
                    cron,
                    entity.getId(),
                    vo.getMarket(),
                    "sdata.ops.indicator.service.OpsWarnInfoService.syncData()"
            );
            syncJob.setId(Long.parseLong(syncJobId));

            // 修改告警任务
            quartzJobService.editJob(syncJob);
        }

        if (alertJobId != null) {
            OpsQuartzJob alertJob = createJob(
                    vo.getWarnName() + "_告警任务",
                    cron,
                    entity.getId(),
                    vo.getMarket(),
                    "sdata.ops.indicator.service.OpsWarnInfoService.alert()"
            );

            alertJob.setId(Long.parseLong(alertJobId));
            // 修改取数任务
            quartzJobService.editJob(alertJob);
        }

        // 更新配置
        vo.setAlertJobId(alertJobId);
        vo.setSyncJobId(syncJobId);
        warnConfigService.saveWarnConfig(entity.getId(), vo);
    }

    @Override
    @SneakyThrows
    public void runNow(String id) {
        OpsWarnInfo warn = super.getById(id);
        if (warn == null) {
            return;
        }
        WarnInfoVO vo = BeanUtil.copyProperties(warn, WarnInfoVO.class);
        // 补充字段值
        warnConfigService.fillConfig(List.of(vo));
        // 使用调度框架执行任务
        quartzJobService.run(vo.getSyncJobId());
    }

    @Override
    public void syncData(String warnId) {
        OpsWarnInfo warn = super.getById(warnId);
        if (warn == null) {
            return;
        }
        WarnInfoVO vo = BeanUtil.copyProperties(warn, WarnInfoVO.class);
        // 补充字段值
        warnConfigService.fillConfig(List.of(vo));

        // 只在指定时段内运行
        String warnSyncStartTime = vo.getWarnSyncStartTime();
        String warnSyncEndTime = vo.getWarnSyncEndTime();
        String now = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        if (now.compareTo(warnSyncStartTime) >= 0 && now.compareTo(warnSyncEndTime) <= 0) {
            this.syncData(vo);
        }
    }

    @Override
    public void alert(String warnId) {
        OpsWarnInfo warn = super.getById(warnId);
        if (warn == null) {
            return;
        }
        WarnInfoVO vo = BeanUtil.copyProperties(warn, WarnInfoVO.class);
        // 补充字段值
        warnConfigService.fillConfig(List.of(vo));

        // 只在指定时段内运行
        String warnSyncStartTime = vo.getWarnSyncStartTime();
        String warnSyncEndTime = vo.getWarnSyncEndTime();
        String now = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        if (now.compareTo(warnSyncStartTime) >= 0 && now.compareTo(warnSyncEndTime) <= 0) {
            this.alert(vo);
        }
    }

    @Override
    public void enable(EnableDTO dto) throws SchedulerException {
        // 启用
        if (EnabledEnum.ENABLED.is(dto.getEnableStatus())) {
            super.lambdaUpdate()
                    .eq(OpsWarnInfo::getId, dto.getId())
                    .set(OpsWarnInfo::getWarnStatus, dto.getEnableStatus())
                    .update();
            WarnInfoVO vo = new WarnInfoVO();
            vo.setId(dto.getId());
            warnConfigService.fillConfig(List.of(vo));
            // 恢复定时任务
            if (StringUtils.isNotBlank(vo.getSyncJobId())) {
                quartzJobService.resumeJob(vo.getSyncJobId());
            }
            if (StringUtils.isNotBlank(vo.getAlertJobId())) {
                quartzJobService.resumeJob(vo.getAlertJobId());
            }
        }

        // 禁用
        if (EnabledEnum.DISABLED.is(dto.getEnableStatus())) {
            super.lambdaUpdate()
                    .eq(OpsWarnInfo::getId, dto.getId())
                    .set(OpsWarnInfo::getWarnStatus, dto.getEnableStatus())
                    .update();
            WarnInfoVO vo = new WarnInfoVO();
            vo.setId(dto.getId());
            warnConfigService.fillConfig(List.of(vo));
            // 暂停定时任务
            if (StringUtils.isNotBlank(vo.getSyncJobId())) {
                quartzJobService.pause(vo.getSyncJobId());
            }
            if (StringUtils.isNotBlank(vo.getAlertJobId())) {
                quartzJobService.pause(vo.getAlertJobId());
            }
        }
    }

    /**
     * 执行通知
     *
     * @param vo 监控单元信息
     */
    private void alert(WarnInfoVO vo) {
        long batchNumber = System.currentTimeMillis();

        List<String> contentList = SpringUtil.getBean(OpsWarnDataTodayService.class).lambdaQuery()
                .select(OpsWarnDataToday::getWarnContent)
                .eq(OpsWarnDataToday::getWarnId, vo.getId())
                .list()
                .stream()
                .map(OpsWarnDataToday::getWarnContent)
                .collect(Collectors.toList());
        WarnAlertContext context = new WarnAlertContext();
        context.setWarn(vo);
        context.setWarnAlertChannels(vo.getWarnAlertChannels());
        context.setBatchNumber(batchNumber);
        context.setItems(contentList);


        try {
            log.info("开始通知, warnId={}, batchNumber={}", vo.getId(), batchNumber);
            for (AbstractWarnAlertService service : warnAlertServiceList) {
                if (service.support(vo.getWarnAlertChannels())) {
                    log.info("开始通知, warnId={}, batchNumber={}, channel={}", vo.getId(), batchNumber, service.channelName());
                    service.alert(context);
                    log.info("完成通知, warnId={}, batchNumber={}, channel={}", vo.getId(), batchNumber, service.channelName());
                }
            }
            log.info("完成通知, warnId={}, batchNumber={}", vo.getId(), batchNumber);
            // 更新通知时间
            warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_ALERT_LAST_TIMESTAMP, batchNumber);
        } catch (RuntimeException e) {
            log.error("监控单元取数异常", e);
            // 记录异常状态
            warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_EXCEPTION_STATUS, 3);
        }
    }

    /**
     * 执行取数
     * @param vo 监控单元信息
     */
    private void syncData(WarnInfoVO vo) {
        String indicatorId = vo.getWarnSyncIndicatorId();
        if (StringUtils.isBlank(indicatorId)) {
            log.warn("监控单元挂载取数指标为空");
            return;
        }
        long batchNumber = System.currentTimeMillis();
        // 组装入参
        Map<String, Object> param = BeanUtil.beanToMap(vo);
        param.put("batchNumber", batchNumber);
        try {
            Object data = metricBasicService.executeMetricByMonitorCenter(indicatorId, param);
            if (data == null) {
                log.error("取数为空");
                // 记录异常状态
                warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_EXCEPTION_STATUS, 1);
                return;
            } else if (data instanceof JSONArray) {
                // 返回是个json数组
                JSONArray jsonArray = (JSONArray) data;
                SpringUtil.getBean(OpsWarnDataTodayService.class).saveData(jsonArray.toList(String.class), vo);
            } else if (data instanceof JSONObject) {
                // 返回是个json对象
                JSONObject jsonObject = (JSONObject) data;
                String[] dataArray = jsonObject.getByPath("$.items", String[].class);
                SpringUtil.getBean(OpsWarnDataTodayService.class).saveData(Arrays.asList(dataArray), vo);
            } else if (data instanceof Collection) {
                // 返回是个集合
                List<String> dataList = ((Collection<?>) data).stream().map(e -> {
                    if (e instanceof String) {
                        return (String) e;
                    } else {
                        return JSONUtil.toJsonStr(e);
                    }
                }).collect(Collectors.toList());
                SpringUtil.getBean(OpsWarnDataTodayService.class).saveData(dataList, vo);
            } else {
                log.error("取数返回值类型不匹配:{}", data);
                // 记录异常状态
                warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_EXCEPTION_STATUS, 1);
                return;
            }
            // 删除异常状态
            warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_EXCEPTION_STATUS, null);
            // 更新同步时间
            warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_SYNC_LAST_TIMESTAMP, batchNumber);
        } catch (Exception e) {
            log.error("监控单元取数异常", e);
            // 记录异常状态
            warnConfigService.updateConfig(vo.getId(), WarnConfigKeyEnum.WARN_EXCEPTION_STATUS, 2);
        }
    }

    /**
     * 生成cron
     * @see sdata.ops.common.core.util.CronGeneric
     *
     * @return cron表达式
     */
    private String calcCron(WarnInfoVO vo) {
        if (WarnSyncTypeEnum.FIXED_RATE.is(vo.getWarnSyncType())) {
            Duration duration = Duration.ofMinutes(vo.getWarnSyncFixedRateMinutes());
            return String.format("* %s * * * ?", duration.toMinutesPart());
        }
        if (WarnSyncTypeEnum.CRON.is(vo.getWarnSyncType()) && CronUtils.isValid(vo.getWarnSyncCron())) {
            return vo.getWarnSyncCron();
        }
        return null;
    }

    /**
     * 生成任务
     *
     * @param jobName 任务名
     * @param cron    cron
     * @param warnId  告警单元id
     * @param target  执行方法
     * @return 任务
     */
    private OpsQuartzJob createJob(String jobName, String cron, String warnId,String market, String target) {
        OpsQuartzJob job = new OpsQuartzJob();
        job.setJobName(jobName);
        job.setJobGroup(ScheduleConstants.GROUP_WARN_CENTER);
        job.setCronExpression(cron);
        job.setJobType(ScheduleConstants.SCH_TYPE_WARN_SYNC);
        job.setWarnId(warnId);
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        job.setMarket(market);
        job.setInvokeTarget(target);
        job.setMisfirePolicy(ScheduleConstants.MISFIRE_DO_NOTHING);
        job.setTimeType("custom");
        return job;
    }
}