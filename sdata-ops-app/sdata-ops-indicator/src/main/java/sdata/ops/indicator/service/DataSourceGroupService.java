package sdata.ops.indicator.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.DataSourceGroup;
import sdata.ops.common.api.R;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface DataSourceGroupService extends IService<DataSourceGroup> {

    /**
     * 指标分组树状查询
     *
     * @return
     */
    List<DataSourceGroup> tree();

    /**
     * 删除指标分组
     *
     * @param groupId
     * @return
     */
    R<Object> deleteGroup(String groupId);

    /**
     * 批量保存
     *
     * @param groupList
     * @return
     */
}
