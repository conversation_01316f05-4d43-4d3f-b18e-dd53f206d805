package sdata.ops.indicator.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.dto.DDLResult;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.entity.OpsModelTableColumn;
import sdata.ops.base.indicator.model.entity.OpsModelTableIndex;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;
import sdata.ops.base.indicator.model.vo.ModelTableFieldsVO;
import sdata.ops.base.indicator.model.vo.ModelTableIndexVO;
import sdata.ops.indicator.config.db.DataBasePoolCache;
import sdata.ops.indicator.config.db.DbClientManager;
import sdata.ops.indicator.mapper.OpsModelTableInfoMapper;
import sdata.ops.indicator.service.*;
import sdata.ops.indicator.utils.GenerateSqlUtil;
import sdata.ops.indicator.utils.IndexSQLGenerator;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_model_table_info(数据表基础信息表)】的数据库操作Service实现
 * @createDate 2025-08-01 14:15:54
 */
@Service
@RequiredArgsConstructor
public class OpsModelTableInfoServiceImpl extends ServiceImpl<OpsModelTableInfoMapper, OpsModelTableInfo>
        implements OpsModelTableInfoService {

    private final OpsModelTableChangeLogService opsModelTableChangeLogService;

    private final OpsModelTableColumnService opsModelTableColumnService;

    private final OpsModelTableIndexService opsModelTableIndexService;

    private final DbClientManager dbClientManager;

    private final OpsDataSourceService dataSourceService;

    @Override
    public IPage<OpsModelTableInfo> pageDynamic(Page<OpsModelTableInfo> page, LambdaQueryWrapper<OpsModelTableInfo> wrapper) {
        return baseMapper.pageCustom(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fieldsSaveOrUpdateWithChangeLog(ModelTableFieldsVO fieldsVO) {
        //判定是增还是删减了
        //记录变更日志
        //生成变更语句
        OpsModelTableInfo opsInfo = getById(fieldsVO.getTableId());
        // 2. 获取现有字段配置
        List<OpsModelTableColumn> oldFields = opsModelTableColumnService.list(new LambdaQueryWrapper<OpsModelTableColumn>().eq(OpsModelTableColumn::getTableId, fieldsVO.getTableId()));
        // 3. 差异对比并生成DDL
        OpsDataSource opsDataSource = dataSourceService.getById(opsInfo.getDatasourceId());
        if (Objects.isNull(opsDataSource)) {
            throw new RuntimeException("数据源不存在");
        }
        DDLResult ddlResult = compareAndGenerateDDL(fieldsVO.getFields(), oldFields,
                opsInfo.getTableName(), opsInfo.getDescription(), opsDataSource.getSubSourceType());
        // 4. 执行DDL
        if (ddlResult.hasChanges()) {
            JdbcTemplate template = dbClientManager.query(opsDataSource.getId());
            if (ddlResult.getCreateTableSQL() != null) {
                for (String exs : ddlResult.getCreateTableSQL()) {
                    template.execute(exs);
                }
            }

            for (String alterSQL : ddlResult.getAlterSQLs()) {
                template.execute(alterSQL);
            }
        }
        //5. 更新column表内容
        //先删再插入
        opsModelTableColumnService.removeBatchByIds(oldFields.stream().map(OpsModelTableColumn::getId).collect(Collectors.toList()));
        opsModelTableColumnService.saveBatch(fieldsVO.getFields());
        opsModelTableChangeLogService.saveLogByColumnChange(ddlResult, opsInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void indexsSaveOrUpdateWithChangeLog(ModelTableIndexVO vo) {
        //判定是增还是删减了
        //记录变更日志
        //生成变更语句
        OpsModelTableInfo info = getById(vo.getTableId());
        List<OpsModelTableIndex> newIndexes = vo.getIndexs();
        List<OpsModelTableIndex> oldIndexs = opsModelTableIndexService.list(new LambdaQueryWrapper<OpsModelTableIndex>().eq(OpsModelTableIndex::getTableId, vo.getTableId()));
        // 比较差异
        List<OpsModelTableIndex> indexesToAdd = new ArrayList<>();
        List<OpsModelTableIndex> indexesToUpdate = new ArrayList<>();
        List<OpsModelTableIndex> indexesToRemove = new ArrayList<>();

        // 1. 找出需要删除的索引(旧的有，新的没有)
        for (OpsModelTableIndex oldIndex : oldIndexs) {
            if (!containsIndex(newIndexes, oldIndex.getIndexName())) {
                indexesToRemove.add(oldIndex);
            }
        }

        // 2. 找出需要新增或修改的索引
        for (OpsModelTableIndex newIndex : newIndexes) {
            newIndex.setTableId(Long.valueOf(vo.getTableId()));
            OpsModelTableIndex oldIndex = findIndex(oldIndexs, newIndex.getIndexName());

            if (oldIndex == null) {
                indexesToAdd.add(newIndex);
            } else if (!isIndexSame(oldIndex, newIndex)) {
                newIndex.setId(oldIndex.getId());
                indexesToUpdate.add(newIndex);
            }
        }
        JdbcTemplate jdbcTemplate = DataBasePoolCache.getInstance().getJdbcTemplate(String.valueOf(info.getDatasourceId()));
        executeIndexChanges(info.getTableName(), indexesToAdd, indexesToUpdate, indexesToRemove, jdbcTemplate);
        opsModelTableChangeLogService.saveLogByIndexChange(info, indexesToAdd, indexesToUpdate, indexesToRemove, oldIndexs);
    }

    @Override
    public void deleteTableForReal(String id) {
        //删除tableInfo表信息
        //删除tableColumn表信息
        //删除tableIndex表信息
        //执行ddl删除drop table
        OpsModelTableInfo info = getById(id);
        if (info == null || StringUtils.isBlank(info.getTableName())) {
            throw new IllegalArgumentException("无效的表信息");
        }

        // 对表名做简单校验（防止非法字符）
        String tableName = info.getTableName().trim();

        JdbcTemplate jdbcTemplate = dbClientManager.query(String.valueOf(info.getDatasourceId()));

        String dropSql = "DROP TABLE IF EXISTS " + tableName;
        //表删除成功了
        jdbcTemplate.execute(dropSql);
        removeById(id);
        opsModelTableColumnService.remove(new LambdaQueryWrapper<OpsModelTableColumn>().eq(OpsModelTableColumn::getTableId, id));
        opsModelTableIndexService.remove(new LambdaQueryWrapper<OpsModelTableIndex>().eq(OpsModelTableIndex::getTableId, id));
        opsModelTableChangeLogService.saveLogByDeleted(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveNewTableOrUpdate(OpsModelTableInfo opsModelTableInfo) {
        if (opsModelTableInfo.getId() == null) {
            //新增 要判定表是否已经存在
            //验证表名
            if (this.count(new LambdaQueryWrapper<OpsModelTableInfo>().eq(OpsModelTableInfo::getTableName, opsModelTableInfo.getTableName())) > 0L) {
                throw new IllegalArgumentException("同表名配置已经存在!");
            }

            JdbcTemplate template = dbClientManager.query(String.valueOf(opsModelTableInfo.getDatasourceId()));
            if (vivifyTableExist(template, opsModelTableInfo.getTableName())) {
                throw new IllegalArgumentException("表已经存在模板数据库中!");
            }
        }
        return saveOrUpdate(opsModelTableInfo);
    }

    /**
     * 验证指定表是否存在
     *
     * @param template  JdbcTemplate实例
     * @param tableName 要检查的表名
     * @return 表是否存在
     */
    private boolean vivifyTableExist(JdbcTemplate template, String tableName) {
        if (template == null || StringUtils.isBlank(tableName) || template.getDataSource() == null) {
            throw new IllegalArgumentException("参数错误");
        }

        try (Connection connection = template.getDataSource().getConnection()) {
            // 获取数据库产品名称
            String databaseProductName =
                    connection.getMetaData()
                            .getDatabaseProductName()
                            .toLowerCase();

            String querySQL;

            // 根据不同数据库类型选择不同的查询语句
            if (databaseProductName.contains("mysql")) {
                // MySQL语法: 查询information_schema中是否存在该表
                querySQL = "SELECT COUNT(1) FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?";
            } else if (databaseProductName.contains("oracle")) {
                // Oracle语法: 查询user_tables视图
                tableName = tableName.toUpperCase(); // Oracle中表名默认大写
                querySQL = "SELECT COUNT(1) FROM user_tables WHERE table_name = ?";
            } else if (databaseProductName.contains("dm")) {
                // 达梦DM语法: 查询SYSDBA.SYSTABLES
                querySQL = "SELECT COUNT(*)  FROM all_tables " +
                        "WHERE   TABLE_NAME = ?";
            } else {
                throw new UnsupportedOperationException("不支持的数据库类型: " + databaseProductName);
            }

            // 执行查询
            Integer count = template.queryForObject(
                    querySQL,
                    Integer.class, tableName);

            return count > 0;
        } catch (DataAccessException e) {
            // 查询出错视为表不存在
            return false;
        } catch (SQLException e) {
            // 获取数据库元数据失败
            throw new RuntimeException("获取数据库类型失败", e);
        }
    }


    private void executeIndexChanges(String tableName,
                                     List<OpsModelTableIndex> toAdd,
                                     List<OpsModelTableIndex> toUpdate,
                                     List<OpsModelTableIndex> toRemove, JdbcTemplate jdbcTemplate) {
        // 先删除需要移除的索引
        for (OpsModelTableIndex index : toRemove) {
            String sql = IndexSQLGenerator.generateDropIndexSQL(tableName, index);
            jdbcTemplate.execute(sql);
            opsModelTableIndexService.removeById(index.getId());
        }

        // 处理更新（先删除再添加）
        for (OpsModelTableIndex index : toUpdate) {
            String dropSQL = IndexSQLGenerator.generateDropIndexSQL(tableName, index);
            jdbcTemplate.execute(dropSQL);
            String createSQL = IndexSQLGenerator.generateCreateIndexSQL(tableName, index);
            jdbcTemplate.execute(createSQL);
            opsModelTableIndexService.updateById(index);
        }

        // 处理新增
        for (OpsModelTableIndex index : toAdd) {
            String sql = IndexSQLGenerator.generateCreateIndexSQL(tableName, index);
            jdbcTemplate.execute(sql);
            opsModelTableIndexService.save(index);
        }
    }

    // 辅助方法：检查是否包含指定索引
    private boolean containsIndex(List<OpsModelTableIndex> indexes, String indexName) {
        if (indexes == null || indexName == null) {
            return false;
        }

        return indexes.stream()
                .anyMatch(idx -> indexName.equalsIgnoreCase(idx.getIndexName()));
    }

    // 辅助方法：查找索引
    private OpsModelTableIndex findIndex(List<OpsModelTableIndex> indexes, String indexName) {
        if (indexes == null || indexName == null) {
            return null;
        }

        return indexes.stream()
                .filter(idx -> indexName.equalsIgnoreCase(idx.getIndexName()))
                .findFirst()
                .orElse(null);
    }

    // 辅助方法：判断索引是否相同
    private boolean isIndexSame(OpsModelTableIndex oldIndex, OpsModelTableIndex newIndex) {
        return Objects.equals(oldIndex.getIndexType(), newIndex.getIndexType()) &&
                Objects.equals(oldIndex.getColumnNames(), newIndex.getColumnNames()) &&
                Objects.equals(oldIndex.getDescription(), newIndex.getDescription());
    }

    private DDLResult compareAndGenerateDDL(List<OpsModelTableColumn> newColumns, List<OpsModelTableColumn> oldColumns, String tableName, String tableComment, String subSourceType) {
        DDLResult result = new DDLResult();
        // 如果没有旧字段，直接创建新表
        if (oldColumns == null || oldColumns.isEmpty()) {
            result.setCreateTableSQL(new GenerateSqlUtil().generateCreateTableSQLs(newColumns, tableName, tableComment, subSourceType));
            result.setNewContent(parseColumnsToJSON(newColumns, "add"));
            result.setPevContent(new ArrayList<>());
            return result;
        }
        newColumns.sort(Comparator.comparing(OpsModelTableColumn::getSort));
        result.setPevContent(parseColumnsToJSON(oldColumns, "old"));
        List<JSONObject> modifiedColumns = new ArrayList<>();
        // 检查被删除的字段
        for (OpsModelTableColumn oldColumn : oldColumns) {
            if (!containsColumn(newColumns, oldColumn.getColumnName())) {
                result.addAlterSQL("ALTER TABLE " + tableName + " DROP COLUMN " + oldColumn.getColumnName());
                modifiedColumns.add(parseColumnsToJSON(List.of(oldColumn), "drop").get(0));
            }
        }
        // 检查新增或修改的字段
        for (OpsModelTableColumn newColumn : newColumns) {
            // 查找对应的旧字段
            OpsModelTableColumn oldColumn = findColumn(oldColumns, newColumn.getColumnName());

            if (oldColumn == null) {
                // 新增字段
                result.addAlterSQL(generateAddColumnSQL(tableName, newColumn,subSourceType));
                modifiedColumns.add(parseColumnsToJSON(List.of(newColumn), "add").get(0));
            } else if (!isColumnSame(newColumn, oldColumn)) {
                // 修改字段
                String alterSQL = generateModifyColumnSQL(tableName, newColumn, oldColumn,subSourceType);
                if (alterSQL != null) {
                    result.addAlterSQL(alterSQL);
                    modifiedColumns.add(parseColumnsToJSON(List.of(newColumn), "modify").get(0));
                }
            }
        }
        result.setNewContent(modifiedColumns);
        return result;
    }

    private List<JSONObject> parseColumnsToJSON(List<OpsModelTableColumn> newColumns, String type) {
        return newColumns.stream()
                .map(column -> {
                    JSONObject json = new JSONObject();
                    json.set("columnName", column.getColumnName());
                    json.set("columnType", type);
                    return json;
                }).collect(Collectors.toList());
    }

    private boolean containsColumn(List<OpsModelTableColumn> columns, String columnName) {
        if (columns == null || columns.isEmpty() || columnName == null || columnName.trim().isEmpty()) {
            return false;
        }

        String searchName = columnName.trim();
        return columns.stream()
                .filter(Objects::nonNull)
                .map(OpsModelTableColumn::getColumnName)
                .filter(Objects::nonNull)
                .anyMatch(name -> searchName.equalsIgnoreCase(name.trim()));
    }

    private OpsModelTableColumn findColumn(List<OpsModelTableColumn> columns, String columnName) {
        if (columns == null || columnName == null) {
            return null;
        }

        return columns.stream()
                .filter(col -> columnName.equalsIgnoreCase(col.getColumnName()))
                .findFirst()
                .orElse(null);
    }

    private boolean isColumnSame(OpsModelTableColumn newColumn, OpsModelTableColumn oldColumn) {
        return Objects.equals(newColumn.getColumnName(), oldColumn.getColumnName()) &&
                Objects.equals(newColumn.getColumnType(), oldColumn.getColumnType()) &&
                Objects.equals(newColumn.getFieldLength(), oldColumn.getFieldLength()) &&
                Objects.equals(newColumn.getIsNullable(), oldColumn.getIsNullable()) &&
                Objects.equals(newColumn.getDefaultValue(), oldColumn.getDefaultValue()) &&
                Objects.equals(newColumn.getDescription(), oldColumn.getDescription());
    }


    private String generateAddColumnSQL(String tableName, OpsModelTableColumn column, String subSourceType) {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE ").append(tableName)
                .append(" ADD  ").append(column.getColumnName()).append(" ")
                .append(column.getColumnType());

        // 处理长度和精度
        if (column.getFieldLength() != null && column.getFieldLength() > 0) {
            sql.append("(").append(column.getFieldLength());
            sql.append(")");
        }

        // 非空约束
        if (column.getIsNullable() != null && column.getIsNullable() == 0) {
            sql.append(" NOT NULL");
        }

        // 默认值
        if (StringUtils.isNotBlank(column.getDefaultValue())) {
            sql.append(" DEFAULT '").append(column.getDefaultValue()).append("'");
        }

        // 字段注释
        if (StringUtils.isNotBlank(column.getDescription())&&"mysql".equalsIgnoreCase(subSourceType)) {
            sql.append(" COMMENT '").append(column.getDescription()).append("'");
        }

        // 字段位置（可扩展支持 AFTER column）
        // sql.append(" AFTER existing_column");

        return sql.toString();
    }

    private String generateModifyColumnSQL(String tableName, OpsModelTableColumn newColumn,
                                           OpsModelTableColumn oldColumn, String subSourceType) {
        // 如果只是描述信息变更，不需要DDL
        if (onlyDescriptionChanged(newColumn, oldColumn)) {
            return null;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE ").append(tableName)
                .append(" MODIFY  ").append(newColumn.getColumnName()).append(" ")
                .append(newColumn.getColumnType());

        // 处理长度和精度
        if (newColumn.getFieldLength() != null && newColumn.getFieldLength() > 0) {
            sql.append("(").append(newColumn.getFieldLength());
            sql.append(")");
        }

        // 非空约束
        if (newColumn.getIsNullable() != null) {
            sql.append(newColumn.getIsNullable() == 0 ? " NOT NULL" : " NULL");
        }

        // 默认值
        if (StringUtils.isNotBlank(newColumn.getDefaultValue())) {
            sql.append(" DEFAULT '").append(newColumn.getDefaultValue()).append("'");
        } else if (oldColumn.getDefaultValue() != null) {
            // 移除默认值
            sql.append(" DEFAULT NULL");
        }

        // 字段注释
        if (StringUtils.isNotBlank(newColumn.getDescription())&&"mysql".equalsIgnoreCase(subSourceType)) {
            sql.append(" COMMENT '").append(newColumn.getDescription()).append("'");
        }

        return sql.toString();
    }

    private boolean onlyDescriptionChanged(OpsModelTableColumn newColumn, OpsModelTableColumn oldColumn) {
        return Objects.equals(newColumn.getColumnName(), oldColumn.getColumnName()) &&
                Objects.equals(newColumn.getColumnType(), oldColumn.getColumnType()) &&
                Objects.equals(newColumn.getFieldLength(), oldColumn.getFieldLength()) &&
                Objects.equals(newColumn.getIsNullable(), oldColumn.getIsNullable()) &&
                Objects.equals(newColumn.getDefaultValue(), oldColumn.getDefaultValue()) &&
                !Objects.equals(newColumn.getDescription(), oldColumn.getDescription());
    }
}




