package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【OPS_SPEC_THIRD_INFO(读取oa或者读取邮件的存储表，提供明细任务使用)】的数据库操作Service
* @createDate 2024-10-16 10:35:29
*/
public interface OpsSpecThirdInfoService extends IService<OpsSpecThirdInfo> {


    void saveData(String userId,String dataId,String dataContent,String fullContent);



    @Transactional(rollbackFor = Exception.class)
    void saveDataBatch(List<OpsSpecThirdInfo> array);


    List<OpsSpecThirdInfo>  scriptObtUnlessInfo(String userId,String indicatorId);
}
