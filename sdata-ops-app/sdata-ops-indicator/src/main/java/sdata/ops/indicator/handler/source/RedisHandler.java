package sdata.ops.indicator.handler.source;

import cn.hutool.json.JSONObject;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.indicator.config.redis.RedisReadManager;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

@Component("redisClient")
public class RedisHandler implements ScriptExecutor {


    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        String command = params.getStr("command");
        String key = params.getStr("key");
        String value = params.getStr("value");
        Long ex = params.getLong("ex");
        return RedisReadManager.execute(vo.getDataSourceId(), command, key, value, ex);
    }
}
