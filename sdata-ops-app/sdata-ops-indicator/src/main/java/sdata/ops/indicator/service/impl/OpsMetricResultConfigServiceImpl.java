package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.base.indicator.model.vo.SqlColumnInfoVO;
import sdata.ops.indicator.mapper.OpsMetricResultConfigMapper;
import sdata.ops.indicator.service.OpsMetricResultConfigService;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【ops_metric_result_config(指标数据结果配置表)】的数据库操作Service实现
* @createDate 2025-08-18 16:40:14
*/
@Service
public class OpsMetricResultConfigServiceImpl extends ServiceImpl<OpsMetricResultConfigMapper, OpsMetricResultConfig>
    implements OpsMetricResultConfigService {
    // 缓存
    private final Cache<String, List<SqlColumnInfoVO>> TABLE_HEAD_CACHE = Caffeine.newBuilder()
            // 一分钟过期
            .expireAfterAccess(Duration.ofMinutes(1))
            .initialCapacity(16)
            .build();


    @Override
    public List<SqlColumnInfoVO> tableHead(String metricId) {
        return TABLE_HEAD_CACHE.get(metricId,
                k -> super.lambdaQuery()
                        // 根据指标id查询
                        .eq(OpsMetricResultConfig::getMetricId, metricId)
                        // 过滤掉空值
                        .isNotNull(OpsMetricResultConfig::getDisplayName)
                        // 排序
                        .orderByAsc(OpsMetricResultConfig::getCreateTime)
                        .list()
                        .stream()
                        .map(e -> {
                            SqlColumnInfoVO vo = new SqlColumnInfoVO();
                            vo.setColumnName(e.getResultField());
                            vo.setColumnComment(e.getDisplayName());
                            return vo;
                        })
                        .collect(Collectors.toList()));
    }
}




