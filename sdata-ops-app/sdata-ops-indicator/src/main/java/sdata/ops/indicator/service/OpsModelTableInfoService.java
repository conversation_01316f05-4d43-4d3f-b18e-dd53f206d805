package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsModelTableInfo;
import sdata.ops.base.indicator.model.vo.ModelTableFieldsVO;
import sdata.ops.base.indicator.model.vo.ModelTableIndexVO;

/**
* <AUTHOR>
* @description 针对表【ops_model_table_info(数据表基础信息表)】的数据库操作Service
* @createDate 2025-08-01 14:15:54
*/
public interface OpsModelTableInfoService extends IService<OpsModelTableInfo> {

    IPage<OpsModelTableInfo> pageDynamic(Page<OpsModelTableInfo> page, LambdaQueryWrapper<OpsModelTableInfo> wrapper);

    void fieldsSaveOrUpdateWithChangeLog(ModelTableFieldsVO fieldsVO);


    void indexsSaveOrUpdateWithChangeLog(ModelTableIndexVO vo);

    void deleteTableForReal(String id);

    Object saveNewTableOrUpdate(OpsModelTableInfo opsModelTableInfo);
}
