package sdata.ops.indicator.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.dto.MappingDTO;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.vo.IndicatorMoveVO;
import sdata.ops.common.api.R;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/21 14:09
 */
public interface IndicatorInfoService extends IService<IndicatorInfo> {

    /**
     * 保存指标数据
     *
     * @param info
     * @return
     */
    R<Object> saveInfo(IndicatorInfo info);


    R<Object> detail(String id);

    /**
     * 删除指标
     *
     * @param id
     * @return
     */
    R<Object> deleteInfo(String id);

    /**
     * 根据sql解析出对应的返回字段及参数条件
     *
     * @param info
     * @return
     */
    R<Object> sqlParse(IndicatorInfo info);

    /**
     * 运行
     *
     * @param info
     * @return
     */
    R<Object> execute(IndicatorInfoDTO info);

    R<Object> executeUp(IndicatorInfoDTO info);

    /**
     * 批量保存
     *
     * @param infoList
     * @return
     */
    R<Object> batchSaveInfo(List<IndicatorInfo> infoList);

    List<MappingDTO> listShortInfo();

    R<Object> moveInfo(IndicatorMoveVO info);

}
