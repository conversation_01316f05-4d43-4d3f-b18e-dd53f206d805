package sdata.ops.indicator.handler.flow.node;

import com.agentsflex.core.chain.node.BaseNode;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.parser.BaseNodeParser;

public class DataBaseReadNodeParser extends BaseNodeParser {

    @Override
    protected BaseNode doParse(JSONObject root, JSONObject data, Tinyflow tinyflow) {
        // 创建自定义节点
        DataBaseReadNode dataBaseReadNode = new DataBaseReadNode(data);
        // 添加输入参数
        addParameters(dataBaseReadNode, data);
        // 添加输出参数
        addOutputDefs(dataBaseReadNode, data);
        return dataBaseReadNode;
    }

    public String getNodeName() {
        return "db-read-node";
    }
}
