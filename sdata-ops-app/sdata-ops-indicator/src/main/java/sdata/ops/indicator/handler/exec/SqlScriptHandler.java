package sdata.ops.indicator.handler.exec;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.DataSourceConfigVO;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.core.util.AesUtils;
import sdata.ops.indicator.config.db.DataBasePoolCache;
import sdata.ops.indicator.config.db.ExternalDataBaseManager;
import sdata.ops.indicator.service.OpsDataSourceService;

import java.util.List;
import java.util.Map;

@Component("dbRead")
@RequiredArgsConstructor
public class SqlScriptHandler implements ScriptExecutor {

    private  final OpsDataSourceService opsDataSourceService;

    public List<Map<String, Object>> query(String dataSourceId, String sqlScript) {
        //1.查看数据源是否有该数据源对象
        if (!DataBasePoolCache.getInstance().containsKey(dataSourceId)) {
            DataSourceConfigVO configVO=queryDataSourceVO(dataSourceId);
            ExternalDataBaseManager.getExternalDataSource(configVO);
        }
        //2.执行配置sql
        JdbcTemplate template = DataBasePoolCache.getInstance().getJdbcTemplate(dataSourceId);
        return template.queryForList(sqlScript);
    }

    private DataSourceConfigVO queryDataSourceVO(String dataSourceId) {
        OpsDataSource source = opsDataSourceService.getById(dataSourceId);
        String config = AesUtils.decryptStr(source.getSourceConfig());
        DataSourceConfigVO configVO = JSONUtil.toBean(JSONUtil.parseObj(config), DataSourceConfigVO.class);
        configVO.setSourceType(source.getSourceType());
        configVO.setDataSourceId(source.getId());
        return configVO;
    }

    public void execute(String dataSourceId, String sqlScript) {
        //1.查看数据源是否有该数据源对象
        if (!DataBasePoolCache.getInstance().containsKey(dataSourceId)) {
            DataSourceConfigVO configVO=queryDataSourceVO(dataSourceId);
            ExternalDataBaseManager.getExternalDataSource(configVO);
        }
        //2.执行配置sql
        JdbcTemplate template = DataBasePoolCache.getInstance().getJdbcTemplate(dataSourceId);
        template.execute(sqlScript);
    }

    private String replacerVariable(JSONObject params, String sqlScript) {
        String res = sqlScript;
        for (Map.Entry<String, Object> p : params) {
            res = StringUtils.replace(sqlScript, "${" + p.getKey() + "}", p.getValue().toString());
        }
        return res;
    }


    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        String sqlVal = replacerVariable(params, vo.getScript());
        return query(vo.getDataSourceId(), sqlVal);
    }
}
