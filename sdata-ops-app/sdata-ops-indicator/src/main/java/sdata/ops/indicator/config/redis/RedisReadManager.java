package sdata.ops.indicator.config.redis;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.springframework.util.StringUtils;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.DataSourceConfigVO;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.service.OpsDataSourceService;

import java.time.Duration;

public class RedisReadManager {

    // 可提取为配置项
    private static final int DEFAULT_IO_THREADS = 4; // IO 线程数
    private static final int DEFAULT_CONNECTION_TIMEOUT_MS = 3000; // 连接超时时间

    public static StatefulRedisConnection<String, String> createOrGetRedisConnection(String sourceId) {
        // 获取数据源配置信息
        OpsDataSourceService opsDataSourceService = SpringBeanUtil.getBean(OpsDataSourceService.class);
        OpsDataSource source = opsDataSourceService.getById(sourceId);
        // 格式化信息
        JSONObject parseObj = JSONUtil.parseObj(source.getSourceConfig());
        // 假设你已经有了配置信息
        String host = parseObj.getStr("host");
        String port = parseObj.getStr("port");
        String username = parseObj.getStr("username");
        String password = parseObj.getStr("password");
        RedisClient redisClient=createNow(host,port,username, password);


        // 建立连接
        StatefulRedisConnection<String, String> connection = redisClient.connect();

        RedisConnectionPoolCache.getInstance().putConnection(sourceId, connection);
        RedisConnectionPoolCache.getInstance().putClient(sourceId, redisClient);
        return connection;
    }

    private static RedisClient createNow(String host, String port, String username, String password) {
        String uriString;
        // 支持三种情况：无用户名密码、有用户名和密码、仅有密码
        if (StringUtils.hasText(username) && StringUtils.hasText(password)) {
            // 第二种情况：有用户名和密码
            uriString = String.format("redis://%s:%s@%s:%d", username, password, host, Integer.parseInt(port));
        } else if (!StringUtils.hasText(username) && StringUtils.hasText(password)) {
            // 第三种情况：没有用户名只有密码（Lettuce 支持只设置密码的情况）
            uriString = String.format("redis://:%s@%s:%d", password, host, Integer.parseInt(port));
        } else {
            // 第一种情况：没有用户名和密码
            uriString = String.format("redis://%s:%d", host, Integer.parseInt(port));
        }
        // 创建Netty事件循环组
        EventLoopGroup eventLoopGroup = new NioEventLoopGroup(DEFAULT_IO_THREADS, new DefaultThreadFactory("lettuce-event-custom"));

        // 创建客户端资源并自定义Netty配置
        ClientResources clientResources = DefaultClientResources.builder()
                .eventExecutorGroup(eventLoopGroup)
                .build();
        // 创建Redis客户端
        RedisClient redisClient = RedisClient.create(clientResources, RedisURI.create(uriString));

        // 设置连接超时时间
        redisClient.setDefaultTimeout(Duration.ofMillis(DEFAULT_CONNECTION_TIMEOUT_MS));
        return redisClient;
    }


    public static StatefulRedisConnection<String, String> testConnection(DataSourceConfigVO configVO) {
        String host = configVO.getHost();
        String port = configVO.getPort();
        String username = configVO.getUsername();
        String password = configVO.getPassword();
        // 建立连接
        return createNow(host, port, username, password).connect();
    }

    public static Object execute(String dataSourceId, String command, String key, String value,Long ex) {
        if(!RedisConnectionPoolCache.getInstance().containsClientKey(dataSourceId)){
            RedisReadManager.createOrGetRedisConnection(dataSourceId);
        }
        StatefulRedisConnection<String, String> connection = RedisConnectionPoolCache.getInstance().getConnection(dataSourceId);
        if (connection == null) {
            throw new RuntimeException("Redis连接不存在");
        }

        RedisCommands<String, String> commands = connection.sync();
        if ("get".equals(command) && StringUtils.hasText(key)) {
            return commands.get(key);
        }
        if ("set".equals(command) && StringUtils.hasText(key)) {
            // 设置过期时间，例如设置为1小时（可根据实际需求调整）
            if(ex!=null){
                return commands.setex(key, ex, value);
            }
            return commands.set(key, value);
        }
        throw new RuntimeException("不支持的命令");

    }

    /**
     * 关闭redis资源
     *
     * @param dataSourceId 数据源id
     */
    public static void destroyRedisSource(String dataSourceId) {
        if (RedisConnectionPoolCache.getInstance().containsConnectionKey(dataSourceId)) {
            RedisConnectionPoolCache.getInstance().destroySingle(dataSourceId);
            RedisConnectionPoolCache.getInstance().removeClient(dataSourceId);
        }
    }


}
