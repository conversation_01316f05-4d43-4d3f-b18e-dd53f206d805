package sdata.ops.indicator.handler.source;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.config.http.HttpHandler;
import sdata.ops.common.core.model.HttpRequestModel;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

import java.util.Objects;

@Component("http")
public class HttpExecHandler implements ScriptExecutor {
    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        //序列化请求内容
        HttpRequestModel requestModel = JSONUtil.toBean(vo.getScript(), HttpRequestModel.class);
        //query参数替换
        requestModel.setQuery(replacerParams(requestModel.getQuery(), params));
        //header参数替换
        requestModel.setHeader(replacerParams(requestModel.getHeader(), params));
        //body参数替换
        requestModel.setBody(replacerParams(requestModel.getBody(), params));
        //authentication参数替换
        requestModel.setAuthentication(replacerParams(requestModel.getAuthentication(), params));
        return HttpHandler.sendHttpCallJson(requestModel);
    }

    /**
     * 参数替换
     *
     * @param input  配置内容
     * @param params 替换内容
     * @return json
     */
    private JSONObject replacerParams(JSONObject input, JSONObject params) {
        input.forEach((key, value) -> {
            if (Objects.nonNull(value)) {
                if (value instanceof String) {
                    String valueStr = value.toString().replace("${", "").replace("}", "");
                    if (params.containsKey(valueStr)) {
                        input.set(key, params.getStr(valueStr));
                    }else {
                        input.set(key, valueStr);
                    }
                }
            }
        });
        return input;
    }
}
