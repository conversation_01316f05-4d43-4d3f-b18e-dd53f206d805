package sdata.ops.indicator.config.js;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.graalvm.polyglot.Context;

/***
 * <AUTHOR>
 * 自定义js引擎context上下文对象缓存池工具，使用apache common pool工具包辅助完成
 * jsengine对象是非线程安全的，所以每个js代码块执行都需要创建一个对象，引擎对象比较重，初始化耗时，所以创建对象缓存池来使用
 */
public class ContextCache {

    private  static volatile ContextCache instance;
    private ContextGenericObjectPool contexts = null;

    private ContextCache() {
        init();
    }

    public static ContextCache getInstance() {
        if (instance == null) {
            synchronized (ContextCache.class) {
                if (instance == null) {
                    instance = new ContextCache();
                }
            }
        }
        return instance;
    }

    private void init() {
        GenericObjectPoolConfig<Context> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(8);
        config.setMinIdle(3);
        config.setMaxIdle(4);
        contexts = new ContextGenericObjectPool(new ContextCachePoolFactory(), config);
    }

    public Context getContext() {
        try {
            return contexts.borrowObject();
        } catch (Exception e) {
            throw new JsProcessException(e);
        }
    }

    public void returnContext(Context context) {
        contexts.returnObject(context);
    }

}
