package sdata.ops.indicator.handler.source;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;
import sdata.ops.base.indicator.model.dto.EMailFilterDTO;
import sdata.ops.base.indicator.model.dto.MessageDTO;
import sdata.ops.base.indicator.model.vo.ExecuteVO;
import sdata.ops.common.exception.ScriptException;
import sdata.ops.indicator.config.mail.MailManager;
import sdata.ops.indicator.handler.exec.ScriptExecutor;

import java.util.List;

@Component("mailMatch")
public class MailMatchHandler implements ScriptExecutor {
    @Override
    public Object execute(ExecuteVO vo, JSONObject params) {
        EMailFilterDTO filters = JSONUtil.toBean(vo.getScript(), EMailFilterDTO.class);
        if (!(params.get("data") instanceof List)) {
            throw new ScriptException("邮件处理器异常: 传入参数类型不兼容");
        }
        List<MessageDTO> mail =  params.getBeanList("data",MessageDTO.class);
        List<MessageDTO> match = MailManager.ruleValidate(mail, filters);
        JSONObject res=new JSONObject();
        res.set("status",!match.isEmpty());
        res.set("data",match);
        return res;
    }

}
