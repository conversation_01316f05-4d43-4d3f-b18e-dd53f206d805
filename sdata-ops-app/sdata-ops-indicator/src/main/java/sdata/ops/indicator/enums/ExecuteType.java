package sdata.ops.indicator.enums;

import sdata.ops.indicator.handler.exec.ScriptExecutor;
import sdata.ops.indicator.handler.exec.SqlScriptHandler;
import sdata.ops.indicator.handler.source.ExpressHandler;
import sdata.ops.indicator.handler.source.JavaScriptHandler;
import sdata.ops.indicator.handler.source.JavaSourceHandler;
import sdata.ops.indicator.handler.source.MailMatchHandler;

import java.util.Optional;

public enum ExecuteType {
    SQL_READ("sql", SqlScriptHandler.class),
    JS_SCRIPT("javascript", JavaScriptHandler.class),
    JAVA_SCRIPT("java", JavaSourceHandler.class),
    MAIL_FILTER("mail", MailMatchHandler.class),
    EXPRESS_MATCH("express", ExpressHandler.class);

    /**
     * 注入spring中的beanName
     */
    private final String type;

    /**
     * 配置类节点实现类class
     */
    private final Class<? extends ScriptExecutor> classConfig;

    ExecuteType(String type, Class<? extends ScriptExecutor> classConfig) {
        this.type = type;
        this.classConfig = classConfig;
    }

    public static Optional<ExecuteType> getEnumByValue(String paramValue) {
        for (ExecuteType nodeTypeEnum : ExecuteType.values()) {
            String nodeType = nodeTypeEnum.getType();
            if (nodeType.equals(paramValue)) {
                return Optional.of(nodeTypeEnum);
            }
        }
        return Optional.empty();
    }

    public String getType() {
        return type;
    }

    public Class<? extends ScriptExecutor> getClassConfig() {
        return classConfig;
    }
}
