package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsQuartzJobLog;
import sdata.ops.base.indicator.model.entity.OpsWorkflowLogSummary;

/**
* <AUTHOR>
* @description 针对表【ops_quartz_job_log(定时任务调度日志表)】的数据库操作Service
* @createDate 2025-07-28 19:20:32
*/
public interface OpsQuartzJobLogService extends IService<OpsQuartzJobLog> {

    OpsWorkflowLogSummary wfDetail(String executionId);
}
