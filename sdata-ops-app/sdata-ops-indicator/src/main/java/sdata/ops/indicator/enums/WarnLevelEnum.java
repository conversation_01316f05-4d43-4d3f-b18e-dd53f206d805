package sdata.ops.indicator.enums;

import lombok.Getter;

@Getter
public enum WarnLevelEnum {
    //    TRACE("跟踪"),
    //    DEBUG("通知"),
    //    INFO("信息"),
    //    WARN("警告"),
    //    ERROR("错误"),
    //    FATAL("严重"),
    NOTICE("通知"),
    WARNING("警告"),
    CRITICAL("重要"),
    URGENT("紧急"),
    ;
    private final String chnName;

    WarnLevelEnum(String chnName) {
        this.chnName = chnName;
    }
}
