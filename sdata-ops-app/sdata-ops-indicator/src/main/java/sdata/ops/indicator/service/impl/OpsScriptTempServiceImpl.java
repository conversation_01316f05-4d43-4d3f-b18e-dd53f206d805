package sdata.ops.indicator.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsScriptTemp;
import sdata.ops.indicator.mapper.OpsScriptTempMapper;
import sdata.ops.indicator.service.OpsScriptTempService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【OPS_SCRIPT_TEMP(脚本执行结果暂存表)】的数据库操作Service实现
 * @createDate 2024-09-23 14:49:25
 */
@Service
public class OpsScriptTempServiceImpl extends ServiceImpl<OpsScriptTempMapper, OpsScriptTemp>
        implements OpsScriptTempService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int scriptIdInsertAndQueryCounter(String thirdId, String indicatorId) {
        String nowDay = DateUtil.format(new Date(), "yyyyMMdd");
        LambdaUpdateWrapper<OpsScriptTemp> query = new LambdaUpdateWrapper<>();
        query.eq(OpsScriptTemp::getIndicatorId, indicatorId).eq(OpsScriptTemp::getDayVal, nowDay);
        List<OpsScriptTemp> ls = list(query);
        if (ls.isEmpty()) {
            OpsScriptTemp item = new OpsScriptTemp();
            item.setCreateTime(new Date());
            item.setDayVal(nowDay);
            item.setThirdId(thirdId);
            item.setIndicatorId(Long.valueOf(indicatorId));
            save(item);
            return 1;
        }
        if (ls.stream().anyMatch(i -> i.getThirdId().equals(thirdId))) {
            return ls.size();
        } else {
            OpsScriptTemp item = new OpsScriptTemp();
            item.setCreateTime(new Date());
            item.setDayVal(nowDay);
            item.setThirdId(thirdId);
            item.setIndicatorId(Long.valueOf(indicatorId));
            save(item);
            return ls.size() + 1;
        }
    }

}




