package sdata.ops.indicator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.entity.OpsWarnReadLog;
import sdata.ops.indicator.mapper.OpsWarnReadLogMapper;
import sdata.ops.indicator.service.OpsWarnReadLogService;

import java.util.Date;

@Service
public class OpsWarnReadLogServiceImpl extends ServiceImpl<OpsWarnReadLogMapper, OpsWarnReadLog> implements OpsWarnReadLogService {
    @Override
    public void addNew(String warnId, String userId) {
        var readLog = new OpsWarnReadLog();
        readLog.setWarnId(warnId);
        readLog.setReadUserId(userId);
        readLog.setReadAt(new Date());
        super.save(readLog);
    }

    @Override
    public void removeOld(String warnId, String userId) {
        lambdaUpdate()
                .eq(OpsWarnReadLog::getWarnId, warnId)
                .eq(userId != null, OpsWarnReadLog::getReadUserId, userId)
                .remove();
    }
}
