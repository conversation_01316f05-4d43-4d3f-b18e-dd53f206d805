package sdata.ops.indicator.handler.source;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import sdata.ops.base.indicator.model.dto.OaSecretDTO;
import sdata.ops.base.indicator.model.entity.OpsThirdAuthInfo;
import sdata.ops.common.core.util.RedisUtil;
import sdata.ops.indicator.service.OpsThirdAuthInfoService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取oa接口前置，token获取处理类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OaTokenObtHandler {


    private final ApplicationContext context;

    private final OpsThirdAuthInfoService infoService;


    @Value("${third.oa.url}")
    public String url;

    @Value("${third.oa.appid:null}")
    public String appId;

    private final RedisUtil redisUtil;

    public final static Map<String,String> cap=new HashMap<>();

    public String replaceTokenStr(String metaStr){
       return metaStr.replace("${oa.token}",obtToken());
    }

    public String checkActiveProfile() {
        String[] profiles = context.getEnvironment().getActiveProfiles();
        List<String> arr = List.of(profiles);
        if (arr.isEmpty() || arr.contains("dev")) {
            return "dev";
        }
        return "prod";
    }

    public String obtToken() {
        //先从缓存中获取取不到,再从数据中或者接口进行认证获取
        String token = (String) redisUtil.get("oa.token");
        if (StringUtils.hasText(token)) {
            return token;
        }
        //进入同步块区域
        synchronized (OaTokenObtHandler.class) {
            //再取一次
            String tokenCache = (String) redisUtil.get("oa.token");
            if (StringUtils.hasText(tokenCache)) {
                return tokenCache;
            }
            //取不到再去获取更新
            String env = checkActiveProfile();
            //获取spk
            OaSecretDTO sec = obtSpkAndSecret(env);
            String sect = sec.getSecret();
            String spl = sec.getSpk();
            cap.put("secret",sect);
            cap.put("spk",spl);
            RSA rsa = new RSA(null, spl);
            String enc = rsa.encryptBase64(sect, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);
            log.info("公钥加密后的数据{}", enc);
            log.info("开始请求.....token");
            try (HttpResponse response = HttpRequest.post(url + "/api/ec/dev/auth/applytoken")
                    .header("appid", appId)
                    .header("secret", enc).execute()) {
                log.info("token响应内容");
                String ks = response.body();
                log.info(ks);
                JSONObject tokenObj = JSONUtil.parseObj(ks);
                if (tokenObj.getInt("code") == 0) {
                    log.info("请求token获取成功");
                    String tokenFast = tokenObj.getStr("token");
                    log.info("token-信息: {}", tokenFast);
                    redisUtil.set("oa.token", tokenFast, 1800L);
                    return tokenFast;
                }

            }
            return null;
        }
    }

    //先获取密钥和公钥
    private synchronized OaSecretDTO obtSpkAndSecret(String env) {
        log.info("先查询数据库中是否存在spk信息");
        List<OpsThirdAuthInfo> infos = infoService.list(Wrappers.lambdaQuery(OpsThirdAuthInfo.class)
                .eq(OpsThirdAuthInfo::getCode, "oa").eq(OpsThirdAuthInfo::getEnv, env));
        if (!infos.isEmpty()) {
            log.info("数据库已缓存secret与spk信息");
            OpsThirdAuthInfo single = infos.get(0);
            OaSecretDTO dto = new OaSecretDTO();
            JSONObject obj = JSONUtil.parseObj(single.getContent());
            dto.setSecret(obj.getStr("secret"))
                    .setSpk(obj.getStr("spk"));
            return dto;
        }
        log.info("token生成测试请求开始.......");
        //1.获取认证注册信息
        try (HttpResponse response = HttpRequest.post(url + "/api/ec/dev/auth/regist")
                .header("appid", appId)
                .header("cpk", "sczq").execute()) {
            if (response.isOk()) {
                log.info("完成请求: {}", response.body());
                JSONObject res = JSONUtil.parseObj(response.body());
                if (res.getInt("code") == 0 && res.getBool("status")) {
                    log.info("secret与spk认证获取成功");
                    OpsThirdAuthInfo ins = new OpsThirdAuthInfo();
                    ins.setCode("oa");
                    ins.setEnv(env);
                    ins.setContent(JSONUtil.toJsonStr(res));
                    OaSecretDTO dto = new OaSecretDTO();
                    dto.setSecret(res.getStr("secret"))
                            .setSpk(res.getStr("spk"));
                    //入库
                    infoService.save(ins);
                    return dto;
                }
            }else {
                log.error("oa-token-获取-完成请求: {}", response.body());

                throw new RuntimeException("获取oa 认证注册 secret与spk 失败");

            }
        }
        throw new RuntimeException("获取oa 认证注册 secret与spk 失败");
    }

}
