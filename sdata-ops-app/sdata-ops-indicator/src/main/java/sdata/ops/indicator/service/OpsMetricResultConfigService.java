package sdata.ops.indicator.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.base.indicator.model.vo.SqlColumnInfoVO;

import java.util.List;

/**
* <AUTHOR>
*/
public interface OpsMetricResultConfigService extends IService<OpsMetricResultConfig> {
    /**
     * 表头数据
     *
     * @param metricId 指标id
     * @return map<字段名, 字段展示名>
     */
    List<SqlColumnInfoVO> tableHead(String metricId);
}
