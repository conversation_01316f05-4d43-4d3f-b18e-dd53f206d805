package sdata.ops.indicator.config.db;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.base.indicator.model.vo.DataSourceConfigVO;
import sdata.ops.indicator.service.OpsDataSourceService;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class DbClientManager {

    private final OpsDataSourceService dataSourceService;

    public JdbcTemplate query(String dataSourceId) {
        //1.查看数据源是否有该数据源对象
        if (!DataBasePoolCache.getInstance().containsKey(dataSourceId)) {
            DataSourceConfigVO configVO=queryDataSourceVO(dataSourceId);
            ExternalDataBaseManager.getExternalDataSource(configVO);
        }
        //2.返回  操作对象
        return DataBasePoolCache.getInstance().getJdbcTemplate(dataSourceId);
    }

    private DataSourceConfigVO queryDataSourceVO(String dataSourceId) {
        OpsDataSource source = dataSourceService.getById(dataSourceId);
        DataSourceConfigVO configVO = JSONUtil.toBean(JSONUtil.parseObj(source.getSourceConfig()), DataSourceConfigVO.class);
        configVO.setSourceType(source.getSubSourceType());
        configVO.setSubSourceType(source.getSubSourceType());
        configVO.setDataSourceId(source.getId());
        return configVO;
    }


    private String replacerVariable(JSONObject params, String sqlScript) {
        String res = sqlScript;
        for (Map.Entry<String, Object> p : params) {
            res = StringUtils.replace(sqlScript, "${" + p.getKey() + "}", p.getValue().toString());
        }
        return res;
    }



}
