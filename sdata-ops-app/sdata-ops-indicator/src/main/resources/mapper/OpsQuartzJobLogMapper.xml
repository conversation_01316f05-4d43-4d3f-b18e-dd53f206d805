<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsQuartzJobLogMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.indicator.model.entity.OpsQuartzJobLog">
            <id property="id" column="job_log_id" jdbcType="BIGINT"/>
            <result property="jobName" column="job_name" jdbcType="VARCHAR"/>
            <result property="jobGroup" column="job_group" jdbcType="VARCHAR"/>
            <result property="invokeTarget" column="invoke_target" jdbcType="VARCHAR"/>
            <result property="jobMessage" column="job_message" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="exceptionInfo" column="exception_info" jdbcType="VARCHAR"/>
            <result property="startTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="stopTime" column="create_time" jdbcType="TIMESTAMP"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,job_name,job_group,
        invoke_target,job_message,status,
        exception_info,start_time,stop_time
    </sql>
</mapper>
