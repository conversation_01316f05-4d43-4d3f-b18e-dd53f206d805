<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsWarnDataTodayMapper">

    <select id="workbenchList" resultType="sdata.ops.base.indicator.model.vo.WarnBenchVo">
        select
        id,
        warn_group_id,
        warn_name,
        if((
            select owrl.read_at from sdata_ops.ops_warn_read_log owrl
            where owrl.warn_id = owi.id and read_user_id = #{userId} and deleted = 0
        ) is null, 0, 1) as has_read
        from sdata_ops.ops_warn_info owi
        where owi.id in ( SELECT distinct warn_id FROM sdata_ops.ops_warn_data_today where deleted = 0 )
        <if test="warnGroupId!=null and warnGroupId!=''">
            and owi.warn_group_id = #{warnGroupId}
        </if>
        <if test="warnName!=null and warnName!=''">
            and owi.warn_name like concat('%', #{warnName}, '%')
        </if>
    </select>
</mapper>
