<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsWarnGroupMapper">

    <select id="groupList" resultType="sdata.ops.base.indicator.model.dto.WarnGroupListVO">
        select id,pid,group_name,order_sort,
        (select count(*) from ops_warn_info owi where owi.warn_group_id = owg.id  AND owi.deleted = 0) as count
        from ops_warn_group owg
        <where>
            and deleted = 0
        </where>
        ORDER BY owg.order_sort
    </select>
</mapper>
