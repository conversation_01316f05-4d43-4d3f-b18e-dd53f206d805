<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsSpecThirdInfoMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
            <result property="thirdId" column="THIRD_ID" jdbcType="VARCHAR"/>
            <result property="dataId" column="DATA_ID" jdbcType="VARCHAR"/>
            <result property="content" column="CONTENT" jdbcType="VARCHAR"/>
            <result property="state" column="STATE" jdbcType="INTEGER"/>
            <result property="operationId" column="OPERATION_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="bizDate" column="BIZ_DATE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,thrid_id,
        data_id,content,state,
        oper_id,create_time,biz_date,
        meta_info
    </sql>
    <select id="scriptObtUnlessInfo" resultType="sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo">
        with c1 as
                 ( select *
                   from ops_spec_third_info
                   where user_id=#{userid}
                     and biz_date=#{bizdate}
                 )
                ,
             c2 as
                 (select *
                  from ops_script_temp
                  where indicator_id=#{indicatorid}
                    and day_val=#{bizdate}
                 )
        select c1.data_id,
               c1.content,
               c2.indicator_id
        from c1
                 left join c2
                           on c1.data_id=c2.third_id
        where c2.indicator_id is null
    </select>
</mapper>
