<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsModelTableInfoMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.indicator.model.entity.OpsModelTableInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="datasourceId" column="datasource_id" jdbcType="BIGINT"/>
            <result property="tableName" column="table_name" jdbcType="VARCHAR"/>
            <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createBy" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,datasource_id,table_name,
        category_id,description,status,
        create_user,create_time,update_time
    </sql>
    <select id="pageCustom" resultType="sdata.ops.base.indicator.model.entity.OpsModelTableInfo">
        select  a.* ,b.counts,c.`name` as category_name,d.`source_name` as data_source_name from (select  id,datasource_id,table_name,
                                                                                                          category_id,description,status,
                                                                                                          create_by,create_time,update_time
                                                                                                  from ops_model_table_info ${ew.customSqlSegment}
                                                                                   ) as a
                                                                                                     left join (SELECT table_id,count(1) AS counts FROM ops_model_table_column GROUP BY table_id )
            b on a.id=b.table_id
                                                                                                     left join ops_model_table_category c on a.category_id=c.`id`

                                                                                                     left join ops_data_source d on a.datasource_id=d.`id`
    </select>
</mapper>
