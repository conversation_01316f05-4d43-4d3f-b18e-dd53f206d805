<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.indicator.mapper.OpsQuartzJobMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.indicator.model.entity.OpsQuartzJob">
            <id property="id" column="job_id" jdbcType="BIGINT"/>
            <id property="jobName" column="job_name" jdbcType="VARCHAR"/>
            <id property="jobGroup" column="job_group" jdbcType="VARCHAR"/>
            <result property="invokeTarget" column="invoke_target" jdbcType="VARCHAR"/>
            <result property="cronExpression" column="cron_expression" jdbcType="VARCHAR"/>
            <result property="misfirePolicy" column="misfire_policy" jdbcType="VARCHAR"/>
            <result property="concurrent" column="concurrent" jdbcType="CHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="bindWfId" column="bind_wf_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,job_name,job_group,
        invoke_target,cron_expression,misfire_policy,
        concurrent,status,create_by,
        create_time,update_by,update_time,bind_wf_id,
        remark
    </sql>
</mapper>
