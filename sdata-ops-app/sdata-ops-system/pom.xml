<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sdata-ops-app</artifactId>
        <groupId>sdata.ops.platform</groupId>
        <version>24.3.1</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>sdata-ops-system</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-common</artifactId>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-flow-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-system-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-api</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-indicator-model</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>sdata.ops.platform</groupId>
            <artifactId>sdata-ops-system-model</artifactId>
            <version>24.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!-- Apache POI for Excel manipulation -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
    </dependencies>
</project>