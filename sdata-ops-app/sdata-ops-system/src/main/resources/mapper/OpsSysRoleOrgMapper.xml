<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysRoleOrgMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysRoleOrg">
            <id property="roleId" column="role_id" jdbcType="BIGINT"/>
            <id property="orgId" column="org_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,org_id
    </sql>
    <delete id="deleteRoleDeptByRoleId" parameterType="Long">
        delete from ops_sys_role_org where role_id=#{roleId}
    </delete>

    <select id="selectCountRoleDeptByDeptId" resultType="Integer">
        select count(1) from ops_sys_role_org where dept_id=#{deptId}
    </select>

    <delete id="deleteRoleDept" parameterType="Long">
        delete from ops_sys_role_org where role_id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <insert id="batchRoleDept">
        insert into ops_sys_role_org(role_id, dept_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.deptId})
        </foreach>
    </insert>
</mapper>
