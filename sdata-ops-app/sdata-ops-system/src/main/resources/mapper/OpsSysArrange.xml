<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysArrangeMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysArrange">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="shiftId" column="shift_id" jdbcType="VARCHAR"/>
        <result property="date" column="date" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,org_id,user_id,shift_id,date,create_by,
        create_time,update_by,update_time
    </sql>

    <delete id="del">
        DELETE
        FROM ops_sys_arrange
        WHERE ops_sys_arrange.org_id = #{opsSysArrange.orgId}
          AND ops_sys_arrange.shift_id = #{opsSysArrange.shiftId}
          AND ops_sys_arrange.date = #{opsSysArrange.date}
    </delete>

    <delete id="delByDate">
        DELETE
        FROM ops_sys_arrange
        WHERE ops_sys_arrange.date &gt;= #{opsSysArrangedDTO.startDate}
          AND ops_sys_arrange.date &lt;= #{opsSysArrangedDTO.endDate}
          AND ops_sys_arrange.org_id = #{opsSysArrangedDTO.orgId}
          AND ops_sys_arrange.shift_id = #{opsSysArrangedDTO.shiftId}
          AND ops_sys_arrange.shift_id = #{opsSysArrangedDTO.userId}
    </delete>

    <select id="findByShiftId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ops_sys_arrange
        WHERE shift_id = #{id}
    </select>

    <select id="list" resultType="sdata.ops.base.system.model.vo.OpsSysArrangeVO">
        SELECT
        ops_sys_arrange.id,
        ops_sys_arrange.org_id,
        ops_sys_arrange.user_id,
        ops_sys_arrange.shift_id,
        ops_sys_arrange.date,
        ops_sys_arrange.create_by,
        ops_sys_arrange.create_time,
        ops_sys_arrange.update_by,
        ops_sys_arrange.update_time,
        ops_sys_shift.name shiftName,
        ops_sys_shift.sort,
        ops_sys_user.name userName
        FROM ops_sys_arrange
        LEFT JOIN ops_sys_shift ON ops_sys_arrange.shift_id = ops_sys_shift.id
        LEFT JOIN ops_sys_user ON ops_sys_arrange.user_id = ops_sys_user.id
        <where>
            <if test="id != null and id != ''">
                AND ops_sys_arrange.org_id = #{id}
            </if>
            <if test="startDate != null and startDate != ''">
                AND ops_sys_arrange.date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND ops_sys_arrange.date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="findByOrgShiftUserDate" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ops_sys_arrange
        WHERE ops_sys_arrange.org_id = #{opsSysArrange.orgId}
          AND ops_sys_arrange.shift_id = #{opsSysArrange.shiftId}
          AND ops_sys_arrange.user_id = #{opsSysArrange.userId}
          AND ops_sys_arrange.date = #{opsSysArrange.date}
    </select>

</mapper>
