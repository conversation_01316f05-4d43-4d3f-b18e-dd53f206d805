<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsTaskFundInfoMapper">

    <resultMap id="baseResultMap" type="java.util.Map">
        <result column="total" property="total" jdbcType="BIGINT"></result>
        <result column="month" property="month" jdbcType="VARCHAR"></result>
    </resultMap>

    <resultMap id="deptResultMap" type="java.util.Map">
        <result column="total" property="total" jdbcType="BIGINT"></result>
        <result column="owner_org_id" property="ownerOrgId" jdbcType="VARCHAR"></result>
        <result column="name" property="orgName" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="getTaskFundList" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select
            * from
        (select id,complete_status ,task_replica_id, start_date, end_date,fund_name from ops_task_fund_info where task_replica_id in
                (select id from ops_task_attr_basic_replica where deleted = 0
                    <if test="sign == 0">
                        and owner_org_id in
                        <foreach collection="deptIds" separator="," item="c" open="(" close=")">
                            #{c}
                        </foreach>
                    </if>
                )
                and start_date is not null
                and end_date is not null
                and start_date  <![CDATA[<=]]> timestampadd(day,-10,#{date})
                and end_date >= timestampadd(day,-10,#{date})
        union all
        select id,complete_status,task_replica_id, start_date, end_date,fund_name from ops_task_fund_info where task_replica_id in
                (select id from ops_task_attr_basic_replica where deleted = 0
                    <if test="sign == 0">
                        and owner_org_id in
                        <foreach collection="deptIds" separator="," item="c" open="(" close=")">
                            #{c}
                        </foreach>
                    </if>
                )
                and start_date is not null
                and end_date is not null
                and start_date  <![CDATA[<=]]> timestampadd(day,-10,#{date})
                and end_date <![CDATA[<]]>  timestampadd(day,-10,#{date})
                and complete_status = '1'
            ) as fx
        where fx.TASK_REPLICA_ID in (
            select
                c.ID
            from ops_task_template a
            left join ops_task_template_relation b
                on a.id = b.template_id
            left join ops_task_attr_basic_replica c
                on b.task_replica_id = c.id
            where a.template_status = 1
                and c.import_status = 1
        )
    </select>

    <select id="getTaskFundListByLeader" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select
        * from
        (select id,complete_status ,task_replica_id, start_date, end_date,fund_name from ops_task_fund_info where task_replica_id in
        (select id from ops_task_attr_basic_replica where deleted = 0 and task_priority=1
        )
        and start_date is not null
        and end_date is not null
        and start_date  <![CDATA[<=]]> timestampadd(day,-10,#{date})
        and end_date >= timestampadd(day,-10,#{date})
        union all
        select id,complete_status,task_replica_id, start_date, end_date,fund_name from ops_task_fund_info where task_replica_id in
        (select id from ops_task_attr_basic_replica where deleted = 0 and task_priority=1
        )
        and start_date is not null
        and end_date is not null
        and start_date  <![CDATA[<=]]> timestampadd(day,-10,#{date})
        and end_date <![CDATA[<]]>  timestampadd(day,-10,#{date})
        and complete_status = '1'
        ) as fx
        where fx.task_replica_id in (
        select
        c.id
        from ops_task_template a
        left join ops_task_template_relation b
        on a.id = b.template_id
        left join ops_task_attr_basic_replica c
        on b.task_replica_id = c.id
        where a.template_status = 1
        and c.import_status = 1
        )
    </select>

    <select id="getMonthFund" resultMap="baseResultMap">
        select ifnull(count(*),0) total,month(t1.end_date) month from ops_task_attr_basic_replica t2
        left join ops_task_fund_info t1 on t1.task_replica_id = t2.id and t2.import_status = 1
        where (t1.complete_status = 1 or t1.complete_status = 3) and (t1.end_date BETWEEN #{startDate} and #{endDate})
        <if test="ids != null and ids.size() > 0">
            and owner_org_id in
            <foreach collection="ids" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        and t2.id in ( select c.id
        from ops_task_template a
        left join ops_task_template_relation b on a.id = b.template_id
        left join ops_task_attr_basic_replica c on b.task_replica_id = c.id
        where a.template_status = 1
        and c.import_status = 1)
        group by month(t1.end_date)
    </select>

    <select id="getDeptFund" resultMap="deptResultMap">
        select ifnull(count(*), 0) as total, t2.owner_org_id
        from ops_task_attr_basic_replica t2
        left join ops_task_fund_info t1 on t1.task_replica_id = t2.id and t2.import_status = 1
        where year(t1.end_date) = #{year} and month(t1.end_date) = #{month} and (t1.complete_status = 1 or
        t1.complete_status = 3)
        <if test="ids != null and ids.size() > 0">
            and owner_org_id in
            <foreach collection="ids" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        and t2.id in ( select c.id
        from ops_task_template a
        left join ops_task_template_relation b on a.id = b.template_id
        left join ops_task_attr_basic_replica c on b.task_replica_id = c.id
        where a.template_status = 1
        and c.import_status = 1)
        group by t2.owner_org_id
    </select>

    <select id="getDelayMonthFund" resultMap="baseResultMap">
        select ifnull(count(*),0) total,month(t1.end_date) month from ops_task_attr_basic_replica t2
        left join ops_task_fund_info t1 on t1.task_replica_id = t2.id and t2.import_status = 1
        where t1.complete_status = 1 and t1.end_date &lt; now() and (t1.end_date BETWEEN #{startDate} and #{endDate})
        <if test="ids != null and ids.size() > 0">
            and owner_org_id in
            <foreach collection="ids" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        group by month(t1.end_date)
    </select>

    <select id="getFundByDate" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select *
        from ops_task_fund_info
        where start_date is not null
          and end_date is not null
          and start_date <![CDATA[<=]]> #{date}
          and end_date >= #{date}
          and task_replica_id = #{taskReplicaId}
        union
        select *
        from ops_task_fund_info
        where (start_date is null or end_date is null or start_date = '' or end_date = '')
          and task_replica_id = #{taskReplicaId}
        union
        select *
        from ops_task_fund_info
        where start_date is not null
          and end_date is not null
          and task_replica_id = #{taskReplicaId}
          and complete_status = '1'
          and #{date} > end_date
    </select>
    <select id="queryMergeForDashboard" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select *
        from (select *
              from ops_task_fund_info
              where start_date is not null
                and end_date is not null
                and start_date <![CDATA[<=]]> #{date}
                and end_date >= #{date}
              union
              select *
              from ops_task_fund_info
              where (start_date is null or end_date is null)
              union
              select *
              from ops_task_fund_info
              where start_date is not null
                and end_date is not null
                and complete_status = '1'
                and #{date} > end_date) as co1 where co1.task_replica_id in
                                                <foreach collection="ids" item="k" separator="," open="(" close=")" >
                                                    #{k}
                                                </foreach>
    </select>
    <select id="queryFundInfoForMonitor" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select fx.*,fnx.temp_id as task_bind_template_id
        from (select t1.*,
                     t2.task_owner_id,
                     t2.task_owner_type,
                     t2.task_owner_val,
                     t2.owner_org_id
              from (select *
                    from ops_task_fund_info
                    where start_date is not null
                      and end_date is not null
                      and start_date <![CDATA[<=]]> #{date}
                      and end_date = #{date})
                       as t1
                       LEFT JOIN ops_task_attr_basic_replica t2
                                 on t1.task_replica_id = t2.ID) as fx  INNER join
 ( select c.id, a.id as temp_id
            from ops_task_template a
       left join ops_task_template_relation b
              on a.id = b.template_id
       left join ops_task_attr_basic_replica c
              on b.task_replica_id = c.id
           where a.template_status = 1
             and c.import_status = 1 ) as fnx on fx.task_replica_id=fnx.id
    </select>
    <select id="queryFundInfoForMonitorWran" resultType="sdata.ops.base.system.model.entity.OpsTaskFundInfo">
        select fx.*,fnx.temp_id as task_bind_template_id

        from (select t1.*,
                     t2.task_owner_id,
                     t2.task_owner_type,
                     t2.task_owner_val,
                     t2.owner_org_id
              from (
                    select *
                    from ops_task_fund_info
                    where start_date is not null
                      and end_date is not null
                      and complete_status = '1'
                      and #{date} > end_date)
                       as t1
                       left join ops_task_attr_basic_replica t2
                                 on t1.task_replica_id = t2.id) as fx  inner join
             ( select c.id, a.id as temp_id
               from ops_task_template a
                        left join ops_task_template_relation b
                                  on a.id = b.template_id
                        left join ops_task_attr_basic_replica c
                                  on b.task_replica_id = c.id
               where a.template_status = 1
                 and c.import_status = 1 ) as fnx on fx.task_replica_id=fnx.id
    </select>
</mapper>