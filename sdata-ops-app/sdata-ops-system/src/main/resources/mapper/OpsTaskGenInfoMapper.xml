<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsTaskGenInfoMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="taskProcessStatus" column="task_process_status" jdbcType="TINYINT"/>
        <result property="taskCompleteStatus" column="task_complete_status" jdbcType="TINYINT"/>
        <result property="taskCompleteDesc" column="task_complete_desc" jdbcType="VARCHAR"/>
        <result property="taskRef" column="task_ref" jdbcType="TINYINT"/>
        <result property="taskTransferStatus" column="task_transfer_status" jdbcType="TINYINT"/>
        <result property="taskTransferUserId" column="task_transfer_user_id" jdbcType="VARCHAR"/>
        <result property="taskTransferDesc" column="task_transfer_desc" jdbcType="VARCHAR"/>
        <result property="taskCheckStatus" column="task_check_status" jdbcType="TINYINT"/>
        <result property="taskCheckDesc" column="task_check_desc" jdbcType="VARCHAR"/>
        <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
        <result property="taskProgress" column="task_progress" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerType" column="task_trigger_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerId" column="task_trigger_id" jdbcType="VARCHAR"/>
        <result property="taskCronVal" column="task_cron_val" jdbcType="VARCHAR"/>
        <result property="taskDesc" column="task_desc" jdbcType="VARCHAR"/>
        <result property="taskCompleteType" column="task_complete_type" jdbcType="VARCHAR"/>
        <result property="taskCompleteUnitId" column="task_complete_unit_id" jdbcType="VARCHAR"/>
        <result property="taskAuditType" column="task_audit_type" jdbcType="CHAR"/>
        <result property="taskAuditUnitId" column="task_audit_unit_id" jdbcType="VARCHAR"/>
        <result property="taskWarnNotice" column="task_warn_notice" jdbcType="VARCHAR"/>
        <result property="taskPriority" column="task_priority" jdbcType="CHAR"/>
        <result property="taskLevel" column="task_level" jdbcType="CHAR"/>
        <result property="taskAttachmentsType" column="task_attachments_type" jdbcType="CHAR"/>
        <result property="taskOwnerType" column="task_owner_type" jdbcType="CHAR"/>
        <result property="taskOwnerId" column="task_owner_id" jdbcType="VARCHAR"/>
        <result property="taskOwnerVal" column="task_owner_val" jdbcType="VARCHAR"/>
        <result property="taskCheckReq" column="task_check_req" jdbcType="CHAR"/>
        <result property="taskCheckType" column="task_check_type" jdbcType="CHAR"/>
        <result property="taskCheckId" column="task_check_id" jdbcType="VARCHAR"/>
        <result property="taskCheckVal" column="task_check_val" jdbcType="VARCHAR"/>
        <result property="taskStartTime" column="task_start_time" jdbcType="TIMESTAMP"/>
        <result property="taskEndTime" column="task_end_time" jdbcType="TIMESTAMP"/>
        <result property="taskTags" column="task_tags" jdbcType="VARCHAR"/>
        <result property="taskAuthType" column="task_auth_type" jdbcType="CHAR"/>
        <result property="taskAuthId" column="task_auth_id" jdbcType="VARCHAR"/>
        <result property="taskAuthVal" column="task_auth_val" jdbcType="VARCHAR"/>
        <result property="taskAuthScope" column="task_auth_scope" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,parent_id,task_process_status,
        task_complete_status,task_complete_desc,task_ref,
        task_transfer_status,task_transfer_user_id,task_transfer_desc,
        task_audit_status,task_check_desc,task_name,
        task_progress,task_type,task_trigger_type,
        task_trigger_id,task_cron_val,task_desc,
        task_complete_type,task_complete_unit_id,task_audit_type,
        task_audit_unit_id,task_warn_notice,task_priority,
        task_level,task_attachments_type,task_owner_type,
        task_owner_id,task_owner_val,task_check_req,
        task_check_type,task_check_id,task_check_val,
        task_start_time,task_end_time,task_tags,
        task_auth_type,task_auth_id,task_auth_val,
        task_auth_scope,create_time,create_by,
        update_time,update_by,deleted,owner_org_id,indicator_id,
        check_org_id,complete_time,audit_time,task_start_threshold,task_end_threshold,work_amount,work_amonut_flag,
        process_definition_id,process_instance_id
    </sql>
    <update id="updateTaskCompleteStatus">
        update ops_task_gen_info set task_complete_status=#{st},complete_time=#{date},update_by=#{userId}
        ,TASK_COMPLETE_DESC=#{desVal}
        <if test="state!=null">
            , delay_state=#{state}
        </if>
        <if test="count!=null">
            ,WORK_AMOUNT=#{count}
        </if>

        where id=#{id}
    </update>
    <update id="updateTaskCompleteStatusByBatch">
        update ops_task_gen_info set task_complete_status=#{st},complete_time=#{date},update_by=#{userId}
        ,task_complete_desc=#{desVal},delay_state=#{state} where id in
        <foreach collection="ids" item="k" separator="," close=")" open="(">
            #{k}
        </foreach>

    </update>
    <update id="updateSingleTaskOperationInfo">
        update ops_task_gen_info
        set operation_complete_id=#{userId}
        where id = #{id}
          and operation_complete_id is null
    </update>
    <update id="updateBatchTaskOperationInfo">
        update ops_task_gen_info set operation_complete_id=#{userId} where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
        and operation_complete_id is null;
    </update>
    <update id="resetOperationId">
        update ops_task_gen_info set operation_complete_id=null where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
    </update>
    <update id="resetOperationCheckId">
        update ops_task_gen_info set operation_check_id=null where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
    </update>
    <update id="updateOperationCheckInfo">
        update ops_task_gen_info set operation_check_id=#{userId} where id in
        <foreach collection="ids" item="k" separator="," open="(" close=")">
            #{k}
        </foreach>
        and operation_check_id is null;
    </update>
    <delete id="realDeleted">
        delete
        from ops_task_gen_info
        where task_gen_time = #{dateStr}
    </delete>

    <select id="pageCustom" parameterType="sdata.ops.base.system.model.dto.ConditionTaskDTO"
            resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select <include refid="comsql"></include> from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}) c1
        <if test="dc.type!=null and dc.type==3">
            where owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where
            (task_owner_type ='2' and task_owner_id=#{dc.userId}) or
            (task_owner_type='1' and task_owner_id in  <foreach collection="dc.postIds" separator="," item="c" open="("
                                                                close=")">
            #{c}
        </foreach>)

        </if>
    </select>
    <select id="countDependStatus" resultType="java.lang.Integer">
        select count(*) from ops_task_gen_info where task_complete_status != 3
        and task_complete_status != 5
        and deleted='0'
        and id in
        <foreach collection="dep" close=")" open="(" separator="," item="k">
            #{k}
        </foreach>
    </select>
    <select id="pageCustomAudit" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        SELECT
        <include refid="comsql"></include>
        from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        where task_complete_status=2
        <if test="dc.type!=null and dc.type==2">
            and check_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and
            (task_check_type ='2' and task_check_id=#{dc.userId}) or
            (task_check_type='1' and task_check_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)

        </if>
        ) ${ew.customSqlSegment}
    </select>
    <select id="findTasksForCheckRequire" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}
        and task_complete_status=2
        <if test="dc.type!=null and dc.type==2">
            and check_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and
            (task_check_type ='2' and task_check_id=#{dc.userId}) or
            (task_check_type='1' and task_check_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            )
        </if>
    </select>
    <select id="dashboardListTask" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        <include refid="comsql"></include>
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>

    </select>
    <select id="dashboardListTaskByMulti" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(parent_id = 0
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND deleted='0' )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND date_format(task_end_time,'%Y-%m-%d') >= #{genTime}
        AND task_deferred_type = '1'
        AND deleted='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'period'
        AND date_format(task_end_time,'%Y-%m-%d') >= #{genTime}
        AND date_format(task_start_time,'%Y-%m-%d') <![CDATA[<=]]> #{genTime}
        AND deleted='0'
        )
        )c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>
    </select>
    <select id="dashboardListTaskByMultiLeaf" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_child_ids is null
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND deleted='0' )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_deferred_type = '1'
        AND deleted='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status <![CDATA[<>]]> 3
        AND deleted='0'
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND date_format(complete_time,'%Y-%m-%d') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status = 3
        AND deleted='0'
        )
        )c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>

    <select id="dashboardListTaskByMultiLeafByLeader" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(task_child_ids is null
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND deleted='0'
        and TASK_PRIORITY=1    )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_deferred_type = '1'
        AND deleted='0'
        and TASK_PRIORITY=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status <![CDATA[<>]]> 3
        AND deleted='0'
        and TASK_PRIORITY=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (task_child_ids is null
        AND task_type = 'period'
        AND date_format(complete_time,'%Y-%m-%d') = #{genTime}
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND task_complete_status = 3
        AND deleted='0'
        and TASK_PRIORITY=1
        )
        ) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>

    <select id="dashboardListTaskCount" resultType="java.lang.Long">
        select id from (select
        <include refid="comsql"></include>
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where
            (c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId}) or
            (c1.task_owner_type='1' and c1.task_owner_id in  <foreach collection="dc.postIds" separator="," item="c" open="("
                                                                close=")">
            #{c}
        </foreach>)
        </if>
        )
    </select>
    <select id="taskGenInfoFindLeafAllStatus" resultType="java.lang.Long">
        select count(1)
        from ops_task_gen_info
        where parent_id = #{parentId}
          and deleted = '0'
          and id <![CDATA[<>]]> #{taskId}
          and task_complete_status <![CDATA[<>]]> 3
    </select>
    <select id="findChildDetailByAuth" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        <include refid="comsql"/>
        from (select
        <include refid="comsql"></include>
        from ops_task_gen_info ${ew.customSqlSegment}) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            <!--            where-->
            <!--            (task_Owner_Type ='2' and task_Owner_Id=#{dc.userId}) or-->
            <!--            (task_owner_type='1' and task_owner_id in-->
            <!--            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">-->
            <!--            #{c}-->
            <!--        </foreach>)-->
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>
    </select>
    <select id="listCustomAudit" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        SELECT
        <include refid="comsql"></include>
        from (
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        where task_complete_status=2
        <if test="dc.type!=null and dc.type==2">
            and check_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            and
            (task_check_type ='2' and task_check_id=#{dc.userId}) or
            (task_check_type='1' and task_check_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)

        </if>
        )  c1 ${ew.customSqlSegment}
    </select>
    <select id="allTaskForNowDay" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE( task_gen_time = #{date}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        and task_complete_status <![CDATA[<>]]> 5
        and task_child_ids is null
        AND deleted='0' )
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{date}
        AND task_end_time >= (#{date}||'22:00:00')
        AND task_deferred_type = '1'
        and task_complete_status <![CDATA[<>]]> 5
        and task_child_ids is null
        AND deleted='0' )
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'period'
        and task_child_ids is null
        and task_complete_status <![CDATA[<>]]> 5
        and task_complete_status != 3
        AND date_format(task_end_time,'%Y-%m-%d') = #{date}
        AND deleted='0')
        union all
        SELECT
        <include refid="shortField"></include>
        FROM ops_task_gen_info
        WHERE ( task_type = 'period'
        and task_child_ids is null
        and task_complete_status <![CDATA[<>]]> 5
        and task_complete_status = 3
        AND date_format(complete_time,'%Y-%m-%d') = #{date}
        AND deleted='0')
    </select>

    <select id="checkTaskForUser" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and date_format(task_start_time,'%Y-%m-%d') <![CDATA[<=]]> #{date}
        and date_format(complete_time,'%Y-%m-%d') =#{date}
        and task_check_type = '2'
        and task_check_id = #{userId}
        and task_complete_status = 3
        and task_child_ids is null
        union all
        select
        <include refid="shortField"></include>
        from ops_task_gen_info
        where deleted = '0'
        and date_format(task_start_time,'%Y-%m-%d') <![CDATA[<=]]> #{date}
        and date_format(complete_time,'%Y-%m-%d') =#{date}
        and task_check_type = '1'
        and operation_check_id = #{userId}
        and task_complete_status = 3
        and task_child_ids is null
    </select>
    <select id="checkTaskForLeader" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select  <include refid="shortField"></include>
        from ops_task_gen_info
        where task_check_req=1
          <if test="cn.postIds !=null and cn.postIds.size()>0">
              and check_org_id in
              <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                  #{v}
              </foreach>
          </if>
          and task_complete_status=3
          and task_type='daily'
          and task_gen_time=#{genTime}
        and task_child_ids is null

        UNION ALL
        SELECT <include refid="shortField"></include>
        from ops_task_gen_info
        where task_check_req=1
        <if test="cn.postIds !=null and cn.postIds.size>0">
            and check_org_id in
            <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                #{v}
            </foreach>
        </if>
          and task_complete_status=3
          and (task_type='period' or task_type='temp')
        and task_child_ids is null
        and date_format(complete_time,'%Y-%m-%d')=#{genTime}
    </select>
    <select id="checkTaskForLeaderByLevelHi" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select  <include refid="shortField"></include>
        from ops_task_gen_info
        where task_check_req=1

        and task_complete_status=3
        and task_type='daily'
        and task_gen_time=#{genTime}
        and task_child_ids is null
        and task_priority=1
        UNION ALL
        SELECT <include refid="shortField"></include>
        from ops_task_gen_info
        where task_check_req=1
        and task_priority=1
        and task_complete_status=3
        and (task_type='period' or task_type='temp')
        and task_child_ids is null
        and date_format(complete_time,'%Y-%m-%d')=#{genTime}
    </select>

    <select id="dashboardListTaskDetail" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
            <include refid="statCommonField"></include>
        from ( SELECT
                    <include refid="statCommonField"></include>
                FROM ops_task_gen_info
                ${ew.customSqlSegment}) c1
            <if test="dc.type!=null and dc.type==3">
                where c1.owner_org_id =#{dc.postId}
            </if>
            <if test="dc.type!=null and dc.type==2">
                where c1.owner_org_id in
                <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
            <if test="dc.type!=null and dc.type==1">
                where c1.owner_org_id in
                  <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                        #{c}
                  </foreach>
                and
                ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
                    or
                    (c1.task_owner_type='1' and c1.task_owner_id in
                        <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                          #{c}
                        </foreach>
                    )
                    or
                    (c1.access_level=1))
            </if>
    </select>
    <select id="dashboardListTaskByMultiLeafDetail"
            resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
            <include refid="statCommonField"></include>
        from
            (SELECT
                <include refid="statCommonField"></include>
                FROM ops_task_gen_info
                WHERE(task_gen_time = #{genTime}
                    AND task_type = 'daily'
                    AND task_deferred_type = '0'
                    AND deleted='0' )
            union all
            SELECT
                <include refid="statCommonField"></include>
                FROM ops_task_gen_info
                WHERE (task_type = 'daily'
                    AND task_gen_time <![CDATA[<=]]> #{genTime}
                    AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
                    AND task_deferred_type = '1'
                    AND deleted='0')
            union all
            SELECT
                <include refid="statCommonField"></include>
                FROM ops_task_gen_info
                WHERE (task_type = 'period'
                    AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
                    AND task_complete_status != 3
                    AND task_start_time <![CDATA[<=]]> #{nowTime}
                    AND deleted='0')
            union all
            SELECT
                <include refid="statCommonField"></include>
                FROM ops_task_gen_info
                WHERE (task_type = 'period'
                    AND date_format(complete_time,'%Y-%m-%d') = #{genTime}
                    AND task_complete_status = 3
                    AND task_start_time <![CDATA[<=]]> #{nowTime}
                    AND deleted='0')
            )c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in
                <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            and
            (
                (c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
                or
                (c1.task_owner_type='1' and c1.task_owner_id in
                    <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                        #{c}
                    </foreach>)
                or
                (c1.access_level=1)
            )
        </if>
    </select>

    <select id="dashboardListTaskByMultiLeafDetailByLeader"
            resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        <include refid="statCommonField"></include>
        from
        (SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND deleted='0'
            and task_priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_deferred_type = '1'
        AND deleted='0'
        and task_priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
        AND date_format(task_end_time,'%Y-%m-%d') = #{genTime}
        AND task_complete_status != 3
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND deleted='0'
        and task_priority=1)
        union all
        SELECT
        <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
        AND date_format(complete_time,'%Y-%m-%d') = #{genTime}
        AND task_complete_status == 3
        AND task_start_time <![CDATA[<=]]> #{nowTime}
        AND deleted='0'
        and task_priority=1)
        ) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
            and
            (
            (c1.task_Owner_Type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>
    </select>
    <select id="checkTaskForUserDetail" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
            <include refid="shortField"></include>
        from ops_task_gen_info
            where deleted = '0'
            and date_format(task_end_time,'%Y-%m-%d') <![CDATA[<=]]> #{date}
            and task_check_type = '2'
            and task_check_id = #{userId}
            and task_complete_status = 3
        union all
        select
            <include refid="shortField"></include>
        from ops_task_gen_info
            where deleted = '0'
            and date_format(task_end_time,'%Y-%m-%d') <![CDATA[<=]]> #{date}
            and task_check_type = '1'
            and operation_check_id = #{userId}
            and task_complete_status = 3
    </select>
    <select id="checkTaskForLeaderDetail" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
            <include refid="shortField"></include>
        from ops_task_gen_info
            where task_check_req=1
            <if test="cn.postIds !=null and cn.postIds.size()>0">
                and check_org_id in
                <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                    #{v}
                </foreach>
            </if>
            and task_complete_status=3
            and task_type='daily'
            and task_gen_time=#{genTime}

        UNION ALL

        SELECT
            <include refid="shortField"></include>
        from ops_task_gen_info
            where task_check_req=1
            <if test="cn.postIds !=null and cn.postIds.size>0">
                and check_org_id in
                <foreach collection="cn.postIds" close=")" open="(" separator="," item="v">
                    #{v}
                </foreach>
            </if>
            and task_complete_status=3
            and (task_type='period' or task_type='temp')
            and date_format(task_end_time,'%Y-%m-%d')=#{genTime}
    </select>
    <select id="getParentListByChildIds" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        WITH basicInfo AS
                 (Select distinct r1.id
                  from ops_task_attr_basic_replica r1
                    start with r1.id in
                             (select distinct t2.id
                                from ops_task_gen_info t1
                                inner join ops_task_attr_basic_replica t2
                                on t1.task_ref_id = t2.id
                                where t1.id in
                                    <foreach collection="ids" open="(" close=")" separator="," item="id">
                                        #{id}
                                    </foreach>
                                )

                    CONNECT BY PRIOR r1.parent_id = r1.id),
            genInfo AS
            (SELECT <include refid="statCommonField"></include>
                from ops_task_gen_info
                where task_ref_id in (select distinct id from basicinfo))

        select distinct g.*
        from geninfo g
            start with g.id in
                <foreach collection="ids" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
        CONNECT BY PRIOR g.parent_id = g.id

    </select>
    <select id="allTaskForNowDayV1" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        SELECT
            <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE(task_gen_time = #{date}
            AND task_type = 'daily'
            AND task_deferred_type = '0'
            and task_complete_status <![CDATA[<>]]> 5
            AND deleted='0')

        union all

        SELECT
            <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'daily'
            AND task_gen_time <![CDATA[<=]]> #{date}
            AND task_end_time >= (#{date}||'22:00:00')
            AND task_deferred_type = '1'
            and task_complete_status <![CDATA[<>]]> 5
            AND deleted='0')

        union all

        SELECT
            <include refid="statCommonField"></include>
        FROM ops_task_gen_info
        WHERE (task_type = 'period'
            and task_complete_status <![CDATA[<>]]> 5
            AND date_format(task_end_time,'%Y-%m-%d') = #{date}
            AND deleted='0')
    </select>
    <select id="dashboardTempListTaskLeaf" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        <include refid="comsql"></include>
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        ${ew.customSqlSegment}) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            )
        </if>
    </select>
    <select id="listByTemplateIdAndNowDate" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select <include refid="comsql"></include>
            from ops_task_gen_info where task_bind_template_id=#{tempId} and task_gen_time=#{now}
    </select>
    <select id="dashboardListTaskByMultiByLeader" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">

        select <include refid="comsql"></include>  from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE(parent_id = 0
        AND task_gen_time = #{genTime}
        AND task_type = 'daily'
        AND task_deferred_type = '0'
        AND deleted='0'
        and task_Priority=1    )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'daily'
        AND task_gen_time <![CDATA[<=]]> #{genTime}
        AND date_format(task_end_time,'%Y-%m-%d') >= #{genTime}
        AND task_deferred_type = '1'
        AND deleted='0'  and task_Priority=1
        )
        union all
        SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        WHERE (parent_id = 0
        AND task_type = 'period'
        AND date_format(task_end_time,'%Y-%m-%d') >= #{genTime}
        AND date_format(task_start_time,'%Y-%m-%d') <![CDATA[<=]]> #{genTime}
        AND deleted='0' and task_Priority=1
        )
        )c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_Owner_Id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>
    </select>
    <select id="dashboardTaskListByTemp" resultType="sdata.ops.base.system.model.entity.OpsTaskGenInfo">
        select
        DISTINCT task_end_time
        from ( SELECT
        <include refid="comsql"></include>
        FROM ops_task_gen_info
        where PARENT_ID=0 and DELETED=0 and TASK_COMPLETE_STATUS=1 and TASK_TYPE='temp' and task_end_time <![CDATA[>=]]> #{st} and task_end_time <![CDATA[<=]]> #{et}) c1
        <if test="dc.type!=null and dc.type==3">
            where c1.owner_org_id =#{dc.postId}
        </if>
        <if test="dc.type!=null and dc.type==2">
            where c1.owner_org_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">
                #{c}
            </foreach>
        </if>
        <if test="dc.type!=null and dc.type==1">
            where c1.owner_org_id in   <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
        </foreach> and
            ((c1.task_owner_type ='2' and c1.task_owner_id=#{dc.userId})
            or
            (c1.task_owner_type='1' and c1.task_owner_id in
            <foreach collection="dc.postIds" separator="," item="c" open="(" close=")">#{c}
            </foreach>)
            or
            (c1.access_level=1)
            )
        </if>
        order by task_end_time desc

    </select>


    <sql id="comsql">
        id
        ,
        parent_id,
        task_no,
        task_process_status,
        task_complete_status,
        task_complete_desc,
        task_ref,
        task_transfer_status,
        task_transfer_user_id,
        task_transfer_desc,
        task_check_status,
        task_check_desc,
        task_name,
        task_progress,
        task_type,
        task_trigger_type,
        task_trigger_id,
        task_cron_val,
        task_desc,
        task_complete_type,
        task_complete_unit_id,
        task_audit_type,
        task_audit_unit_id,
        task_warn_notice,
        task_priority,
        task_level,
        task_attachments_type,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        task_gen_time,
        task_start_time,
        task_end_time,
        task_tags,
        task_auth_type,
        task_auth_id,
        task_auth_val,
        task_auth_scope,
        task_child_ids,
        task_deferred_type,
        task_deferred_count,
        depend_on_ids,
        required_item,
        task_ref_id,
        owner_org_id,
        check_org_id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,task_start_threshold,task_end_threshold,task_bind_template_id,task_sort,access_level,work_amount,work_amount_flag,
          operation_complete_id,operation_check_id,task_check_transfer_status, task_name_append ,task_append_type,task_create_type,indicator_id
    </sql>
    <sql id="shortField">
        id,
        parent_id,
        task_child_ids,
        task_complete_status,
          complete_time,
        task_name,
        task_type,
        task_gen_time,
        owner_org_id,
        task_bind_template_id,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_end_time,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        operation_complete_id,
        operation_check_id,
        task_check_transfer_status,
        access_level,
        indicator_id
    </sql>

    <sql id="statCommonField">
        id,
        parent_id,
        task_child_ids,
        task_complete_status,
        complete_time,
        task_name,
        task_type,
        task_gen_time,
        owner_org_id,
        task_bind_template_id,
        task_owner_type,
        task_owner_id,
        task_owner_val,
        task_start_time,
        task_end_time,
        task_check_req,
        task_check_type,
        task_check_id,
        task_check_val,
        task_ref_id,
        operation_complete_id,
        operation_check_id,
        task_check_transfer_status,
        access_level,
        indicator_id
    </sql>
</mapper>
