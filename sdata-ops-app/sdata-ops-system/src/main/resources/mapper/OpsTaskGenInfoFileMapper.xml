<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsTaskGenInfoFileMapper">
    <resultMap id="OpsTaskGenInfoFileResultMap" type="sdata.ops.base.system.model.entity.OpsTaskGenInfoFile">
        <id column="ID" property="id" />
        <result column="INFO_ID" property="infoId" />
        <result column="NAME" property="name" />
        <result column="FILE_PATH" property="filePath" />
        <result column="DATE_TIME" property="dateTime" />
        <result column="USER_ID" property="userId" />
        <result column="USER_NAME" property="userName" />
    </resultMap>
    <sql id="Base_Column_List">
        id,info_id,name,file_path,date_time,user_id,user_name
    </sql>
    <select id="selectListByInfoId" resultMap="OpsTaskGenInfoFileResultMap">
        select <include refid="Base_Column_List"/> from ops_task_gen_info_file
        where info_id = #{id} order by date_time desc
    </select>
</mapper>