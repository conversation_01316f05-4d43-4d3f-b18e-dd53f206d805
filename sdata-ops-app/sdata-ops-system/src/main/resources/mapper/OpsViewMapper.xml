<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsViewMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsView">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="props" column="props" jdbcType="VARCHAR"/>
            <result property="cardList" column="card_list" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,description,
        type,props,card_list,
        source,template_id,create_time,
        create_by,deleted,update_time,
        update_by
    </sql>
    <select id="selectListBySort" resultType="sdata.ops.base.system.model.entity.OpsView">
        SELECT
            *
        FROM
            (SELECT
                 a.*,b.is_visible
             FROM
                 sdata_ops.ops_view a
                     LEFT JOIN sdata_ops.ops_view_preferences b
                               ON a.id = b.view_id
             WHERE b.user_id = #{userId} and b.is_visible=0
             ORDER BY b.id) c
        UNION
            ALL
        SELECT
            *,0 AS is_visible
        FROM
            ops_view
        WHERE SOURCE = 'system' and id NOT IN (SELECT view_id FROM ops_view_preferences WHERE user_id = #{userId})

    </select>
    <select id="selectListBySortNoFilter" resultType="sdata.ops.base.system.model.entity.OpsView">
        SELECT
            *
        FROM
            (SELECT
                 a.*,b.is_visible
             FROM
                 sdata_ops.ops_view a
                     LEFT JOIN sdata_ops.ops_view_preferences b
                               ON a.id = b.view_id
             WHERE b.user_id = #{userId}
             ORDER BY b.id) c
        UNION
            ALL
        SELECT
            *,0 AS is_visible
        FROM
            ops_view
        WHERE SOURCE = 'system' and id NOT IN (SELECT view_id FROM ops_view_preferences WHERE user_id = #{userId})
    </select>
</mapper>
