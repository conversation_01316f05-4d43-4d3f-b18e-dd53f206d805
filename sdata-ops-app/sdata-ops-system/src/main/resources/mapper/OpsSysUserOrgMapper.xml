<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysUserOrgMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysUserOrg">
            <id property="userId" column="user_id" jdbcType="BIGINT"/>
            <id property="orgId" column="org_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,org_id
    </sql>
    <update id="updateUserOrgStr">
        update ops_sys_user set role=concat(role,',',#{orgId}) where id=#{userId}
    </update>
    <select id="queryUserInfoByOrgId" resultType="sdata.ops.base.system.model.entity.SystemUser">
        select  a.* from ops_sys_user a INNER JOIN ops_sys_user_org b on a.id=b.user_id
        where b.org_id=#{orgId}
    </select>
    <select id="queryUserInfoNotAuthByOrgId" resultType="sdata.ops.base.system.model.entity.SystemUser">
        select  a.* from ops_sys_user a where a.id not in (
            select  a.id from ops_sys_user a INNER JOIN ops_sys_user_org b on a.id=b.user_id
            where b.org_id=#{orgId}
            )
        and a.deleted=0
        <if test="name !=null and name !=''">
            and a.name like ${name}
        </if>
        <if test="phone!=null and phone!=''">
            and a.phone_num like ${phone}
        </if>
    </select>
    <select id="diffInfoBy" resultType="java.lang.String">

        select a.ORG_ID
        from OPS_SYS_USER_ORG a
                 left join (select  * from OPS_SYS_USER_ORG  where user_id=#{toId}) b
                           on a.org_id=b.org_id
        where a.user_id=#{fromId}
          and b.org_id is null;

    </select>
</mapper>
