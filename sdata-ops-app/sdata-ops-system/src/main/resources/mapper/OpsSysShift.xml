<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysShiftMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysShift">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,name,org_id,remark,sort,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="list" resultType="sdata.ops.base.system.model.vo.OpsSysShiftVO">
        SELECT
        ops_sys_shift.id,
        ops_sys_shift.name,
        ops_sys_shift.org_id,
        ops_sys_shift.remark,
        ops_sys_shift.create_by,
        ops_sys_shift.create_time,
        ops_sys_shift.update_by,
        ops_sys_shift.update_time,
        ops_sys_shift.sort,
        ops_sys_org.org_name orgName
        FROM ops_sys_shift
        LEFT JOIN ops_sys_org ON ops_sys_shift.org_id = ops_sys_org.id
        <where>
            <if test="opsSysOrg.name != null and opsSysOrg.name != ''">
                AND name LIKE #{opsSysOrg.name}
            </if>
            <if test="opsSysOrg.remark != null and opsSysOrg.remark != ''">
                AND remark LIKE #{opsSysOrg.remark}
            </if>
        </where>
        ORDER BY ops_sys_shift.sort
    </select>

    <select id="getByOrgId" resultType="sdata.ops.base.system.model.entity.OpsSysShift">
        SELECT ops_sys_shift.id,
               ops_sys_shift.name,
               ops_sys_shift.org_id,
               ops_sys_shift.remark,
               ops_sys_shift.sort,
               ops_sys_shift.create_by,
               ops_sys_shift.create_time,
               ops_sys_shift.update_by,
               ops_sys_shift.update_time
        FROM ops_sys_shift
        WHERE ops_sys_shift.org_id = #{id}
        ORDER BY ops_sys_shift.sort
    </select>

</mapper>
