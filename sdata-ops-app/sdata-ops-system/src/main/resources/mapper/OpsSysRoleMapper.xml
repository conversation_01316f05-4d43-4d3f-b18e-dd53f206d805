<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysRoleMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysRole">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
            <result property="roleKey" column="role_key" jdbcType="VARCHAR"/>
            <result property="roleSort" column="role_sort" jdbcType="INTEGER"/>
            <result property="dataScope" column="data_scope" jdbcType="CHAR"/>
            <result property="menuCheckStrictly" column="menu_check_strictly" jdbcType="TINYINT"/>
            <result property="deptCheckStrictly" column="dept_check_strictly" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="deleted" column="deleted" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,role_name,role_key,
        role_sort,data_scope,menu_check_strictly,
        dept_check_strictly,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>

    <sql id="selectRoleVo">
        select distinct r.id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.menu_check_strictly, r.dept_check_strictly,
                        r.status, r.deleted, r.create_time, r.remark
        from 	ops_sys_role r
                    LEFT JOIN ops_sys_user_role ur ON ur.role_id = r.id
                    LEFT JOIN ops_sys_user u ON u.id = ur.user_id
                    LEFT JOIN ops_sys_org d ON u.owner_dept = d.id
    </sql>

    <select id="selectRoleList" parameterType="sdata.ops.base.system.model.entity.OpsSysRole" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        where r.deleted = '0'
        <if test="id != null and id != 0">
            AND r.id = #{id}
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name like concat('%', #{roleName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="roleKey != null and roleKey != ''">
            AND r.role_key like concat('%', #{roleKey}, '%')
        </if>
        <!-- 数据范围过滤 -->
        order by r.role_sort
    </select>

    <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        WHERE r.deleted= '0' and ur.user_id = #{userId}
    </select>

    <select id="selectRoleAll" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
    </select>

    <select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
        select r.id
        from ops_sys_role r
                 left join ops_sys_user_role ur on ur.role_id = r.id
                 left join ops_sys_user u on u.user_id = ur.user_id
        where u.user_id = #{userId}
    </select>

    <select id="selectRoleById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        where r.id = #{roleId}
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        WHERE r.deleted = '0' and u.user_name = #{userName}
    </select>

    <select id="checkRoleNameUnique" parameterType="String" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        where r.role_name=#{roleName} and r.deleted = '0' limit 1
    </select>

    <select id="checkRoleKeyUnique" parameterType="String" resultMap="BaseResultMap">
        <include refid="selectRoleVo"/>
        where r.role_key=#{roleKey} and r.deleted = '0' limit 1
    </select>

    <insert id="insertRole" parameterType="sdata.ops.base.system.model.entity.OpsSysRole" useGeneratedKeys="true" keyProperty="roleId">
        insert into ops_sys_role(
        <if test="id != null and id != 0">id,</if>
        <if test="roleName != null and roleName != ''">role_name,</if>
        <if test="roleKey != null and roleKey != ''">role_key,</if>
        <if test="roleSort != null">role_sort,</if>
        <if test="dataScope != null and dataScope != ''">data_scope,</if>
        <if test="menuCheckStrictly != null">menu_check_strictly,</if>
        <if test="deptCheckStrictly != null">dept_check_strictly,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="id != null and id != 0">#{id},</if>
        <if test="roleName != null and roleName != ''">#{roleName},</if>
        <if test="roleKey != null and roleKey != ''">#{roleKey},</if>
        <if test="roleSort != null">#{roleSort},</if>
        <if test="dataScope != null and dataScope != ''">#{dataScope},</if>
        <if test="menuCheckStrictly != null">#{menuCheckStrictly},</if>
        <if test="deptCheckStrictly != null">#{deptCheckStrictly},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateRole" parameterType="sdata.ops.base.system.model.entity.OpsSysRole">
        update ops_sys_role
        <set>
            <if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
            <if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if test="roleSort != null">role_sort = #{roleSort},</if>
            <if test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
            <if test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
            <if test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteRoleById" parameterType="Long">
        update ops_sys_role set deleted = '1' where id = #{roleId}
    </delete>

    <delete id="deleteRoleByIds" parameterType="Long">
        update ops_sys_role set deleted = '1' where id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
</mapper>
