<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysOrgMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysOrg">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="ancestors" column="ancestors" jdbcType="VARCHAR"/>
            <result property="orgType" column="org_type" jdbcType="CHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
            <result property="leader" column="leader" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="deleted" column="deleted" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,ancestors,
        org_type,org_name,order_num,
        leader,phone,email,
        status,deleted,create_by,
        create_time,update_by,update_time
    </sql>
    <sql id="selectDeptVo">
        select d.id, d.parent_id, d.ancestors,d.org_type, d.org_name, d.order_num, d.leader, d.phone, d.email, d.status, d.deleted, d.create_by, d.create_time
        from ops_sys_org d
    </sql>
    <select id="selectDeptList" parameterType="sdata.ops.base.system.model.entity.OpsSysOrg" resultMap="BaseResultMap">
        <include refid="selectDeptVo"/>
        where d.deleted= '0'
        <if test="id != null and id != 0">
            AND id = #{id}
        </if>
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="orgName != null and orgName != ''">
            AND org_name like concat('%', #{orgName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <!-- 数据范围过滤 -->
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptListByRoleId" resultType="Long">
        select d.id
        from ops_sys_org d
        left join ops_sys_role_org rd on d.id = rd.org_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.id = rd.org_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectDeptVo"/>
        where id = #{deptId}
    </select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
        select count(1) from ops_sys_user_org where ORG_ID = #{deptId}
    </select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
        select count(1) from ops_sys_org
        where deleted = '0' and parent_id = #{deptId} limit 1
    </select>

    <select id="selectChildrenDeptById" parameterType="Long" resultMap="BaseResultMap">
        select * from ops_sys_org where find_in_set(#{deptId}, ancestors)
    </select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
        select count(*) from ops_sys_org where status = 0 and deleted = '0' and find_in_set(#{deptId}, ancestors)
    </select>

    <select id="checkDeptNameUnique" resultMap="BaseResultMap">
        <include refid="selectDeptVo"/>
        where org_name=#{deptName} and parent_id = #{parentId} and deleted = '0' limit 1
    </select>



    <update id="updateDept" parameterType="sdata.ops.base.system.model.entity.OpsSysOrg">
        update ops_sys_org
        <set>
            <if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
            <if test="orgName != null and orgName != ''">org_name = #{orgName},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update ops_sys_org set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal" parameterType="Long">
        update ops_sys_org set status = '0' where id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <delete id="deleteDeptById" parameterType="Long">
        update ops_sys_org set deleted = '2' where id = #{deptId}
    </delete>
</mapper>
