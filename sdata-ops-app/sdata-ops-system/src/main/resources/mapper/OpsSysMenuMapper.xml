<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysMenuMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysMenu">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="component" column="component" jdbcType="VARCHAR"/>
            <result property="query" column="query" jdbcType="VARCHAR"/>
            <result property="isFrame" column="is_frame" jdbcType="INTEGER"/>
            <result property="isCache" column="is_cache" jdbcType="INTEGER"/>
            <result property="menuType" column="menu_type" jdbcType="CHAR"/>
            <result property="visible" column="visible" jdbcType="CHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="perms" column="perms" jdbcType="VARCHAR"/>
            <result property="icon" column="icon" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,menu_name,parent_id,
        order_num,path,component,
        query,is_frame,is_cache,
        menu_type,visible,status,
        perms,icon,create_by,
        create_time,update_by,update_time,
        remark
    </sql>

    <sql id="selectMenuVo">
        select id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, ifnull(perms,'') as perms, icon, create_time
        from ops_sys_menu
    </sql>

    <select id="selectMenuList" parameterType="sdata.ops.base.system.model.entity.OpsSysMenu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        <where>
            <if test="menuName != null and menuName != ''">
                AND menu_name like concat('%', #{menuName}, '%')
            </if>
<!--            <if test="visible != null and visible != ''">-->
<!--                AND visible = #{visible}-->
<!--            </if>-->
<!--            <if test="status != null and status != ''">-->
<!--                AND status = #{status}-->
<!--            </if>-->
        </where>
        order by parent_id, order_num
    </select>

    <select id="selectMenuTreeAll" resultMap="BaseResultMap">
        select distinct m.id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from ops_sys_menu m where m.menu_type in ('M', 'C') and m.status = 0
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuListByUserId" parameterType="sdata.ops.base.system.model.entity.OpsSysMenu" resultMap="BaseResultMap">
        select distinct m.id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from ops_sys_menu m
        left join ops_sys_role_menu rm on m.id = rm.menu_id
        left join ops_sys_user_role ur on rm.role_id = ur.role_id
        left join ops_sys_role ro on ur.role_id = ro.id
        where ur.user_id = #{params.userId}
        <if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
        </if>
        <if test="visible != null and visible != ''">
            AND m.visible = #{visible}
        </if>
        <if test="status != null and status != ''">
            AND m.status = #{status}
        </if>
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="BaseResultMap">
        select distinct m.id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from ops_sys_menu m
                 left join ops_sys_role_menu rm on m.menu_id = rm.menu_id
                 left join ops_sys_user_role ur on rm.role_id = ur.role_id
                 left join ops_sys_role ro on ur.role_id = ro.role_id
                 left join ops_sys_user u on ur.user_id = u.user_id
        where u.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 0  AND ro.status = 0
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuListByRoleId" resultType="Long">
        select m.id
        from ops_sys_menu m
        left join ops_sys_role_menu rm on m.id = rm.menu_id
        where rm.role_id = #{roleId}
        <if test="menuCheckStrictly == 1">
            and m.id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.id = rm.menu_id and rm.role_id = #{roleId})
        </if>
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuPerms" resultType="String">
        select distinct m.perms
        from ops_sys_menu m
                 left join ops_sys_role_menu rm on m.id = rm.menu_id
                 left join ops_sys_user_role ur on rm.role_id = ur.role_id
    </select>

    <select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
        select distinct m.perms
        from ops_sys_menu m
                 left join ops_sys_role_menu rm on m.id = rm.menu_id
                 left join ops_sys_user_role ur on rm.role_id = ur.role_id
                 left join ops_sys_role r on r.role_id = ur.role_id
        where m.status = '0' and r.status = '0' and ur.user_id = #{userId}
    </select>

    <select id="selectMenuPermsByRoleId" parameterType="Long" resultType="String">
        select distinct m.perms
        from ops_sys_menu m
                 left join ops_sys_role_menu rm on m.id = rm.menu_id
        where m.status = '0' and rm.role_id = #{roleId}
    </select>

    <select id="selectMenuById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where id = #{menuId}
    </select>

    <select id="hasChildByMenuId" resultType="Integer">
        select count(1) from ops_sys_menu where parent_id = #{menuId}
    </select>

    <select id="checkMenuNameUnique" parameterType="sdata.ops.base.system.model.entity.OpsSysMenu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where menu_name=#{menuName} and parent_id = #{parentId} limit 1
    </select>

    <update id="updateMenu" parameterType="sdata.ops.base.system.model.entity.OpsSysMenu">
        update ops_sys_menu
        <set>
            <if test="menuName != null and menuName != ''">menu_name = #{menuName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="query != null">`query` = #{query},</if>
            <if test="isFrame != null and isFrame != ''">is_frame = #{isFrame},</if>
            <if test="isCache != null and isCache != ''">is_cache = #{isCache},</if>
            <if test="menuType != null and menuType != ''">menu_type = #{menuType},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="status != null">status = #{status},</if>
            <if test="perms !=null">perms = #{perms},</if>
            <if test="icon !=null and icon != ''">icon = #{icon},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>
    <update id="updateMenuVisible">
        update ops_sys_menu
        set visible = #{status}
        where id = #{id}
    </update>
    <update id="updateMenuStatus">
        update ops_sys_menu set status=#{status} where id=#{id}
    </update>

    <insert id="insertMenu" parameterType="sdata.ops.base.system.model.entity.OpsSysMenu">
        insert into sys_menu(
        <if test="id != null and id != 0">id,</if>
        <if test="parentId != null and parentId != 0">parent_id,</if>
        <if test="menuName != null and menuName != ''">menu_name,</if>
        <if test="orderNum != null">order_num,</if>
        <if test="path != null and path != ''">path,</if>
        <if test="component != null and component != ''">component,</if>
        <if test="query != null and query != ''">`query`,</if>
        <if test="isFrame != null and isFrame != ''">is_frame,</if>
        <if test="isCache != null and isCache != ''">is_cache,</if>
        <if test="menuType != null and menuType != ''">menu_type,</if>
        <if test="visible != null">visible,</if>
        <if test="status != null">status,</if>
        <if test="perms !=null and perms != ''">perms,</if>
        <if test="icon != null and icon != ''">icon,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="id != null and id != 0">#{id},</if>
        <if test="parentId != null and parentId != 0">#{parentId},</if>
        <if test="menuName != null and menuName != ''">#{menuName},</if>
        <if test="orderNum != null">#{orderNum},</if>
        <if test="path != null and path != ''">#{path},</if>
        <if test="component != null and component != ''">#{component},</if>
        <if test="query != null and query != ''">#{query},</if>
        <if test="isFrame != null and isFrame != ''">#{isFrame},</if>
        <if test="isCache != null and isCache != ''">#{isCache},</if>
        <if test="menuType != null and menuType != ''">#{menuType},</if>
        <if test="visible != null">#{visible},</if>
        <if test="status != null">#{status},</if>
        <if test="perms !=null and perms != ''">#{perms},</if>
        <if test="icon != null and icon != ''">#{icon},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <delete id="deleteMenuById" parameterType="Long">
        delete from ops_sys_menu where id = #{menuId}
    </delete>

</mapper>
