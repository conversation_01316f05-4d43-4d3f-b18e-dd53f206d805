<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsTaskCockpitMapper">

    <resultMap id="deptResultMap" type="java.util.Map">
        <result column="total" property="total" jdbcType="BIGINT"></result>
        <result column="owner_org_id" property="ownerOrgId" jdbcType="VARCHAR"></result>
        <result column="name" property="orgName" jdbcType="VARCHAR"></result>
    </resultMap>

    <resultMap id="comPreDeptResultMap" type="java.util.Map">
        <result column="total" property="total" jdbcType="BIGINT"></result>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"></result>
        <result column="org_id" property="orgId" jdbcType="VARCHAR"></result>
    </resultMap>


    <resultMap id="baseResultMap" type="java.util.Map">
        <result column="total" property="total" jdbcType="BIGINT"></result>
        <result column="month" property="month" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="countByStatus" resultType="java.lang.Integer">
        select count(*) from ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and YEAR(task_end_time) = #{year}
            and MONTH(task_end_time) = #{month}
            and task_complete_status != 5
            <if test="status!=null">
                and task_complete_status = #{status}
            </if>
        </where>
    </select>

    <select id="deptByStatus" resultMap="deptResultMap">
        select count(*) as total,owner_org_id,(select org_name from ops_sys_org where id = owner_org_id) as name from
        ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and YEAR(task_end_time) = #{year}
            and MONTH(task_end_time) = #{month}
            and task_complete_status != 5
            and owner_org_id is not null
            <if test="status!=null">
                and task_complete_status = #{status}
            </if>
        </where>
        group by owner_org_id
    </select>
    <select id="deptInCheck" resultMap="deptResultMap">
        select count(*) as total,check_org_id as owner_org_id,(select org_name from ops_sys_org where id = check_org_id) as name from
        ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and YEAR(task_end_time) = #{year}
            and MONTH(task_end_time) = #{month}
            and owner_org_id is not null
            and task_complete_status = 3
            and task_check_req = 1
        </where>
        group by check_org_id
    </select>

    <select id="countByMonth" resultMap="baseResultMap">
        select COUNT(*) as total, MONTH(task_end_time) as month
        from ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and task_complete_status != 5
            and (task_end_time between #{startdate} and #{enddate})
            and owner_org_id is not null
            <if test="ids != null and ids.size() > 0">
                and owner_org_id in
                <foreach collection="ids" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
        </where>
        group by year (task_end_time), month (task_end_time)
        order by year (task_end_time), month (task_end_time)
    </select>
    <select id="checkCountByMonth" resultMap="baseResultMap">
        select count(*) as total, month(task_end_time) as month
        from ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and task_complete_status = 3
            and (task_end_time between #{startdate} and #{enddate})
            and task_check_req = 1
            <if test="ids != null and ids.size() > 0">
                and check_org_id in
                <foreach collection="ids" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
        </where>
        group by year (task_end_time), month (task_end_time)
        order by month (task_end_time)
    </select>

    <select id="delayCountByMonth" resultMap="baseResultMap">
        select count(*) as total, month(task_end_time) as month
        from ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and task_complete_status != 5
            and task_complete_status != 3
            and date_format(task_end_time,'%Y-%m-%d') &lt; date_format(now(),'%Y-%m-%d')
            and (task_end_time between #{startdate} and #{enddate})
            <if test="ids != null and ids.size() > 0">
                and owner_org_id in
                <foreach collection="ids" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
        </where>
        group by year (task_end_time), month (task_end_time)
        order by year (task_end_time), month (task_end_time)
    </select>

    <select id="lessDept" resultMap="deptResultMap">
        select count(*) as total, owner_org_id,(select org_name from ops_sys_org where id = owner_org_id) as name from
        ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and year(task_end_time) = #{year}
            and month(task_end_time) = #{month}
            <if test="ids != null and ids.size() > 0">
                and owner_org_id in
                <foreach collection="ids" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
        </where>
        group by owner_org_id
    </select>
    <select id="checkLessDept" resultMap="deptResultMap">
        select count(*) as total, check_org_id as owner_org_id,(select org_name from ops_sys_org where id = check_org_id) as name from
        ops_task_gen_info
        <where>
            task_child_ids is null
            and deleted = 0
            and year(task_end_time) = #{year}
            and month(task_end_time) = #{month}
            and task_complete_status = 3
            and task_check_req = 1
            <if test="ids != null and ids.size() > 0">
                and check_org_id in
                <foreach collection="ids" separator="," item="c" open="(" close=")">
                    #{c}
                </foreach>
            </if>
        </where>
        group by check_org_id
    </select>
    <select id="getComprehensiveDept" resultMap="comPreDeptResultMap">
        select  sum(c.work_amount) as total,
        min(c.owner_org_id) as org_id,
        min(c.task_name) as task_name
        from (select a.owner_org_id,
        a.work_amount,
        a.task_name,
        a.task_ref_id
        from ops_task_gen_info a
        where
            a.task_start_time >=#{st}
        and a.task_end_time <![CDATA[<=]]> #{et}
        and a.task_complete_status=3
        and a.owner_org_id in
        <foreach collection="ls" item="k" close=")" open="(" separator=",">
            #{k}
        </foreach>
        and a.work_amount_flag=1
        )c
        group by c.task_ref_id

    </select>

</mapper>