<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.SystemUserMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.SystemUser">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phoneNum" column="phone_num" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="authType" column="auth_type" jdbcType="CHAR"/>
        <result property="userType" column="user_type" jdbcType="TINYINT"/>
        <result property="synced" column="synced" jdbcType="CHAR"/>
        <result property="deleted" column="deleted" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,username,password,
        name,phone_num,email,
        dept,status,auth_type,
        user_type,synced,deleted,
        create_by,create_time,update_by,
        update_time
    </sql>
    <update id="updateUserStatus">
        update ops_sys_user a set a.status=#{status} where a.id=#{id}
    </update>
    <select id="selectAllocatedList" parameterType="sdata.ops.base.system.model.entity.SystemUser" resultMap="BaseResultMap">
        select  distinct a.id, a.dept, a.username, a.name, a.email, a.phone_num, a.status, a.create_time
        from ops_sys_user a INNER JOIN ops_sys_user_role b on a.id=b.user_id
        LEFT JOIN ops_sys_role c on b.role_id=c.id where a.deleted='0' and c.deleted='0'
        <if test="username != null and username != ''">
            AND a.username like concat('%', #{username}, '%')
        </if>
        <if test="phoneNum != null and phoneNum != ''">
            AND b.phone_num like concat('%', #{phoneNum}, '%')
        </if>
        <if test="roleId != null and roleId !=''">
            and c.id=#{roleId}
        </if>
    </select>
    <select id="selectUnallocatedList" resultType="sdata.ops.base.system.model.entity.SystemUser">
        SELECT DISTINCT
            a.id,
            a.dept,
            a.username,
            a.NAME,
            a.email,
            a.phone_num,
            a.STATUS,
            a.create_time
        FROM
            ops_sys_user a
        WHERE
                a.id NOT IN (
                SELECT DISTINCT
                    a.id
                FROM
                    ops_sys_user a
                        INNER JOIN ops_sys_user_role b ON a.id = b.user_id
                        LEFT JOIN ops_sys_role c ON b.role_id = c.id
                WHERE
                    a.deleted = '0'
                  AND c.deleted = '0'
        <if test="roleId!=null and roleId!=''">
            and c.id=#{roleId}
        </if>
            )
        and a.deleted='0'
        <if test="username != null and username != ''">
            AND a.username like concat('%', #{username}, '%')
        </if>
        <if test="phoneNum != null and phoneNum != ''">
            AND b.phone_num like concat('%', #{phoneNum}, '%')
        </if>
    </select>
    <select id="findAllOrgAndRoleName" resultType="sdata.ops.base.system.model.dto.MapperDTO">
        SELECT
            a.id,
            a.org_name AS NAME
        FROM
            ops_sys_org a
        WHERE
            a.deleted = 0 UNION ALL
        SELECT
            a.id,
            a.role_name AS NAME
        FROM
            ops_sys_role a
        WHERE
            a.deleted = 0
    </select>
    <select id="queryAllUserShortInfo" resultType="sdata.ops.base.system.model.dto.MapperDTO">

        select id,name from ops_sys_user a where a.deleted=0

    </select>
    <select id="queryAllUserShortFailName" resultType="sdata.ops.base.system.model.dto.OrgUserDTO">
        SELECT
            a.id,
            a.NAME AS org_name,
            c.org_id
        FROM
            ops_sys_user a INNER JOIN ops_sys_user_org c on a.id=c.user_id
        WHERE
            a.deleted = 0

    </select>
    <select id="findUserByLeaderId" resultType="sdata.ops.base.system.model.entity.SystemUser">
        select  id,username,NAME
        from ops_sys_user
        where id in (select USER_ID
                     from ops_sys_user_org
                     where ORG_ID in (select id
                                      from ops_sys_org
                                      where ANCESTORS like ${orgId}
                                        and ORG_TYPE=3))
    </select>
    <select id="getLeaderForOrgId" resultType="java.lang.String">
        select  id from ops_sys_org where LEADER=#{userId};

    </select>


</mapper>
