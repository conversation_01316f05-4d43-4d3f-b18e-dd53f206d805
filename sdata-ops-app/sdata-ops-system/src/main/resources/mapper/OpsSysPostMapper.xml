<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsSysPostMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsSysPost">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="postCode" column="post_code" jdbcType="VARCHAR"/>
            <result property="postName" column="post_name" jdbcType="VARCHAR"/>
            <result property="postSort" column="post_sort" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,post_code,post_name,
        post_sort,status,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
</mapper>
