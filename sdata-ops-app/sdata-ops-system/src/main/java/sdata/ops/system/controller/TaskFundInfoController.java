package sdata.ops.system.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.OpsTaskInfoDeleteDTO;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.OpsTaskFundInfoService;

/**
 * <AUTHOR>
 * @since 2024/9/19 10:57
 */
@RestController
@RequestMapping("/system/fund")
@RequiredArgsConstructor
public class TaskFundInfoController {

    final private OpsTaskFundInfoService fundInfoService;

    /**
     * 列表查询
     *
     * @return 返回结果
     */
    @GetMapping("/list")
    public R<Object> list(@RequestParam("date") String date, @RequestParam("taskReplicaId") String taskReplicaId) {
        return R.data(fundInfoService.getFundByDate(date,taskReplicaId));
    }

    /**
     * 保存任务明显
     *
     * @return 返回结果
     */
    @PostMapping("/save")
    public R<Object> updateStatus(@RequestBody OpsTaskInfoUpdateDTO dto) {
        String operator = SecureUtil.currentUserName();
        fundInfoService.update(dto, operator);
        return R.success("操作成功");
    }

    /**
     * 删除任务明显
     *
     * @param dto 请求参数
     * @return 返回结果
     */
    @PostMapping("/delete")
    public R<Object> delete(@RequestBody OpsTaskInfoDeleteDTO dto) {
        fundInfoService.delete(dto);
        return R.success("删除成功");
    }

    @PostMapping("/saveByIndicator")
    public R<Object> saveByIndicator(@RequestBody OpsTaskInfoUpdateDTO dto){
        fundInfoService.update(dto, "system");
        return R.success("操作成功");
    }

}
