package sdata.ops.system.service;

import sdata.ops.base.system.model.entity.OpsSysUserOrg;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.OrgUserVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_user_post(用户与机构岗位关联表)】的数据库操作Service
* @createDate 2024-06-03 19:48:03
*/
public interface OpsSysUserOrgService extends IService<OpsSysUserOrg> {

    List<SystemUser> selectAllocatedList(String orgId);

    List<SystemUser> selectUnAllocatedList(String orgId,String name, String phoneNum);

    void saveUpdateRelation(OrgUserVO orgUserVO);

    List<String> diffInfoBy(String fromId, String toId);

}
