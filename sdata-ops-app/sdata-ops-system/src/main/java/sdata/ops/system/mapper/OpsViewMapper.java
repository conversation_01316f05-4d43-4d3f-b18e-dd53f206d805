package sdata.ops.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsView;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_view】的数据库操作Mapper
* @createDate 2025-03-17 10:05:16
* @Entity generator.domain.OpsView
*/
@Mapper
public interface OpsViewMapper extends BaseMapper<OpsView> {


    @Delete("delete from sdata_ops.ops_view where id=#{id}")
    void deletedReal(@Param("id") String id);

    List<OpsView> selectListBySort(@Param("userId") String id);

    List<OpsView> selectListBySortNoFilter(String id);
}
