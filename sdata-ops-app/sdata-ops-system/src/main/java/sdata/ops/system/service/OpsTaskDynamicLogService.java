package sdata.ops.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsTaskDynamicLog;

/**
 * <AUTHOR>
 * @description 针对表【OPS_TASK_DYNAMIC_LOG(自定义任务已经触发成功的日志信息)】的数据库操作Service
 * @createDate 2024-08-09 09:56:56
 */
public interface OpsTaskDynamicLogService extends IService<OpsTaskDynamicLog> {

    boolean checkTaskIsRecord(Long taskId);

    void insertSuccessTaskRecord(Long taskId);

}
