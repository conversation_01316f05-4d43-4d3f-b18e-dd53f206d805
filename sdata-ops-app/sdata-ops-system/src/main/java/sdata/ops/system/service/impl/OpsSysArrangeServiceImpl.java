package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsSysArrangeDTO;
import sdata.ops.base.system.model.entity.OpsSysArrange;
import sdata.ops.base.system.model.entity.OpsSysShift;
import sdata.ops.base.system.model.vo.ArrangeUserVO;
import sdata.ops.base.system.model.vo.ArrangeVO;
import sdata.ops.base.system.model.vo.OpsSysArrangeVO;
import sdata.ops.system.mapper.OpsSysArrangeMapper;
import sdata.ops.system.service.OpsSysArrangeService;
import sdata.ops.system.service.OpsSysCalendarService;
import sdata.ops.system.service.OpsSysShiftService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_attr_basic_replica】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
@AllArgsConstructor
public class OpsSysArrangeServiceImpl extends ServiceImpl<OpsSysArrangeMapper, OpsSysArrange>
        implements OpsSysArrangeService {

    private final OpsSysShiftService opsSysShiftService;

    private final OpsSysCalendarService opsSysCalendarService;

    @Override
    public List<Map<String, Object>> list(String id, String startDate, String endDate) {
        List<OpsSysArrangeVO> list = baseMapper.list(id, startDate, endDate);
        Map<String, String> shiftMap = new HashMap<>();
        for (OpsSysShift opsSysShift : opsSysShiftService.list()) {
            shiftMap.put(opsSysShift.getId(), opsSysShift.getName());
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        List<String> dates = list.stream().map(OpsSysArrange::getDate).distinct().sorted().collect(Collectors.toList());
        for (String date : dates) {
            Map<String, Object> map = new HashMap<>();
            Map<String, List<OpsSysArrangeVO>> listMap = list.stream().filter(x -> date.equals(x.getDate())).collect(Collectors.groupingBy(OpsSysArrangeVO::getShiftId));
            List<ArrangeVO> arrangeVOS = new ArrayList<>();
            for (Map.Entry<String, List<OpsSysArrangeVO>> entry : listMap.entrySet()) {
                ArrangeVO arrangeVO = new ArrangeVO();
                arrangeVO.setShiftId(entry.getKey());
                arrangeVO.setShiftName(shiftMap.get(entry.getKey()));
                List<ArrangeUserVO> arrangeUserVOS = new ArrayList<>();
                for (OpsSysArrangeVO vo : entry.getValue()) {
                    arrangeVO.setSort(vo.getSort());
                    ArrangeUserVO arrangeUserVO = new ArrangeUserVO();
                    arrangeUserVO.setArrangeId(vo.getId());
                    arrangeUserVO.setUserName(vo.getUserName());
                    arrangeUserVO.setUserId(vo.getUserId());
                    arrangeUserVOS.add(arrangeUserVO);
                }
                arrangeVO.setUsers(arrangeUserVOS);
                arrangeVOS.add(arrangeVO);
            }
            arrangeVOS.sort((o1, o2) -> {
                String x = StringUtils.isBlank(o1.getSort()) ? "0" : o1.getSort();
                String y = StringUtils.isBlank(o2.getSort()) ? "0" : o2.getSort();
                return x.compareTo(y);
            });
            map.put("list", arrangeVOS);
            map.put("date", date);
            maps.add(map);
        }
        return maps;
    }

    @Override
    public Integer findByShiftId(String id) {
        return baseMapper.findByShiftId(id);
    }

    @Override
    public void cover(OpsSysArrangeDTO opsSysArrangedDTO) {
        //通过岗位ID、班次ID、日期删除排班记录
        baseMapper.del(opsSysArrangedDTO);
        //添加新的排班记录
        List<OpsSysArrange> list = new ArrayList<>();
        for (String userId : opsSysArrangedDTO.getUserIds().split(",")) {
            OpsSysArrange opSysArrange = new OpsSysArrange();
            BeanUtils.copyProperties(opsSysArrangedDTO, opSysArrange);
            opSysArrange.setUserId(userId);
            list.add(opSysArrange);
        }
        this.saveBatch(list, 1000);
    }

    @Override
    @Transactional
    public void batchScheduling(OpsSysArrangeDTO opsSysArrangedDTO) {
        // 获取日期列表
        List<String> dateList = opsSysCalendarService.listByDate(opsSysArrangedDTO.getStartDate(), opsSysArrangedDTO.getEndDate());
        if (dateList.isEmpty()) {
            return;
        }
        //清空这个日期区间内这个人的排班信息
        baseMapper.delByDate(opsSysArrangedDTO);
        List<OpsSysArrange> opsSysArranges = dateList.stream().map(date -> {
            OpsSysArrange opsSysArrange = new OpsSysArrange();
            BeanUtils.copyProperties(opsSysArrangedDTO, opsSysArrange);
            opsSysArrange.setDate(date);
            return opsSysArrange;
        }).collect(Collectors.toList());
        //批量排班
        this.saveBatch(opsSysArranges, 1000);
    }

    @Override
    public Integer findByOrgShiftUserDate(OpsSysArrange opsSysArrange) {
        return baseMapper.findByOrgShiftUserDate(opsSysArrange);
    }
}




