package sdata.ops.system.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.vo.OpsViewPreferencesVO;
import sdata.ops.base.system.model.vo.OpsViewVO;
import sdata.ops.base.system.model.vo.ShortOpsViewVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_view】的数据库操作Service
 * @createDate 2025-03-17 10:05:16
 */
public interface OpsViewService extends IService<OpsView> {

    void saveView(OpsView viewEntity);

    void deleteView(String id);

    OpsViewVO getAllViewInfo(String id);

    List<OpsViewVO> viewList(String id);

    List<ShortOpsViewVO> shortViewList(String id);

    void removePreferences(List<OpsViewPreferencesVO> preferences);

}
