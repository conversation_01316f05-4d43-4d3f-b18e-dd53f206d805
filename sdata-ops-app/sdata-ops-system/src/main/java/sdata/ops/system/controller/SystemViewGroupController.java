package sdata.ops.system.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.base.system.model.entity.OpsViewGroup;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsViewGroupService;

import java.util.List;
import java.util.Objects;

@RequestMapping("/view-template-groups")
@RestController
@RequiredArgsConstructor
public class SystemViewGroupController {


    private final OpsViewGroupService viewGroupService;


    @RequestMapping("/tree")
    public R<Object> viewGroupTreeList() {
        List<OpsViewGroup> viewGroups = viewGroupService.treeList();
        return R.data(viewGroups);
    }

    @RequestMapping("/save")
    public R<Object> save(@RequestBody OpsViewGroup viewGroup) {
        if(Objects.isNull(viewGroup.getPId())){
            viewGroup.setPId(0L);
        }
        boolean save = viewGroupService.saveOrUpdate(viewGroup);
        return R.data(save);
    }

    @RequestMapping("/delete")
    private R<Object> delete(Long id) {
        viewGroupService.deleteGroup(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }


}
