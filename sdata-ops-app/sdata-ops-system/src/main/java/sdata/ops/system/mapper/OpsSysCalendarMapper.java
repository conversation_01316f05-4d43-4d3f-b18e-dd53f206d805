package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import sdata.ops.base.system.model.entity.OpsSysCalendar;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:19
 */
@Mapper
public interface OpsSysCalendarMapper extends BaseMapper<OpsSysCalendar> {

    @Select("select calendar_date, \n" +
            "       nums \n" +
            "  from (select calendar_date,\n" +
            "               trade,\n" +
            "               ROW_NUMBER() OVER(ORDER BY calendar_date) AS nums\n" +
            "          from ops_sys_calendar\n" +
            "         where calendar_date > #{today}\n" +
            "           and calendar_date <#{moday}\n" +
            "           and trade='y' order by calendar_date) c1 \n" +
            " where nums=#{df}")
    OpsSysCalendar selectStringDate(@Param("today") String today, @Param("moday") String moDay, @Param("df") Integer df);

    @Select("select calendar_date from ops_sys_calendar where trade = 'y' and calendar_date >= #{startDate} and calendar_date <= #{endDate}")
    List<String> listByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select("select calendar_date from (select ROW_NUMBER() OVER(ORDER BY calendar_date) rownum,c1.calendar_date \n" +
            "  from (select \n" +
            "               calendar_date\n" +
            "          from ops_sys_calendar\n" +
            "         where calendar_date >=#{rangedate}\n" +
            "           and calendar_date<=#{date}\n" +
            "           and trade='y' order by calendar_date desc) c1  ) c2 where c2.rownum=2")
    String listRowNumberFixed(@Param("rangeDate") String rangeDate, @Param("date") String date);

    @Select("select calendar_date from (select  ROW_NUMBER() OVER(ORDER BY calendar_date) rownum,c1.calendar_date  \n" +
            "              from (select  \n" +
            "                           calendar_date \n" +
            "                      from ops_sys_calendar \n" +
            "                     where calendar_date <=#{range} \n" +
            "                       and calendar_date>=#{today} \n" +
            "                       and trade='y' order by calendar_date ) c1  ) c2 where c2.rownum=2")
    String nextWorkdayFixed(@Param("today") String today, @Param("range") String rangeDate);

    @Select("select calendar_date from (select  ROW_NUMBER() OVER(ORDER BY calendar_date) rownum,c1.calendar_date  \n" +
            "              from (select  \n" +
            "                           calendar_date \n" +
            "                      from ops_sys_calendar \n" +
            "                     where calendar_date <=#{range} \n" +
            "                       and calendar_date>=#{today} \n" +
            "                       and trade='y' order by calendar_date ) c1  ) c2 where c2.rownum=#{of}")
    String nextWorkday(@Param("today") String day,@Param("range") String rangeDate, @Param("of") Integer offset);
    @Select("select calendar_date from (select ROW_NUMBER() OVER(ORDER BY calendar_date) rownum,c1.calendar_date \n" +
            "  from (select \n" +
            "               calendar_date\n" +
            "          from ops_sys_calendar\n" +
            "         where calendar_date >=#{rangeDate}\n" +
            "           and calendar_date<=#{date}\n" +
            "           and trade='y' order by calendar_date desc) c1  ) c2 where c2.rownum=#{of}")
    String listRowNumber(@Param("date") String day, @Param("rangeDate") String rangeDate, @Param("of") int abs);
}
