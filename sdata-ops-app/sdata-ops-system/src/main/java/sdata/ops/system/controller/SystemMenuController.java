package sdata.ops.system.controller;


import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysMenu;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.api.UserConstants;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.OpsSysMenuService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/systemMenu")
@RequiredArgsConstructor
public class SystemMenuController {


    private final OpsSysMenuService opsMenu;

    /**
     * 获取菜单列表
     */
    //@RequiresPermissions("system:menu:list")
    @GetMapping("/list")
    public R<Object> list(OpsSysMenu menu) {
        String userId = SecureUtil.currentUserId();
        List<OpsSysMenu> menus = opsMenu.selectMenuList(menu, userId);
        return R.data(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    // @RequiresPermissions("system:menu:query")
    @GetMapping(value = "/{id}")
    public R<Object> getInfo(@PathVariable Long id) {
        return R.data(opsMenu.selectMenuById(id));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public R<Object> treeselect(OpsSysMenu menu) {
        List<OpsSysMenu> menus = opsMenu.selectMenuListNoAuth(menu);
        return R.data(opsMenu.buildMenuTree(menus));
    }


    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselectByAuth")
    public R<Object> treeSelectByAuth(OpsSysMenu menu) {
        List<OpsSysMenu> menus = opsMenu.selectMenuListByAuth(menu,SecureUtil.currentUserId());
        return R.data(opsMenu.buildMenuTree(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public R<Object> roleMenuTreeselect(@PathVariable("roleId") String roleId) {
        String userId = SecureUtil.currentUserId();
        List<OpsSysMenu> menus = opsMenu.selectMenuList(userId);
        Map<String, Object> ajax = MapUtil.empty();
        ajax.put("checkedKeys", opsMenu.selectMenuListByRoleId(roleId));
        ajax.put("menus", opsMenu.buildMenuTreeSelect(menus));
        return R.data(ajax);
    }

    /**
     * 新增菜单
     */
    // @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Object> add(@RequestBody OpsSysMenu menu) {
        if (!opsMenu.checkMenuNameUnique(menu)) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !HttpUtil.isHttp(menu.getPath())) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        return R.data(opsMenu.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    //@RequiresPermissions("system:menu:edit")
    //@Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public R<Object> edit(@Validated @RequestBody OpsSysMenu menu) {
        if (!opsMenu.checkMenuNameUnique(menu)) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !HttpUtil.isHttp(menu.getPath())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        if (menu.getId().equals(menu.getParentId())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        return R.data(opsMenu.updateMenu(menu));
    }

    /**
     * 删除菜单
     */
    //@RequiresPermissions("system:menu:remove")
    //@Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @GetMapping("/delete/{id}")
    public R<Object> remove(@PathVariable("id") Long menuId) {
        if (opsMenu.hasChildByMenuId(menuId)) {
            return R.fail("存在子菜单,不允许删除");
        }
        if (opsMenu.checkMenuExistRole(menuId)) {
            return R.fail("菜单已分配,不允许删除");
        }
        return R.data(opsMenu.deleteMenuById(menuId));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<Object> getRouters() {
        String userId = SecureUtil.currentUserId();
        List<OpsSysMenu> menus = opsMenu.selectMenuTreeByUserId(userId);
        return R.data(opsMenu.buildMenus(menus));
    }

    /**
     * 修改菜单状态  - 显示隐藏
     *
     * @param id     菜单id
     * @param status 状态
     * @return bool
     */
    @GetMapping("/menuVisible")
    public R<Object> editVisible(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        opsMenu.updateMenuVisible(id, status);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }

    /**
     * 修改菜单状态  - 显示隐藏
     *
     * @param id     菜单id
     * @param status 状态
     * @return bool
     */
    @GetMapping("/menuStatus")
    public R<Object> editStatus(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        opsMenu.updateMenuStatus(id, status);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }
}
