package sdata.ops.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.entity.OpsViewPreferences;
import sdata.ops.base.system.model.vo.OpsViewPreferencesVO;
import sdata.ops.base.system.model.vo.OpsViewVO;
import sdata.ops.base.system.model.vo.ShortOpsViewVO;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsViewMapper;
import sdata.ops.system.service.OpsViewGroupRelationService;
import sdata.ops.system.service.OpsViewPreferencesService;
import sdata.ops.system.service.OpsViewService;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_view】的数据库操作Service实现
 * @createDate 2025-03-17 10:05:16
 */
@Service
@RequiredArgsConstructor
public class OpsViewServiceImpl extends ServiceImpl<OpsViewMapper, OpsView>
        implements OpsViewService {


    private final OpsViewPreferencesService preferencesService;

    private final OpsViewGroupRelationService groupRelation;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveView(OpsView viewEntity) {
        //先插入视图，再插入偏好设置
        String id = viewEntity.getId();
        if (StringUtils.hasText(id)) {
            baseMapper.deletedReal(viewEntity.getId());
            save(viewEntity);
        } else {
            id = IdWorker.getId() + "";
            viewEntity.setId(id);
            save(viewEntity);
            //插入偏好设置
            OpsViewPreferences preferences = new OpsViewPreferences();
            preferences.setViewId(id);
            preferences.setIsVisible(0);
            preferences.setUserId(SecureUtil.currentUserId());
            preferencesService.save(preferences);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteView(String id) {
        baseMapper.deletedReal(id);
        preferencesService.remove(Wrappers.lambdaQuery(OpsViewPreferences.class).eq(OpsViewPreferences::getViewId, id));
    }

    @Override
    public OpsViewVO getAllViewInfo(String id) {
        OpsView view = getById(id);
        if (view != null) {
            return new OpsViewVO().convert(view, preferencesService.getOne(Wrappers.lambdaQuery(OpsViewPreferences.class).eq(OpsViewPreferences::getViewId, id)).getIsVisible());
        }
        return null;
    }

    @Override
    public List<OpsViewVO> viewList(String id) {
        List<OpsView> list = baseMapper.selectListBySort(id);
        return list.stream().map(i -> new OpsViewVO().convert(i, i.getIsVisible())).collect(Collectors.toList());
    }

    @Override
    public List<ShortOpsViewVO> shortViewList(String id) {
        List<OpsView> list = baseMapper.selectListBySortNoFilter(id);
        return list.stream().map(i -> new ShortOpsViewVO().convert(i, i.getIsVisible())).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removePreferences(List<OpsViewPreferencesVO> preferences) {
        //先删除原来的
        String userId = SecureUtil.currentUserId();
        preferencesService.remove(Wrappers.lambdaQuery(OpsViewPreferences.class).eq(OpsViewPreferences::getUserId, userId));
        //再新增
        preferences.forEach(i -> {
            OpsViewPreferences preferencesEntity = new OpsViewPreferences();
            preferencesEntity.setUserId(userId);
            preferencesEntity.setViewId(i.getViewId());
            preferencesEntity.setIsVisible(i.getIsVisible() ? 0 : 1);
            preferencesService.save(preferencesEntity);
        });
    }

}
