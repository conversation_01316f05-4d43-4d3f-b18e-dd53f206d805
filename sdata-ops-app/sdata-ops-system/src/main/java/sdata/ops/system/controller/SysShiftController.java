package sdata.ops.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.OpsSysShiftDTO;
import sdata.ops.base.system.model.entity.OpsSysShift;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsSysArrangeService;
import sdata.ops.system.service.OpsSysShiftService;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:37
 */
@RestController
@RequestMapping("/sysShift")
@RequiredArgsConstructor
public class SysShiftController {


    private final OpsSysShiftService opsSysShiftService;

    private final OpsSysArrangeService opsSysArrangeService;

    /**
     * 获取班次列表
     */
    @PostMapping("/list")
    public R<Object> list(@RequestBody OpsSysShiftDTO opsSysOrg) {
        IPage<OpsSysShift> list = opsSysShiftService.list(opsSysOrg);
        return R.data(list);
    }

    /**
     * 添加班次
     */
    @PostMapping("/save")
    public R<Object> save(@RequestBody OpsSysShift opsSysShift) {
        opsSysShiftService.save(opsSysShift);
        return R.success("添加成功");
    }

    /**
     * 修改班次
     */
    @PostMapping("/update")
    public R<Object> update(@RequestBody OpsSysShift opsSysShift) {
        opsSysShiftService.updateById(opsSysShift);
        return R.success("修改成功");
    }

    /**
     * 删除班次
     */
    @GetMapping("/del")
    public R<Object> del(@RequestParam("id") String id) {
        if (opsSysArrangeService.findByShiftId(id) > 0) {
            return R.data(false);
        }
        opsSysShiftService.removeById(id);
        return R.data(true);
    }

    /**
     * 通过ID获取班次
     */
    @GetMapping("/getById")
    public R<Object> getById(@RequestParam("id") String id) {
        return R.data(opsSysShiftService.getById(id));
    }

    /**
     * 通过岗位获取班次列表
     */
    @GetMapping("/getByOrgId")
    public R<Object> getByOrgId(@RequestParam("id") String id) {
        return R.data(opsSysShiftService.getByOrgId(id));
    }

}
