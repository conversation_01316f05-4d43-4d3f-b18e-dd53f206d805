package sdata.ops.system.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.entity.OpsViewTemplate;

/**
* <AUTHOR>
* @description 针对表【ops_view_template】的数据库操作Service
* @createDate 2025-05-28 19:20:00
*/
public interface OpsViewTemplateService extends IService<OpsViewTemplate> {

    IPage<OpsViewTemplate> pageCustom(IPage<OpsViewTemplate> pageEntity, Integer groupId, String name);

    void saveRelation(OpsViewGroupRelation ls);
}
