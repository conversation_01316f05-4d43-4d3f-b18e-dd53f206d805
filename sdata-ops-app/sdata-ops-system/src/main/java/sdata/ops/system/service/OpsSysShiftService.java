package sdata.ops.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.dto.OpsSysShiftDTO;
import sdata.ops.base.system.model.entity.OpsSysShift;

import java.util.List;

public interface OpsSysShiftService extends IService<OpsSysShift> {
    IPage<OpsSysShift> list(OpsSysShiftDTO opsSysOrg);

    List<OpsSysShift> getByOrgId(String id);
}
