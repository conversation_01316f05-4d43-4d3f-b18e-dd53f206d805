package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import sdata.ops.base.system.model.entity.OpsTaskTemplateRelation;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_task_template_relation(菜单权限表)】的数据库操作Mapper
* @createDate 2024-07-10 20:11:30
* @Entity generator.domain.OpsTaskTemplateRelation
*/
@Mapper
public interface OpsTaskTemplateRelationMapper extends BaseMapper<OpsTaskTemplateRelation> {

    @Select("select  a.task_replica_id from ops_task_template_relation a where a.template_id=#{templateId}")
    List<String> findReplicaIds(@Param("templateId") String templateId);
}




