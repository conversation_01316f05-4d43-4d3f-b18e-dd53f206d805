package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.common.api.UserConstants;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsSysRoleMenuMapper;
import sdata.ops.system.mapper.OpsSysRoleOrgMapper;
import sdata.ops.system.mapper.OpsSysUserRoleMapper;
import sdata.ops.system.service.OpsSysRoleService;
import sdata.ops.system.mapper.OpsSysRoleMapper;
import org.springframework.stereotype.Service;
import sdata.ops.system.service.SystemUserService;

import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_role(角色信息表)】的数据库操作Service实现
 * @createDate 2024-06-03 19:48:03
 */
@Service
@RequiredArgsConstructor
public class OpsSysRoleServiceImpl extends ServiceImpl<OpsSysRoleMapper, OpsSysRole>
        implements OpsSysRoleService {


    private final OpsSysRoleMapper roleMapper;

    private final OpsSysRoleMenuMapper roleMenuMapper;

    private final OpsSysUserRoleMapper userRoleMapper;

    private final OpsSysRoleOrgMapper roleDeptMapper;

    private final SystemUserService userService;

    @Override
    public List<OpsSysRole> selectRoleList(OpsSysRole role) {
        return roleMapper.selectRoleList(role);
    }

    @Override
    public Map<String, List<String>> queryRoleWithMenuIds() {
        List<OpsSysRoleMenu> ls = roleMenuMapper.selectList(Wrappers.emptyWrapper());
        Map<String, List<String>> res = new HashMap<>();

        for (OpsSysRoleMenu l : ls) {
            if (res.containsKey(l.getRoleId() + "")) {
                res.get(l.getRoleId() + "").add(l.getMenuId() + "");
            } else {
                List<String> cap = new ArrayList<>();
                cap.add(l.getMenuId() + "");
                res.put(l.getRoleId() + "", cap);
            }
        }
        return res;
    }

    @Override
    public List<OpsSysRole> selectRolesByUserId(Long userId) {
        List<OpsSysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
        List<OpsSysRole> roles = selectRoleAll();
        for (OpsSysRole role : roles) {
            for (OpsSysRole userRole : userRoles) {
                if (role.getId().longValue() == userRole.getId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<OpsSysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (OpsSysRole perm : perms) {
            if (Objects.nonNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public List<OpsSysRole> selectRoleAll() {
        return selectRoleList(new OpsSysRole());
    }

    @Override
    public List<Long> selectRoleListByUserId(Long userId) {
        return roleMapper.selectRoleListByUserId(userId);
    }

    @Override
    public OpsSysRole selectRoleById(Long roleId) {
        return roleMapper.selectRoleById(roleId);
    }

    @Override
    public boolean checkRoleNameUnique(OpsSysRole role) {
        long roleId = (role.getId() == null) ? -1L : role.getId();
        OpsSysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
        if (Objects.nonNull(info) && info.getId() != roleId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public boolean checkRoleKeyUnique(OpsSysRole role) {
        long roleId = role.getId() == null ? -1L : role.getId();
        OpsSysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (Objects.nonNull(info) && info.getId() != roleId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void checkRoleAllowed(OpsSysRole role) {
        if (StringUtils.hasText(role.getId().toString()) && role.getId() == 1L) {
            throw new RuntimeException("不允许操作超级管理员角色");
        }
    }

    @Override
    public void checkRoleDataScope(Long... roleIds) {
        if (!SecureUtil.isAdmin(SecureUtil.currentUserId())) {
            for (Long roleId : roleIds) {
                OpsSysRole role = new OpsSysRole();
                role.setId(roleId);
                List<OpsSysRole> roles = selectRoleList(role);
                if (Objects.isNull(roles)) {
                    throw new RuntimeException("没有权限访问角色数据！");
                }
            }
        }
    }

    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(OpsSysRole role) {
        roleMapper.insert(role);
        return insertRoleMenu(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(OpsSysRole role) {
        // 修改角色信息
        roleMapper.updateById(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getId());
        return insertRoleMenu(role);
    }

    @Override
    public int updateRoleStatus(OpsSysRole role) {
        return roleMapper.updateRole(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(OpsSysRole role) {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(role.getId());
        // 新增角色和部门信息（数据权限）
        return insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(OpsSysRole role) {
        int rows = 0;
        // 新增用户与角色管理
        List<OpsSysRoleMenu> list = new ArrayList<>();
        if(role.getMenuIds()!=null) {
            for (String menuId : role.getMenuIds()) {
                OpsSysRoleMenu rm = new OpsSysRoleMenu();
                rm.setRoleId(role.getId());
                rm.setMenuId(Long.parseLong(menuId));
                list.add(rm);
            }
            if (!list.isEmpty()) {
                rows = roleMenuMapper.batchRoleMenu(list);
            }
        }
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(OpsSysRole role) {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<OpsSysRoleOrg> list = new ArrayList<>();
        for (Long deptId : role.getDeptIds()) {
            OpsSysRoleOrg rd = new OpsSysRoleOrg();
            rd.setRoleId(role.getId());
            rd.setOrgId(deptId);
            list.add(rd);
        }
        if (!list.isEmpty()) {
            rows = roleDeptMapper.batchRoleDept(list);
        }
        return rows;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(Long roleId) {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    @Override
    public int deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            checkRoleAllowed(new OpsSysRole(roleId));
            checkRoleDataScope(roleId);
            OpsSysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new RuntimeException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDept(roleIds);
        return roleMapper.deleteRoleByIds(roleIds);
    }

    @Override
    public int deleteAuthUser(OpsSysUserRole userRole) {
        return userRoleMapper.deleteUserRoleInfo(userRole);
    }

    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds) {
        return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAuthUsers(Long roleId, Long[] userIds) {
        // 新增用户与角色管理
        List<OpsSysUserRole> list = new ArrayList<>();
        for (Long userId : userIds) {
            Long count=userRoleMapper.selectCount(Wrappers.lambdaQuery(OpsSysUserRole.class).eq(OpsSysUserRole::getRoleId,roleId).eq(OpsSysUserRole::getUserId,userId));
            if(count==0L) {
                OpsSysUserRole ur = new OpsSysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
        }
        //更新用户信息中的role字段字符
        for (OpsSysUserRole item : list) {
            //先查再更新
            SystemUser user = userService.getById(item.getUserId());
            //如果不为空
            String newDept = "";
            if (StringUtils.hasText(user.getRole())) {
                newDept += user.getRole() + ",";
            }
            newDept += item.getRoleId();
            userService.update(Wrappers.lambdaUpdate(SystemUser.class).set(SystemUser::getRole, newDept).eq(SystemUser::getId, item.getUserId()));
        }
        return userRoleMapper.batchUserRole(list);
    }
}




