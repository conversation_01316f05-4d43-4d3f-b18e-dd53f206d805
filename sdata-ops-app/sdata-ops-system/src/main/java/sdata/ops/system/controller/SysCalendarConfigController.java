package sdata.ops.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysCalendarConfig;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsSysCalendarConfigService;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/09/02 17:07
 */
@RestController
@RequestMapping("/calendar/config")
@RequiredArgsConstructor
public class SysCalendarConfigController {

    private final OpsSysCalendarConfigService calendarConfigService;

    /**
     * 列表接口
     */
    @GetMapping("/list")
    public R<?> list(
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market
    ) {
        LambdaQueryWrapper<OpsSysCalendarConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OpsSysCalendarConfig::getMarket,market);
        queryWrapper.orderByAsc(OpsSysCalendarConfig::getCreateTime);
        queryWrapper.orderByAsc(OpsSysCalendarConfig::getId);
        return R.data(calendarConfigService.list(queryWrapper));
    }

    /**
     * 保存接口
     */
    @PostMapping("/save")
    public R<?> save(
            @RequestBody OpsSysCalendarConfig dto
    ) {
        List<OpsSysCalendarConfig> dtoList = new ArrayList<>();
        dtoList.add(dto);
        calendarConfigService.customSaveList(dtoList);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 批量保存接口
     */
    @PostMapping("/saveBatch")
    public R<?> saveBatch(
            @RequestBody List<OpsSysCalendarConfig> dtoList
    ) {
        calendarConfigService.customSaveList(dtoList);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 批量删除接口
     */
    @PostMapping("/deleteBatch")
    public R<?> deleteBatch(
            @RequestBody List<String> ids
    ) {
        calendarConfigService.customDeleteBatch(ids);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

}
