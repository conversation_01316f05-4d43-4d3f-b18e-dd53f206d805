package sdata.ops.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.dto.OrgListDTO;
import sdata.ops.base.system.model.dto.OrgUserDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.OperationOrgVO;
import sdata.ops.base.system.model.vo.TreeSelect;
import sdata.ops.common.api.UserConstants;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsSysRoleMapper;
import sdata.ops.system.mapper.SystemUserMapper;
import sdata.ops.system.service.OpsSysOrgService;
import sdata.ops.system.mapper.OpsSysOrgMapper;
import org.springframework.stereotype.Service;
import sdata.ops.system.service.OpsSysUserOrgService;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;
import sdata.ops.system.service.OpsTaskTemplateService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_org(部门表)】的数据库操作Service实现
 * @createDate 2024-06-03 19:48:02
 */
@Service
@RequiredArgsConstructor
public class OpsSysOrgServiceImpl extends ServiceImpl<OpsSysOrgMapper, OpsSysOrg>
        implements OpsSysOrgService {

    private final OpsSysOrgMapper deptMapper;

    private final OpsSysRoleMapper roleMapper;

    private final SystemUserMapper systemUserMapper;

    private final OpsSysUserOrgService opsSysUserOrgService;

    private final OpsTaskTemplateService opsTaskTemplateService;

    private final OpsTaskAttrBasicReplicaService opsTaskAttrBasicReplicaService;

    @Override
    public List<OpsSysOrg> selectDeptList(OpsSysOrg dept) {
        return deptMapper.selectDeptList(dept);
    }

    @Override
    public List<TreeSelect> selectDeptTreeList(OpsSysOrg dept) {
        List<OpsSysOrg> depts = selectDeptList(dept);
        return buildDeptTreeSelect(depts);
    }

    @Override
    public List<OpsSysOrg> buildDeptTree(List<OpsSysOrg> depts) {
        List<OpsSysOrg> returnList = new ArrayList<OpsSysOrg>();
        List<Long> tempList = depts.stream().map(OpsSysOrg::getId).collect(Collectors.toList());
        for (OpsSysOrg dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<OpsSysOrg> depts) {
        List<OpsSysOrg> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public List<OpsSysOrg> buildDeptTreeFull(OpsSysOrg dept) {
        List<OpsSysOrg> depts = selectDeptList(dept);
        return buildDeptTree(depts);
    }

    /**
     * 通过用户id查询 该用户权限下所属的全部岗位id
     *
     * @param userId 用户id
     * @return array
     */
    @Override
    public OrgListDTO allOrgIdByUser(String userId) {
        //先查询是否是组织负责人,如果是组织负责人则查询组织下所有类型为岗位的id
        OrgListDTO res = new OrgListDTO();
        res.setLeader(false);
        List<OpsSysOrg> leaderLs = list(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getLeader, userId)
                .ne(OpsSysOrg::getOrgType, "3").eq(OpsSysOrg::getDeleted, 0));
        if (!leaderLs.isEmpty()) {
            List<String> item = new ArrayList<>();
            List<String> ids = leaderLs.stream().map(i -> i.getId() + "").collect(Collectors.toList());
            List<OpsSysOrg> array = selectDeptList(new OpsSysOrg());
            for (OpsSysOrg org : array) {
                for (String s : ids) {
                    if (org.getAncestors() != null && org.getOrgType().equals("3")) {
                        if (List.of(org.getAncestors().split(",")).contains(s)) {
                            item.add(org.getId() + "");
                            break;
                        }
                    }
                }
            }
            res.setLeader(true);
            res.getOrgIds().addAll(item);
        }
        //不是领导则查询用户有哪些岗位id
        List<OpsSysUserOrg> orgLs = opsSysUserOrgService.list(Wrappers.lambdaQuery(OpsSysUserOrg.class)
                .eq(OpsSysUserOrg::getUserId, userId));
        if (!orgLs.isEmpty()) {
            List<String> item = orgLs.stream().map(i -> i.getOrgId() + "").collect(Collectors.toList());
            res.getOrgIds().addAll(item);
        }
        if (!res.getOrgIds().isEmpty()) {
            //由于二级领导身份和自身岗位的关系配置可能会导致岗位id重复,所以要去重一次
            res.setOrgIds(new ArrayList<>(new HashSet<>(res.getOrgIds())));
        }
        return res;
    }


    @Override
    public List<OpsSysOrg> buildDeptTreeFillUserInfo(OpsSysOrg dept) {
        List<OpsSysOrg> depts = selectDeptList(dept);
        OrgListDTO res = this.allOrgIdByUser(SecureUtil.currentUserId());
        Map<String, List<OrgUserDTO>> mapping = systemUserMapper.queryAllUserShortFailName().stream().collect(Collectors.groupingBy(OrgUserDTO::getOrgId));
        List<OpsSysOrg> onlyPost = new ArrayList<>();
        //如果岗位信息为空,则无权限
        if (res.getOrgIds().isEmpty()) {
            return new ArrayList<>();
        }
        for (OpsSysOrg org : depts) {
            //如果是管理员,返回全部
            if (SecureUtil.isAdmin(null)) {
                if (org.getOrgType().equals("3")) {
                    if (mapping.containsKey(org.getId() + "")) {
                        org.setUserlist(mapping.get(org.getId() + ""));
                    }
                    onlyPost.add(org);
                }
            } else {
                if (org.getOrgType().equals("3") && res.getOrgIds().contains(org.getId() + "")) {
                    if (mapping.containsKey(org.getId() + "")) {
                        org.setUserlist(mapping.get(org.getId() + ""));
                    }
                    onlyPost.add(org);
                }
            }
        }
        return buildDeptTree(onlyPost);
    }

    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        OpsSysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.getDeptCheckStrictly() == 0);
    }

    @Override
    public OpsSysOrg selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }

    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    @Override
    public boolean checkDeptNameUnique(OpsSysOrg dept) {
        long deptId = dept.getId() == null ? -1L : dept.getId();
        OpsSysOrg info = deptMapper.checkDeptNameUnique(dept.getOrgName(), dept.getParentId());
        if (Objects.nonNull(info) && info.getId() != deptId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!SecureUtil.isAdmin(SecureUtil.currentUserId()) && !StringUtils.hasText(deptId.toString())) {
            OpsSysOrg dept = new OpsSysOrg();
            dept.setId(deptId);
            List<OpsSysOrg> depts = selectDeptList(dept);
            if (ObjectUtil.isEmpty(depts)) {
                throw new RuntimeException("没有权限访问部门数据！");
            }
        }
    }

    @Override
    public int insertDept(OpsSysOrg dept) {
        OpsSysOrg info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (Objects.nonNull(info) && !UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new RuntimeException("部门停用，不允许新增");
        }
        dept.setAncestors(info == null ? "0" : info.getAncestors() + "," + dept.getParentId());
        return deptMapper.insert(dept);
    }

    @Override
    public int updateDept(OpsSysOrg dept) {
        OpsSysOrg newParentDept = deptMapper.selectDeptById(dept.getParentId());
        OpsSysOrg oldDept = deptMapper.selectDeptById(dept.getId());
        if (Objects.nonNull(newParentDept) && Objects.nonNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getId(), newAncestors, oldAncestors);
        }
        if(Objects.isNull(newParentDept)&& Objects.nonNull(oldDept)){
            String newAncestors = "0";
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateById(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.hasText(dept.getAncestors())
                && !dept.getAncestors().equals("0")) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(OpsSysOrg dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<OpsSysOrg> children = deptMapper.selectChildrenDeptById(deptId);
        for (OpsSysOrg child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            deptMapper.updateDeptChildren(children);
        }
    }

    @Override
    public int deleteDeptById(Long deptId) {
        return deptMapper.deleteDeptById(deptId);
    }

    @Override
    public List<OpsSysOrg> queryOrgByType3() {
        LambdaQueryWrapper<OpsSysOrg> query = new LambdaQueryWrapper<>();
        query.eq(OpsSysOrg::getOrgType, 3);
        return list(query);
    }

    @Override
    public Map<String, Integer> getSortMap() {
        LambdaQueryWrapper<OpsSysOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsSysOrg::getOrgType, 3);
        return list(queryWrapper).stream().collect(Collectors.toMap(i -> i.getId() + "", OpsSysOrg::getOrderNum));
    }

    @Override
    public List<OpsSysOrg> deptConPostInfos() {
        List<OpsSysOrg> ls = list(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getDeleted, 0));
        List<OpsSysOrg> res = new ArrayList<>();
        for (OpsSysOrg l : ls) {
            if (l.getStSign() == 1) {
                res.add(l);
            }
        }
        for (OpsSysOrg re : res) {
            for (OpsSysOrg l : ls) {
                if (l.getOrgType().equals("3") && l.getAncestors() != null && l.getAncestors().contains(re.getId().toString())) {
                    re.getIdMap().put(String.valueOf(l.getId()), l);
                }
            }
        }
        return res;
    }


    /**
     * 递归列表
     */
    private void recursionFn(List<OpsSysOrg> list, OpsSysOrg t) {
        // 得到子节点列表
        List<OpsSysOrg> childList = getChildList(list, t);
        t.setChildren(childList);
        for (OpsSysOrg tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<OpsSysOrg> getChildList(List<OpsSysOrg> list, OpsSysOrg t) {
        List<OpsSysOrg> tlist = new ArrayList<>();
        for (OpsSysOrg n : list) {
            if (n.getParentId() != null && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<OpsSysOrg> list, OpsSysOrg t) {
        return !getChildList(list, t).isEmpty();
    }

    @Override
    public List<OpsSysOrg> getOrgByPerm() {
        OpsSysOrg opsSysOrg = new OpsSysOrg();
        List<OpsSysOrg> opsSysOrgList = buildDeptTreeFull(opsSysOrg);
        return SecureUtil.isAdmin(null) ? opsSysOrgList : opsSysOrgList.stream().filter(e -> e.getId().equals(Long.parseLong(systemUserMapper.selectById(SecureUtil.currentUserId()).getOwnerDept()))).collect(Collectors.toList());
    };

    @Override
    public List<OpsTaskTemplate> templateInOrg(String orgId){
        LambdaQueryWrapper<OpsTaskTemplate> opsTaskTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        opsTaskTemplateLambdaQueryWrapper.eq(OpsTaskTemplate::getOrgId,orgId);
        List<OpsTaskTemplate> opsTaskTemplateList = opsTaskTemplateService.list(opsTaskTemplateLambdaQueryWrapper);
        LambdaQueryWrapper<OpsTaskAttrBasicReplica> opsTaskAttrBasicReplicaLambdaQueryWrapper = new LambdaQueryWrapper<>();
        opsTaskAttrBasicReplicaLambdaQueryWrapper.select(OpsTaskAttrBasicReplica::getTaskBindTemplateId).in(OpsTaskAttrBasicReplica::getTaskBindTemplateId,opsTaskTemplateList.stream().map(OpsTaskTemplate::getId).collect(Collectors.toList())).eq(OpsTaskAttrBasicReplica::getImportStatus,1);
        List<String> tempLs = opsTaskAttrBasicReplicaService.list(opsTaskAttrBasicReplicaLambdaQueryWrapper).stream().map(OpsTaskAttrBasicReplica::getTaskBindTemplateId).collect(Collectors.toList());
        opsTaskTemplateList = opsTaskTemplateList.stream().filter(template ->!tempLs.contains(template.getId())).collect(Collectors.toList());
        return opsTaskTemplateList;
    }

    @Override
    public List<SystemUser> userInOrg(String orgId){
        LambdaQueryWrapper<SystemUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemUser::getDeleted,0);
        if(!SecureUtil.isAdmin(null)){
            lambdaQueryWrapper.eq(SystemUser::getOwnerDept,systemUserMapper.selectById(SecureUtil.currentUserId()).getOwnerDept());
        }
        return systemUserMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<OperationOrgVO> operationOrgInfo() {
        List<OpsSysOrg>  all= deptMapper.selectList(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getDeleted,0));
        //先去有效的二级部门
        List<OpsSysOrg>  second=all.stream().filter(e->e.getOrgType().equals("2")&&e.getStSign()==1).collect(Collectors.toList());
        //再取所有类型为岗位的，并转换为map key是id value是OpsSysOrg
        List<OpsSysOrg> postMap=all.stream().filter(e->e.getOrgType().equals("3")).collect(Collectors.toList());
        List<OperationOrgVO>  res=new ArrayList<>();
        for (OpsSysOrg org : second) {
            OperationOrgVO operationOrgVO=new OperationOrgVO();
            operationOrgVO.setId(org.getId().toString());
            operationOrgVO.setName(org.getOrgName());
            operationOrgVO.setType(org.getOrgType());
            operationOrgVO.setChild(parsePost(org.getId(),postMap));
            res.add(operationOrgVO);
        }
        return res;
    }

    private List<OperationOrgVO> parsePost(Long id, List<OpsSysOrg> postMap) {
        List<OperationOrgVO> res=new ArrayList<>();
        postMap.forEach(items->{
           List<String>  keys= List.of(items.getAncestors().split(","));
           if(keys.contains(id+"")){
               OperationOrgVO item=new OperationOrgVO();
               item.setId(items.getId()+"");
               item.setName(items.getOrgName());
               item.setType(items.getOrgType());
               res.add(item);
           }
        });
        return res;
    }

}




