package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.OpsSysUserOrg;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.OrgUserVO;
import sdata.ops.system.mapper.OpsSysUserOrgMapper;
import sdata.ops.system.service.OpsSysUserOrgService;
import org.springframework.stereotype.Service;
import sdata.ops.system.service.SystemUserService;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_user_org(用户与组织机构关联表)】的数据库操作Service实现
 * @createDate 2024-06-03 19:48:03
 */
@Service
public class OpsSysUserOrgServiceImpl extends ServiceImpl<OpsSysUserOrgMapper, OpsSysUserOrg> implements OpsSysUserOrgService {





    private  SystemUserService userService;

    @Lazy
    @Autowired
    public void setUserService(SystemUserService userService) {
        this.userService = userService;
    }

    @Override
    public List<SystemUser> selectAllocatedList(String orgId) {
        return baseMapper.queryUserInfoByOrgId(orgId);
    }

    @Override
    public List<SystemUser> selectUnAllocatedList(String orgId, String name, String phoneNum) {
        if (StringUtils.hasText(name)) {
            name = "'%" + name + "%'";
        }
        if (StringUtils.hasText(phoneNum)) {
            phoneNum = "'%" + phoneNum + "%'";
        }
        return baseMapper.queryUserInfoNotAuthByOrgId(orgId, name, phoneNum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUpdateRelation(OrgUserVO orgUserVO) {
        List<OpsSysUserOrg> ls = new ArrayList<>();
        for (String userId : orgUserVO.getUserIds()) {
            long count = count(Wrappers.lambdaQuery(OpsSysUserOrg.class).eq(OpsSysUserOrg::getUserId, userId).eq(OpsSysUserOrg::getOrgId, orgUserVO.getOrgId()));
            if (count == 0L) {
                OpsSysUserOrg saveItem = new OpsSysUserOrg();
                saveItem.setOrgId(Long.valueOf(orgUserVO.getOrgId()));
                saveItem.setUserId(Long.valueOf(userId));
                ls.add(saveItem);
            }
        }
        //更新用户实体org字段
        for (OpsSysUserOrg item : ls) {
            //先查再更新
            SystemUser user = userService.getById(item.getUserId());
            //如果不为空
            String newDept = "";
            if (StringUtils.hasText(user.getDept())) {
                newDept += user.getDept() + ",";
            }
            newDept += item.getOrgId();
            userService.update(Wrappers.lambdaUpdate(SystemUser.class).set(SystemUser::getDept, newDept).eq(SystemUser::getId, item.getUserId()));
        }
        saveBatch(ls);
    }

    @Override
    public List<String> diffInfoBy(String fromId, String toId) {
        return baseMapper.diffInfoBy(fromId,toId);
    }


}




