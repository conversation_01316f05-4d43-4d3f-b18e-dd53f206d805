package sdata.ops.system.controller;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sdata.ops.base.system.model.entity.OpsTaskGenInfoFile;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsTaskGenInfoFileService;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class SystemFileUploadController {


    private final OpsTaskGenInfoFileService opsTaskGenInfoFileService;

    /**
     * 获取当前信息上传附件列表
     *
     * @param taskId
     * @return
     */
    @GetMapping("/fileList")
    public R<List<OpsTaskGenInfoFile>> fileList(@RequestParam("taskId") Long taskId) {
        return R.data(opsTaskGenInfoFileService.getListById(taskId));
    }

    /**
     * 获取当前信息上传附件数量
     *
     * @param taskId
     * @return
     */
    @GetMapping("/fileCount")
    public R<Integer> fileCount(@RequestParam("taskId") Long taskId) {
        return R.data(opsTaskGenInfoFileService.getListById(taskId).size());
    }

    /**
     * 删除附件
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Object> deleteFile(@RequestParam("id") Long id) {
        return opsTaskGenInfoFileService.removeById(id) ? R.success("删除成功") : R.fail("删除失败");
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    @PostMapping("/file")
    public R<Object> uploadFile(@RequestParam("taskId") Long taskId, @RequestParam("file") MultipartFile file) {
        return opsTaskGenInfoFileService.uploadFile(taskId, file);
    }

    /**
     * 上传文件,并触发脚本,执行逻辑
     *
     * @param file 文件
     * @return bool
     */
    @PostMapping("/fileTrigger")
    public R<Object> uploadFileTrigger(@RequestParam("taskId") Long taskId,
                                       @RequestParam("indicatorId")String indicatorId,
                                       @RequestParam("file") MultipartFile file,@RequestParam(value = "date",required = false)String date) {
        return opsTaskGenInfoFileService.uploadFileReturnInfo(taskId, indicatorId,file,date);
    }

    @GetMapping("/download")
    public R<String> download(@RequestParam("id") String id, HttpServletResponse response) {
        OpsTaskGenInfoFile opsTaskGenInfoFile = opsTaskGenInfoFileService.getById(id);
        String fileNotFoundMsg = "文件不存在";
        if (ObjectUtil.isEmpty(opsTaskGenInfoFile)) {
            return R.fail(fileNotFoundMsg);
        }
        File file = new File(opsTaskGenInfoFile.getFilePath(), opsTaskGenInfoFile.getName());
        if (!file.exists() && file.canRead()) {
            return R.fail(fileNotFoundMsg);
        }
        try (FileInputStream is = new FileInputStream(file)) {
            writeResponse(response, file, is);
            return null;
        } catch (Exception e) {
            log.error("下载文件错误", e);
            return R.fail(fileNotFoundMsg);
        }
    }

    @GetMapping("/downloadByIndicator")
    public R<String> downloadByIndicator(@RequestParam("taskId") String taskId, @RequestParam("indicatorId") String indicatorId,
                                    @RequestParam("startDate")String start,@RequestParam("endDate") String end,
                                    HttpServletResponse response){
        //通过任务单元id获取绑定指标
        String path=opsTaskGenInfoFileService.getByPath(taskId,indicatorId,start,end);
        String fileNotFoundMsg = "文件不存在";
        if (path == null) {
            return R.fail(fileNotFoundMsg);
        }
        File file = new File(path);
        if (!file.exists() && file.canRead()) {
            return R.fail(fileNotFoundMsg);
        }
        try (FileInputStream is = new FileInputStream(file)) {
            writeResponse(response, file, is);
            return null;
        } catch (Exception e) {
            log.error("任务单元-自定义配置按钮下载文件错误", e);
            return R.fail("自定义配置按钮下载文件错误");
        }
    }

    private static void writeResponse(HttpServletResponse response, File file, FileInputStream is) throws IOException {
        // 设置响应头
        String cdName = "Content-Disposition";
        String cdValue = "attachment; filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8);

        response.setCharacterEncoding(StandardCharsets.UTF_8.displayName());
        response.addHeader("Access-Control-Expose-Headers", cdName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setContentLengthLong(file.length());
        response.setHeader(cdName, cdValue);
        IOUtils.copy(is, response.getOutputStream());
    }
}
