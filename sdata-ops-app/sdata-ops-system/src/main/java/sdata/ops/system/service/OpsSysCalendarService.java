package sdata.ops.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.dto.OpsSysCalendarDTO;
import sdata.ops.base.system.model.entity.OpsSysCalendar;
import sdata.ops.system.job.TaskException;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
public interface OpsSysCalendarService extends IService<OpsSysCalendar> {

    /**
     * 初始化日历
     *
     * @param startYear 开始年度 yyyy
     * @param endYear   结束年度 yyyy
     */
    void init(String startYear, String endYear,String market);

    /**
     * 更新日历
     *
     * @param dto 节假日的日期
     */
    void updateCalendar(OpsSysCalendarDTO dto);


    Date queryFeuDate(Integer taskDeferredCount, Date taskEndTime) throws TaskException;

    List<String> listByDate(String startDate, String endDate);


    String getLastWorkByFixed(String date, String rangeDate);

    String getNextWorkByFixed(String today, String rangeDate);

    String getNextWork(String day, String rangeDate, Integer offset);

    String getLastWork(String day, String rangeDate, int abs);


    List<OpsSysCalendar> listByDateAllMarket(String startDate, String endDate);

    /**
     * 判定指定日期在指定市场下是否为交易日
     *
     * @param date   日期
     * @param market 市场类别
     * @return 是交易日返回true,否则false
     */
    Boolean isTradeDay(LocalDate date, String market);
}
