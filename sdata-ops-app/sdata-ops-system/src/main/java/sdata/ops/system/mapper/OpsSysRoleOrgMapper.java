package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsSysRoleOrg;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_role_org(角色和机构岗位关联表)】的数据库操作Mapper
* @createDate 2024-06-03 19:48:03
* @Entity sdata.ops.base.system.api.entity.OpsSysRoleOrg
*/
@Mapper
public interface OpsSysRoleOrgMapper extends BaseMapper<OpsSysRoleOrg> {
    /**
     * 通过角色ID删除角色和部门关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
     int deleteRoleDeptByRoleId(Long roleId);

    /**
     * 批量删除角色部门关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
     int deleteRoleDept(Long[] ids);

    /**
     * 查询部门使用数量
     *
     * @param deptId 部门ID
     * @return 结果
     */
     int selectCountRoleDeptByDeptId(Long deptId);

    /**
     * 批量新增角色部门信息
     *
     * @param roleDeptList 角色部门列表
     * @return 结果
     */
     int batchRoleDept(List<OpsSysRoleOrg> roleDeptList);
}




