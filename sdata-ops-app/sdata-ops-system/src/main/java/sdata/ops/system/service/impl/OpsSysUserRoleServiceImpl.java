package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import sdata.ops.base.system.model.entity.OpsSysUserRole;
import sdata.ops.system.service.OpsSysUserRoleService;
import sdata.ops.system.mapper.OpsSysUserRoleMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ops_sys_user_role(用户和角色关联表)】的数据库操作Service实现
* @createDate 2024-06-03 19:48:03
*/
@Service
public class OpsSysUserRoleServiceImpl extends ServiceImpl<OpsSysUserRoleMapper, OpsSysUserRole>
    implements OpsSysUserRoleService{

}




