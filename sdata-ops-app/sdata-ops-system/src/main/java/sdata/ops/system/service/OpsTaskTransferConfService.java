package sdata.ops.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsTaskTransferConf;
import sdata.ops.base.system.model.vo.TaskTransferVO;

/**
* <AUTHOR>
* @description 针对表【OPS_TASK_TRANSFER_CONF(任务转派配置-未来日期自动转派)】的数据库操作Service
* @createDate 2024-09-10 09:56:31
*/
public interface OpsTaskTransferConfService extends IService<OpsTaskTransferConf> {

    Boolean delayTransfer(TaskTransferVO taskTransferVO);

}
