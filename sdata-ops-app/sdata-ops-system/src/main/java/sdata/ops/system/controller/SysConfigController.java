package sdata.ops.system.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.vo.ConfigVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.WarnConfigService;

@RestController
@RequestMapping("/sysConfig")
@RequiredArgsConstructor
public class SysConfigController {

    private final WarnConfigService warnConfigService;


    @GetMapping("/getByTaskId")
    public R<Object> queryConfig(@RequestParam("taskId") String taskId) {
        return R.data(warnConfigService.queryJson(taskId));
    }


    @PostMapping("/saveConfig")
    public R<Object> saveConfig(@RequestBody ConfigVO configVO) {
        warnConfigService.saveTableConf(configVO.getTaskId(), configVO.getConfig());
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

}
