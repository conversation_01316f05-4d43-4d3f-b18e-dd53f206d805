package sdata.ops.system.controller;


import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.OpsSysRole;
import sdata.ops.base.system.model.entity.OpsSysUserRole;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.OpsSysOrgService;
import sdata.ops.system.service.OpsSysRoleService;
import sdata.ops.system.service.SystemUserService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/systemRole")
@RequiredArgsConstructor
@Slf4j
public class SystemRoleController {

    private final OpsSysRoleService roleService;

    private final SystemUserService userService;

    private final OpsSysOrgService deptService;

    // @RequiresPermissions("system:role:list")
    @ControllerAuditLog(value = "获取角色列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list")
    public R<Object> list(OpsSysRole role) {
        List<OpsSysRole> list = roleService.selectRoleList(role);
        Map<String,List<String>>  as=roleService.queryRoleWithMenuIds();
        for (OpsSysRole sysRole : list) {
            if(as.containsKey(sysRole.getId()+"")) {
                List<String> target=as.get(sysRole.getId()+"");
                sysRole.setMenuIds(target.toArray(new String[0]));
            }
        }
        return R.data(list);
    }


    /**
     * 根据角色编号获取详细信息
     */
    //@RequiresPermissions("system:role:query")
    @ControllerAuditLog(value = "根据角色编号获取详细信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping(value = "/getById")
    public R<Object> getInfo(@RequestParam("id") Long roleId) {
        roleService.checkRoleDataScope(roleId);
        return R.data(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    //@RequiresPermissions("system:role:add")
    // @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @ControllerAuditLog(value = "新增角色", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    @PostMapping("/add")
    public R<Object> add(@RequestBody OpsSysRole role) {
        if (!roleService.checkRoleNameUnique(role)) {
            return R.fail("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!roleService.checkRoleKeyUnique(role)) {
            return R.fail("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        return R.data(roleService.insertRole(role));
    }

    /**
     * 修改保存角色
     */
    // @RequiresPermissions("system:role:edit")
    // @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @ControllerAuditLog(value = "修改保存角色", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PostMapping("/edit")
    public R<Object> edit(@RequestBody OpsSysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getId());
        if (!roleService.checkRoleNameUnique(role)) {
            return R.fail("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!roleService.checkRoleKeyUnique(role)) {
            return R.fail("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        return R.data(roleService.updateRole(role));
    }

    /**
     * 修改保存数据权限
     */
    // @RequiresPermissions("system:role:edit")
    // @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @ControllerAuditLog(value = "修改保存数据权限", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PutMapping("/dataScope")
    public R<Object> dataScope(@RequestBody OpsSysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getId());
        return R.data(roleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    // @RequiresPermissions("system:role:edit")
    // @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @ControllerAuditLog(value = "修改角色状态", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PutMapping("/changeStatus")
    public R<Object> changeStatus(@RequestBody OpsSysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getId());
        return R.data(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    // @RequiresPermissions("system:role:remove")
    //@Log(title = "角色管理", businessType = BusinessType.DELETE)
    @ControllerAuditLog(value = "删除角色", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/delete/{roleIds}")
    public R<Object> remove(@PathVariable Long[] roleIds) {
        return R.data(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    // @RequiresPermissions("system:role:query")
    @ControllerAuditLog(value = "获取角色选择框列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/optionselect")
    public R<Object> optionselect() {
        return R.data(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    // @RequiresPermissions("system:role:list")
    @ControllerAuditLog(value = "查询已分配用户角色列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/authUser/allocatedList")
    public R<Object> allocatedList(SystemUser user) {
        List<SystemUser> list = userService.selectAllocatedList(user);
        return R.data(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    // @RequiresPermissions("system:role:list")
    @ControllerAuditLog(value = "查询未分配用户角色列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/authUser/unallocatedList")
    public R<Object> unallocatedList(SystemUser user) {
        List<SystemUser> list = userService.selectUnallocatedList(user);
        return R.data(list);
    }

    /**
     * 取消授权用户
     */
    // @RequiresPermissions("system:role:edit")
    // @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @ControllerAuditLog(value = "取消授权用户", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PutMapping("/authUser/cancel")
    public R<Object> cancelAuthUser(@RequestBody OpsSysUserRole userRole) {
        return R.data(roleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    // @RequiresPermissions("system:role:edit")
    // @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @ControllerAuditLog(value = "批量取消授权用户", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/authUser/cancelAll")
    public R<Object> cancelAuthUserAll(Long roleId, Long[] userIds) {
        return R.data(roleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权
     */
    //@RequiresPermissions("system:role:edit")
    //@Log(title = "角色管理", businessType = BusinessType.GRANT)
    @ControllerAuditLog(value = "批量选择用户授权", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/authUser/selectAll")
    public R<Object> selectAuthUserAll(Long roleId, Long[] userIds) {
        //roleService.checkRoleDataScope(roleId);
        return R.data(roleService.insertAuthUsers(roleId, userIds));
    }

    /**
     * 获取对应角色部门树列表
     */
    //@RequiresPermissions("system:role:query")
    @ControllerAuditLog(value = "获取对应角色部门树列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping(value = "/deptTree/{roleId}")
    public R<Object> deptTree(@PathVariable("roleId") Long roleId) {
        Map<String,Object> ajax = MapUtil.empty();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.selectDeptTreeList(new OpsSysOrg()));
        return R.data(ajax);
    }

}
