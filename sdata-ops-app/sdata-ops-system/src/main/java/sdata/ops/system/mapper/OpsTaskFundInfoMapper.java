package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/19 10:33
 */
@Mapper
public interface OpsTaskFundInfoMapper extends BaseMapper<OpsTaskFundInfo> {

    List<OpsTaskFundInfo> getTaskFundList(@Param("date") String date, @Param("deptIds") List<String> deptIds, @Param("sign") Integer sign);
    List<OpsTaskFundInfo> getTaskFundListByLeader(@Param("date") String date, @Param("deptIds") List<String> deptIds, @Param("sign") Integer sign);

    List<Map<String, Object>> getMonthFund(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("ids") List<Long> ids);

    List<Map<String, Object>> getDeptFund(@Param("year") String year, @Param("month") String month, @Param("ids") List<Long> ids);

    List<Map<String, Object>> getDelayMonthFund(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("ids") List<Long> ids);

    List<OpsTaskFundInfo> getFundByDate(@Param("date") String date,@Param("taskReplicaId") String taskReplicaId);

    List<OpsTaskFundInfo> queryMergeForDashboard(@Param("date") String date,@Param("ids")List<String> ids);

    List<OpsTaskFundInfo> queryFundInfoForMonitor(@Param("date") String date);

    List<OpsTaskFundInfo> queryFundInfoForMonitorWran(@Param("date") String date);
}
