package sdata.ops.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import sdata.ops.base.system.model.dto.OpsViewGroupDto;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_view_group_relation】的数据库操作Mapper
* @createDate 2025-05-22 16:40:49
* @Entity generator.domain.OpsViewGroupRelation
*/
@Mapper
public interface OpsViewGroupRelationMapper extends BaseMapper<OpsViewGroupRelation> {


    @Select("SELECT group_id,count(1) as count_num FROM ops_view_group_relation GROUP BY group_id")
    List<OpsViewGroupDto> countNumGroupByGroupId();
}
