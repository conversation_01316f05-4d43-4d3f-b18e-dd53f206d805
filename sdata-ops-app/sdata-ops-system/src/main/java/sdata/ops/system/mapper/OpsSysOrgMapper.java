package sdata.ops.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_org(部门表)】的数据库操作Mapper
* @createDate 2024-06-03 19:48:02
* @Entity sdata.ops.base.system.api.entity.OpsSysOrg
*/
@Mapper
public interface OpsSysOrgMapper extends BaseMapper<OpsSysOrg> {

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
     List<OpsSysOrg> selectDeptList(OpsSysOrg dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
     List<Long> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    OpsSysOrg selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门
     *
     * @param deptId 部门ID
     * @return 部门列表
     */
     List<OpsSysOrg> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
     int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
     int hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果
     */
     int checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 结果
     */
    OpsSysOrg checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);



    /**
     * 修改部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
     int updateDept(OpsSysOrg dept);

    /**
     * 修改所在部门正常状态
     *
     * @param deptIds 部门ID组
     */
     void updateDeptStatusNormal(Long[] deptIds);

    /**
     * 修改子元素关系
     *
     * @param depts 子元素
     * @return 结果
     */
     int updateDeptChildren(@Param("depts") List<OpsSysOrg> depts);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
     int deleteDeptById(Long deptId);
}




