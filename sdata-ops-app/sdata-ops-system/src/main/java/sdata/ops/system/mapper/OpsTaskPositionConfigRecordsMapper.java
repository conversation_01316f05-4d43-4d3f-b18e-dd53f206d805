package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsTaskPositionConfigRecords;

/**
* <AUTHOR>
* @description 针对表【OPS_TASK_POSITION_CONFIG_RECORDS(任务模板转派配置记录表)】的数据库操作Mapper
* @createDate 2025-01-20 15:12:54
* @Entity generator.domain.OpsTaskPositionConfigRecords
*/
@Mapper
public interface OpsTaskPositionConfigRecordsMapper extends BaseMapper<OpsTaskPositionConfigRecords> {


}
