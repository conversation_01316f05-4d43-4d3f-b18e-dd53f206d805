package sdata.ops.system.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.PositionConfigDTO;
import sdata.ops.base.system.model.entity.OpsTaskShiftConf;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.OpsTaskPositionConfigRecordsService;
import sdata.ops.system.service.OpsTaskShiftConfService;

import java.util.stream.Collectors;

@RestController
@RequestMapping("/taskShift")
@RequiredArgsConstructor
public class TaskShiftMangerController {


    private final OpsTaskShiftConfService opsTaskshitConfService;

    private final OpsTaskPositionConfigRecordsService taskPositionConfigRecordsService;


    //查询用户下所有人员
    @GetMapping("/userList")
    public R<Object>  findUserListByLeaderId(){
        String userId= SecureUtil.currentUserId();
        return R.data(opsTaskshitConfService.findUserArrByLeaderId(userId).stream().map(i->i.setPassword("")).collect(Collectors.toList()));
    }

    //转派操作列表
    @GetMapping("/list")
    public R<Object> listOfEntity(){
        return R.data(opsTaskshitConfService.listByUserType());
    }
    //转派
    @PostMapping("/shift")
    public R<Object>  operatorShift(@RequestBody OpsTaskShiftConf conf){
        opsTaskshitConfService.operationShiftConf(conf);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    //还原
    @GetMapping("/reset")
    public R<Object>  operatorReset(@RequestParam("id")String id){
        opsTaskshitConfService.resetConf(id);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    /**
     * 人员转派配置新增
     * 选定模板
     * 按步骤进行对模板中人员归属或者复核进行替换
     * 每个步骤检查替换人的权限是否包含被替换人的权限，不包含临时授权增加
     * 模板保存副本
     * 结束
     * @return add
     */
    @PostMapping("/confAdd")
    public R<Object> transferConfAdd(@RequestBody PositionConfigDTO configDTO){
        if(taskPositionConfigRecordsService.checkIsExisit(configDTO)){
            return R.fail("当前模板已经有权限交接配置，需要还原或者覆盖后再次进行操作!");
        }
        taskPositionConfigRecordsService.confAdd(configDTO);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    /**
     * 配置列表读取
     * @return ls
     */
    @GetMapping("/confList")
    public R<Object> confList(){
        return R.data(taskPositionConfigRecordsService.confList());
    }



    /**
     * 本次配置重置还原
     * @param id  recordId
     * @param prem  默认0 不操作 1 重置
     * @return ls
     */
    @GetMapping("/confReset")
    public R<Object> confReset(@RequestParam("id")String id,@RequestParam(value = "prem",defaultValue = "0")Integer prem){
        taskPositionConfigRecordsService.reset(id,prem);
        return R.data(MessageConstant.OPERATOR_SUCCESS);
    }

    /**
     * 本次配置覆盖历史模板
     * @return ls
     */
    @GetMapping("/confOverwrite")
    public R<Object> confOverwrite(@RequestParam("id")String id){
        taskPositionConfigRecordsService.confOverwrite(id);
        return R.data(MessageConstant.OPERATOR_SUCCESS);
    }



}
