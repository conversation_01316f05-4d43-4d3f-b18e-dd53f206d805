package sdata.ops.system.service;

import sdata.ops.base.system.model.dto.OrgListDTO;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsTaskTemplate;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.OperationOrgVO;
import sdata.ops.base.system.model.vo.TreeSelect;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_org(部门表)】的数据库操作Service
 * @createDate 2024-06-03 19:48:02
 */
public interface OpsSysOrgService extends IService<OpsSysOrg> {

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    List<OpsSysOrg> selectDeptList(OpsSysOrg dept);

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    List<TreeSelect> selectDeptTreeList(OpsSysOrg dept);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    List<OpsSysOrg> buildDeptTree(List<OpsSysOrg> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildDeptTreeSelect(List<OpsSysOrg> depts);

    List<OpsSysOrg> buildDeptTreeFull(OpsSysOrg dept);


    /**
     * 获取所有岗位id信息
     *
     * @param userId 用户id
     * @return single
     */
    OrgListDTO allOrgIdByUser(String userId);

    List<OpsSysOrg> buildDeptTreeFillUserInfo(OpsSysOrg dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    OpsSysOrg selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    boolean checkDeptNameUnique(OpsSysOrg dept);

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    int insertDept(OpsSysOrg dept);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    int updateDept(OpsSysOrg dept);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    int deleteDeptById(Long deptId);


    List<OpsSysOrg> queryOrgByType3();

    Map<String, Integer> getSortMap();


    List<OpsSysOrg>  deptConPostInfos();

    List<OpsSysOrg> getOrgByPerm();

    List<OpsTaskTemplate> templateInOrg(String orgId);

    List<SystemUser> userInOrg(String orgId);

    List<OperationOrgVO> operationOrgInfo();
}
