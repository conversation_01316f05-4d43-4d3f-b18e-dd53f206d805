package sdata.ops.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsTaskFundInfoDTO;
import sdata.ops.base.system.model.dto.OpsTaskInfoDeleteDTO;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/19 10:34
 */
public interface OpsTaskFundInfoService extends IService<OpsTaskFundInfo> {

    /**
     * 保存任务明显
     *
     * @param dto 请求参数
     */
    void update(OpsTaskInfoUpdateDTO dto, String operator);

    @Transactional(rollbackFor = Exception.class)
    void saveFromIndicator(OpsTaskInfoUpdateDTO dto, String operator);

    /**
     * 删除任务明显
     *
     * @param dto 请求参数
     */
    void delete(OpsTaskInfoDeleteDTO dto);

    /**
     * 批量保存产品信息
     *
     * @param dataList 数据列表
     */
    void batchUpdateFundInfo(String taskId, String ownerOrgId, List<OpsTaskFundInfoDTO> dataList);

    /**
     * 关联产品的任务列表
     *
     * @param ownerOrgId 岗位ID
     * @return 返回结果
     */
    List<OpsTaskGenInfo> getOpsTaskInfoList(String ownerOrgId, String now);

    List<OpsTaskFundInfo> getTaskFundList(String date, List<String> deptIds);

    List<OpsTaskFundInfo> getTaskFundListByLeader(String date, List<String> deptIds);

    List<OpsTaskFundInfo> getFundByDate(String date, String taskReplicaId);


    List<OpsTaskFundInfo> queryMergeForDashBoard(String date, List<String> ids);

    List<OpsTaskFundInfo> getTaskFundListForMonitor(String date);
    List<OpsTaskFundInfo> getTaskFundListForMonitorWarn(String date);

    List<OpsTaskGenInfo> getOpsTaskInfoListByLeader(String orgId, String genTime);
}
