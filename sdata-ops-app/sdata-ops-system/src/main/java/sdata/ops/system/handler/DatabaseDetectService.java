package sdata.ops.system.handler;

import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sdata.ops.common.core.util.DatabaseType;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

@Service
@RequiredArgsConstructor
public class DatabaseDetectService {

    private final SqlSessionFactory sqlSessionFactory;

    public DatabaseType detectDatabaseType() {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Connection connection = session.getConnection();
            DatabaseMetaData metaData = connection.getMetaData();
            return DatabaseType.fromProductName(metaData.getDatabaseProductName());
        } catch (SQLException e) {
            throw new RuntimeException("Failed to detect database type", e);
        }
    }
}
