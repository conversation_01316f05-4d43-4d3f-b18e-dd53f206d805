package sdata.ops.system.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.OrgListDTO;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.MapperVO;
import sdata.ops.base.system.model.vo.SystemUserVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.OpsSysOrgService;
import sdata.ops.system.service.SystemUserService;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/userController")
@RequiredArgsConstructor
public class SystemUserController {

    private final SystemUserService systemUserService;

    private final OpsSysOrgService opsSysOrgService;


    @ControllerAuditLog(value = "根据用户ID获取用户信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getUserById")
    public R<SystemUser> getUserById(@RequestParam("id") String id) {
        return R.data(systemUserService.getById(id));
    }

    @ControllerAuditLog(value = "获取当前用户信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getUserInfo")
    public R<SystemUserVO> getUserInfo() {
        //获取当前线程用户
        String userId = SecureUtil.currentUserId();
        SystemUser user = systemUserService.getById(userId);
        user.setPassword(null);
        //岗位和角色信息id与name的映射
        Map<String, String> mapper = systemUserService.findRoleAndOrgNameMapper();
        SystemUserVO vo = new SystemUserVO().convertVoFill(user, mapper);
        OrgListDTO orgList = opsSysOrgService.allOrgIdByUser(user.getId());
        //查询是否是领导
        List<OpsSysOrg> leaderLs = opsSysOrgService.list(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getLeader, userId)
                .ne(OpsSysOrg::getOrgType, "3").eq(OpsSysOrg::getDeleted, 0));
        if(!leaderLs.isEmpty()){
            vo.setLeader(1);
        }
        //查询用户所有岗位信息
        if (Objects.nonNull(orgList) && !orgList.getOrgIds().isEmpty()) {
            Map<String,Integer> orgSort=opsSysOrgService.getSortMap();
            orgList.getOrgIds().sort(Comparator.comparing(orgSort::get));
            for (String orgId : orgList.getOrgIds()) {
                vo.getOrgInfos().add(new MapperVO().setId(orgId).setName(mapper.get(orgId)));
            }
        }
        return R.data(vo);
    }

    @ControllerAuditLog(value = "获取用户列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list")
    public R<List<SystemUserVO>> listUser(@RequestParam(value = "username", required = false) String username,
                                          @RequestParam(value = "name", required = false) String name
    ) {
        return R.data(systemUserService.list(Wrappers.lambdaQuery(SystemUser.class).
                        like(StringUtils.hasText(name), SystemUser::getName, name).like(StringUtils.hasText(username), SystemUser::getUsername, username))
                .stream().map(i -> new SystemUserVO().convertVo(i)).collect(Collectors.toList()));
    }


    @ControllerAuditLog(value = "分页获取用户列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/listByPage")
    public R<IPage<SystemUserVO>> listUserByPage(@RequestParam(value = "username", required = false) String username,
                                                 @RequestParam(value = "name", required = false) String name,
                                                 @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                 @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                 @RequestParam(required = false, defaultValue = "1") int page,
                                                 @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        Page<SystemUser> pageEntity = new Page<>(page, pageSize);
        Map<String, String> mapper = systemUserService.findRoleAndOrgNameMapper();
        IPage<SystemUserVO> iPage = systemUserService.page(pageEntity, Wrappers.lambdaQuery(SystemUser.class).
                        like(StringUtils.hasText(name), SystemUser::getName, name).
                        like(StringUtils.hasText(username), SystemUser::getUsername, username)
                        .ge(Objects.nonNull(startTime), SystemUser::getCreateTime, startTime).
                        le(Objects.nonNull(endTime), SystemUser::getCreateTime, endTime))
                .convert(i -> new SystemUserVO().convertVoFill(i, mapper));
        return R.data(iPage);
    }

    @ControllerAuditLog(value = "新增用户", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    @PostMapping("/add")
    public R<String> addUser(@RequestBody SystemUserVO userVO) {
        try {
            SystemUser exist = systemUserService.getOne(Wrappers.lambdaQuery(SystemUser.class).eq(SystemUser::getUsername, userVO.getUsername()));
            if (Objects.nonNull(exist)) {
                return R.fail("用户已存在");
            }
        } catch (Exception e) {
            return R.fail("用户已存在");
        }
        systemUserService.addUser(userVO);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "编辑用户", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PostMapping("/edit")
    public R<String> edit(@RequestBody SystemUserVO userVO) {
        systemUserService.addUser(userVO);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "删除用户", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/delete")
    public R<String> delete(@RequestParam("id") String id) {
        systemUserService.removeById(id);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 操作用户，冻结 和解冻
     *
     * @param id     用户id
     * @param status 状态值 lock unlock
     * @return msg
     */
    @ControllerAuditLog(value = "操作用户(冻结/解冻)", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/operator")
    public R<String> operatorUser(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        systemUserService.updateStatus(id, status);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }


    /**
     * 用户授权角色
     */

    @ControllerAuditLog(value = "用户授权角色", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PutMapping("/authRole")
    public R<String> insertAuthRole(Long userId, Long[] roleIds) {
        systemUserService.insertUserAuth(userId, roleIds);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    @ControllerAuditLog(value = "同步用户表信息", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/syncUser")
    public R<String> syncUser() {
        //todo 同步用户表信息 ，两种配置 1 是api读取结果覆盖更新 2 是sql读取结果覆盖更新
        return R.success(MessageConstant.ADD_SUCCESS);
    }


    @ControllerAuditLog(value = "获取用户ID与名称映射", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/idNameMapper")
    public Map<String, String> idNameMapper() {
        return systemUserService.findNameIdMapping();
    }
}
