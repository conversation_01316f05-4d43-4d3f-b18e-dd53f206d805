package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.common.core.mapper.AuditLogMapper;
import sdata.ops.common.core.model.AuditLog;
import sdata.ops.system.service.AuditLogService;

import java.util.Date;

@Service
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLog> implements AuditLogService {
    @Override
    public void clear(Date opStartTime, Date opEndTime) {
        if (opStartTime == null || opEndTime == null) {
            log.warn("开始时间或结束时间为空");
            return;
        }
        lambdaUpdate()
                .ge(AuditLog::getOperatorTime, opStartTime)
                .le(AuditLog::getOperatorTime, opEndTime)
                .remove();
    }
}
