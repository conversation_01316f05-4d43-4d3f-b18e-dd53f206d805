package sdata.ops.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.base.system.model.entity.OpsTaskTemplate;
import sdata.ops.base.system.model.entity.OpsTaskTemplateRelation;
import sdata.ops.base.system.model.vo.TemplateVO;
import sdata.ops.system.mapper.OpsTaskTemplateMapper;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;
import sdata.ops.system.service.OpsTaskTemplateRelationService;
import sdata.ops.system.service.OpsTaskTemplateService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_template(菜单权限表)】的数据库操作Service实现
 * @createDate 2024-07-09 15:23:13
 */
@Service
@RequiredArgsConstructor
public class OpsTaskTemplateServiceImpl extends ServiceImpl<OpsTaskTemplateMapper, OpsTaskTemplate>
        implements OpsTaskTemplateService {

    private final OpsTaskAttrBasicReplicaService replicaService;

    private final OpsTaskTemplateRelationService relationService;

    @Override
    public IPage<OpsTaskTemplate> pageByDynamic(String templateName, String templateStatus, String templateType, Page<OpsTaskTemplate> pageModel) {


        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateAll(TemplateVO vo) {
        //获取模板本身对象，如果是新增一般只要一个模板名称，其他都是空余
        OpsTaskTemplate template = vo.getTemplate();
        if (template.getTemplateStatus() == null) {
            template.setTemplateStatus(0);
        }
        saveOrUpdate(template);
        //获取模板本身包含的任务对象,一般是树状列表
        List<OpsTaskAttrBasicReplica> list = vo.getList();
        //模板任务不为空
        if (!list.isEmpty()) {
            //删除replica表的副本数据
            List<String> replicaIds = relationService.findIds(template.getId());
            if (!replicaIds.isEmpty()) {
                LambdaQueryWrapper<OpsTaskAttrBasicReplica> deleteWrs = new LambdaQueryWrapper<>();
                deleteWrs.in(OpsTaskAttrBasicReplica::getId, replicaIds);
                replicaService.remove(deleteWrs);
            }
            //先删除关联关系表模板与复制任务单元关系表
            LambdaQueryWrapper<OpsTaskTemplateRelation> deleteWr = new LambdaQueryWrapper<>();
            deleteWr.eq(OpsTaskTemplateRelation::getTemplateId, template.getId());
            relationService.remove(deleteWr);
            //再保存模板与replica 任务id关系
            List<OpsTaskTemplateRelation> relations = new ArrayList<>();
            List<OpsTaskAttrBasicReplica> flatten = new ArrayList<>();
            flattenHandler(list, flatten, relations, template.getId());
            relationService.saveBatch(relations);
            for (OpsTaskAttrBasicReplica replica : flatten) {
                replicaService.saveOrUpdate(replica);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(String id) {
        //复制模板本身
        OpsTaskTemplate templateSource = getById(id);
        OpsTaskTemplate templateTarget = new OpsTaskTemplate();
        BeanUtil.copyProperties(templateSource, templateTarget);
        templateTarget.setId(null);
        templateTarget.setCreateBy(null);
        templateTarget.setCreateTime(null);
        templateTarget.setUpdateBy(null);
        templateTarget.setUpdateTime(null);
        templateTarget.setTemplateName(templateTarget.getTemplateName() + "_复制");
        save(templateTarget);
        //复制数组
        List<OpsTaskAttrBasicReplica> self = replicaService.viewList(id);
        Map<String, String> idMapper = new HashMap<>();
        List<OpsTaskTemplateRelation> relations = new ArrayList<>();
        //先对当前父id做一次映射
        for (OpsTaskAttrBasicReplica basicReplica : self) {
            Long newId = IdWorker.getId();
            idMapper.put(String.valueOf(basicReplica.getId()), newId + "");
            basicReplica.setId(String.valueOf(newId));
        }
        //替换
        for (OpsTaskAttrBasicReplica replica : self) {
            if (!replica.getParentId().equals("0")) {
                replica.setParentId(idMapper.get(replica.getParentId()));
            }
            OpsTaskTemplateRelation relationI = new OpsTaskTemplateRelation();
            relationI.setTemplateId(templateTarget.getId())
                    .setTaskId(replica.getTaskRefId())
                    .setTaskReplicaId(String.valueOf(replica.getId()));
            relations.add(relationI);
        }
        replicaService.saveBatch(self);
        relationService.saveBatch(relations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void templateImport(List<OpsTaskAttrBasicReplica> cap, String templateName, String schedulerType, String orgId, String ownerId, String ownerType, String ownerVal) {
        //树状替换
        Long templateId = IdWorker.getId();
        Map<String, String> mapper = new HashMap<>();
        for (OpsTaskAttrBasicReplica replica : cap) {
            replica.setOwnerOrgId(orgId);
            if (replica.getTaskCheckReq() != null && replica.getTaskCheckReq().equals("1")
                    && replica.getTaskCheckType() != null && replica.getTaskCheckType().equals("2")) {
                replica.setCheckOrgId(orgId);
            }
            replica.setTaskBindTemplateId(String.valueOf(templateId));
            if (!replica.getTaskNo().contains("-")) {
                Long id = IdWorker.getId();
                replica.setId(String.valueOf(id));
                mapper.put(replica.getTaskNo(), String.valueOf(id));
                replica.setParentId("0");
            } else {
                Long id = IdWorker.getId();
                replica.setId(String.valueOf(id));
                mapper.put(replica.getTaskNo(), String.valueOf(id));
            }
        }
        List<OpsTaskTemplateRelation> relations = new ArrayList<>();
        for (OpsTaskAttrBasicReplica item : cap) {
            if (item.getParentId() == null || !item.getParentId().equals("0")) {
                System.out.println(item.getTaskNo());
                String key = item.getTaskNo().substring(0, item.getTaskNo().lastIndexOf("-"));
                item.setParentId(mapper.get(key));
            }
            //追加用户信息 //追加默认参数信息
            fillDefVal(item);
            OpsTaskTemplateRelation relationItem = new OpsTaskTemplateRelation();
            relationItem.setTaskReplicaId(String.valueOf(item.getId()));
            relationItem.setTemplateId(String.valueOf(templateId));
            relationItem.setTaskId("-1");
            relations.add(relationItem);
        }
        //入库
        //生成模板
        OpsTaskTemplate template = new OpsTaskTemplate();
        template.setSchedulerType(schedulerType);
        template.setTemplateName(templateName);
        template.setId(String.valueOf(templateId));
        template.setOrgId(orgId);
        template.setTemplateStatus(0);
        save(template);
        //生成模板与replica关系
        relationService.saveBatch(relations);
        //生成replica 实体类
        replicaService.saveBatch(cap);
    }

    private void fillDefVal(OpsTaskAttrBasicReplica item) {


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplate(String id) {
        //删除模板,删除关系,删除replica
        removeById(id);
        //先删除任务单元副本
        baseMapper.removeReplicaRelations(id);
        //在删除关系表
        baseMapper.removeRelations(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplateForTransfer(String id, List<OpsTaskAttrBasicReplica> replicas) {

        //先删除任务单元副本
        baseMapper.removeReplicaRelations(id);
        //在删除关系表
        baseMapper.removeRelations(id);
        //
        List<OpsTaskTemplateRelation> relations = new ArrayList<>();
        for (OpsTaskAttrBasicReplica replica : replicas) {
            OpsTaskTemplateRelation relationItem = new OpsTaskTemplateRelation();
            relationItem.setTaskReplicaId(String.valueOf(replica.getId()));
            relationItem.setTemplateId(String.valueOf(id));
            relationItem.setTaskId("-1");
            relations.add(relationItem);
        }
        //生成模板与replica关系
        relationService.saveBatch(relations);
        //生成replica 实体类
        replicaService.saveBatch(replicas);

    }


    @Override
    public void flattenHandler(List<OpsTaskAttrBasicReplica> list,
                               List<OpsTaskAttrBasicReplica> all,
                               List<OpsTaskTemplateRelation> relations, String id) {
        for (OpsTaskAttrBasicReplica item : list) {
            item.setTaskBindTemplateId(id);
            if (!item.getChild().isEmpty()) {
                all.add(item);
                relations.add(new OpsTaskTemplateRelation().setTaskId(item.getTaskRefId()).setTemplateId(id)
                        .setTaskReplicaId(String.valueOf(item.getId())));
                flattenHandler(item.getChild(), all, relations, id);
            } else {
                relations.add(new OpsTaskTemplateRelation().setTaskId(item.getTaskRefId()).setTemplateId(id)
                        .setTaskReplicaId(String.valueOf(item.getId())));
                all.add(item);
            }
        }
    }

    @Override
    public List<String> listReplicaIds(Long taskId) {
        return baseMapper.listReplicaIds(taskId);
    }

}




