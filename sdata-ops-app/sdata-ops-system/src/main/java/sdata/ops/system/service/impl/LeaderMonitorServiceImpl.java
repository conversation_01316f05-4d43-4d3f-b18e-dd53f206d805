package sdata.ops.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.LeaderInfoVO;
import sdata.ops.system.service.*;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class LeaderMonitorServiceImpl implements LeaderMonitorService {

    private final OpsTaskGenInfoService opsTaskGenInfoService;

    private final OpsTaskFundInfoService fundInfoService;

    private final OpsSysOrgService opsSysOrgService;

    private final OpsTaskTemplateService taskTemplateService;

    private final SystemUserService userService;

    private final OpsTaskAttrBasicReplicaService taskAttrBasicReplicaService;

    /**
     * 超时任务分组
     *
     * @param date 日期 yyyyMMdd
     * @return list
     */
    @Override
    public Object warnList(String date) {
        //先查询所有超时任务
        List<OpsTaskGenInfo> res = opsTaskGenInfoService.list(warnWrapper(date));
        //排除自动延时的
        res = res.stream().filter(this::defferCondition).collect(Collectors.toList());
        //todo 查询所有明细任务追加
        addDetailTypeTaskForWarn(res, date);
        //分组-计算 -岗位 -模板
        //先根据owner-org-id进行部门分组
        //再根据owner -org -type类型进行分岗位或者人员
        //查询部门- 岗位- 人员  info进行补充
        Map<String, List<OpsTaskGenInfo>> orgMapping = res.stream().filter(i -> i.getOwnerOrgId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));
        //获取部门与部门下的所有岗位
        //统计计数组织数据结构操作
        return orgConstruct(orgMapping);
    }

    @Override
    public Object warnListV1(String date) {
        Instant t1 = Instant.now();
        //所有超时任务-根+叶
        List<OpsTaskGenInfo> allList = opsTaskGenInfoService.list(warnWrapperAll(date));
        Map<String, String> idToNameMap = userService.findNameIdMapping();
        Instant t2 = Instant.now();
        log.info("warnListV1_allList+idToNameMap:{} 毫秒", Duration.between(t1, t2).toMillis());
        //权限范围内所有的叶节点对应的父节点数据
        List<OpsTaskGenInfo> pList = Lists.newArrayList();
        //过滤出所有的叶节点数据
        List<OpsTaskGenInfo> sList = allList.stream()
                .filter(i -> (!StringUtils.hasText(i.getTaskChildIds()))).collect(Collectors.toList());
        //叶节点允许延时数据剔除
        List<OpsTaskGenInfo> res = sList.stream().filter(this::defferCondition).collect(Collectors.toList());
        List<String> sIds = res.stream().map(i -> String.valueOf(i.getId())).collect(Collectors.toList());
        allList.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds()))
                .collect(Collectors.toMap(
                        i -> Arrays.stream(i.getTaskChildIds().split(",")).collect(Collectors.toSet()),
                        i -> i, (n1, n2) -> n1))
                .forEach((set, value) -> {
                    if (sIds.stream().anyMatch(set::contains)) {
                        pList.add(value);
                    }
                });
        addDetailTypeTaskForWarnV1(res, date, pList);
        Instant t3 = Instant.now();
        log.info("warnListV1_追加明细数据:{} 毫秒", Duration.between(t2, t3).toMillis());
        if (CollUtil.isNotEmpty(idToNameMap)) {
            res.forEach(gen -> {
                if (StringUtils.hasText(gen.getOperationCompleteId())
                        && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                    String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                    if (StringUtils.hasText(tmpOperationName)) {
                        gen.setOperationCompleteId(tmpOperationName);
                    }
                } else {
                    gen.setOperationCompleteId(gen.getTaskOwnerVal());
                }
                if (StringUtils.hasText(gen.getOperationCheckId())
                        && idToNameMap.containsKey(gen.getOperationCheckId())) {
                    String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                    if (StringUtils.hasText(tmpCheckName)) {
                        gen.setOperationCheckId(tmpCheckName);
                    }
                } else {
                    gen.setOperationCheckId(gen.getTaskCheckVal());
                }
            });
            pList.forEach(gen -> {
                if (StringUtils.hasText(gen.getOperationCompleteId())
                        && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                    String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                    if (StringUtils.hasText(tmpOperationName)) {
                        gen.setOperationCompleteId(tmpOperationName);
                    }
                } else {
                    gen.setOperationCompleteId(gen.getTaskOwnerVal());
                }
                if (StringUtils.hasText(gen.getOperationCheckId())
                        && idToNameMap.containsKey(gen.getOperationCheckId())) {
                    String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                    if (StringUtils.hasText(tmpCheckName)) {
                        gen.setOperationCheckId(tmpCheckName);
                    }
                } else {
                    gen.setOperationCheckId(gen.getTaskCheckVal());
                }
            });
        }
        Instant t4 = Instant.now();
        log.info("warnListV1_经办人和复核人转化值:{} 毫秒", Duration.between(t3, t4).toMillis());
        Map<String, List<OpsTaskGenInfo>> orgMapping = res.stream().filter(i -> i.getOwnerOrgId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));
        List<LeaderInfoVO> rl = orgConstruct1(orgMapping, pList);
        Instant t5 = Instant.now();
        log.info("warnListV1_orgConstruct1:{} 毫秒", Duration.between(t4, t5).toMillis());
        log.info("warnListV1_方法执行总时间:{} 毫秒", Duration.between(t1, t5).toMillis());
        return rl;
    }

    /**
     * 数据结构转化
     *
     * @param orgMapping 叶节点map
     * @param pList      根节点列表
     * @return
     */
    private List<LeaderInfoVO> orgConstruct1(Map<String, List<OpsTaskGenInfo>> orgMapping, List<OpsTaskGenInfo> pList) {
        Instant m1 = Instant.now();
        List<OpsSysOrg> orgInfo = opsSysOrgService.deptConPostInfos();
        Instant m2 = Instant.now();
        log.info("orgConstruct1_orgInfo:{} 毫秒", Duration.between(m1, m2).toMillis());
        Map<String, OpsTaskTemplate> mp = taskTemplateService.list().stream().collect(Collectors.toMap(OpsTaskTemplate::getId, i -> i, (n2, n1) -> n1));
        Instant m3 = Instant.now();
        log.info("orgConstruct1_taskTemplate:{} 毫秒", Duration.between(m2, m3).toMillis());
        List<LeaderInfoVO> res = new ArrayList<>();
        for (OpsSysOrg org : orgInfo) {
            //部门层级的数据组织
            LeaderInfoVO deptVO = new LeaderInfoVO();
            deptVO.setName(org.getOrgName()).setId(String.valueOf(org.getId())).setType(1);
            //部门岗位为空则删除
            if (org.getIdMap().isEmpty()) {
                continue;
            }
            //部门下岗位进行数据分组与计算
            org.getIdMap().forEach((id, en) -> {
                LeaderInfoVO postVO = new LeaderInfoVO();
                postVO.setType(2).setName(en.getOrgName()).setId(id);
                if (orgMapping.containsKey(id)) {
                    deptVO.getChild().add(postVO);
                    List<OpsTaskGenInfo> postArr = orgMapping.get(id);
                    postVO.setTotal((long) postArr.size());
                    //先按岗位下的模板进行分组
                    Map<String, List<OpsTaskGenInfo>> postGroup = postArr.stream().collect(Collectors.groupingBy(task -> Optional.ofNullable(task.getTaskBindTemplateId()).orElse("-100")));
                    postGroup.forEach((tempId, ens) -> {
                        LeaderInfoVO tempVO = new LeaderInfoVO();
                        postVO.getChild().add(tempVO);
                        tempVO.setId(tempId).setType(3).setName(mp.get(tempId) == null ? "其他" : mp.get(tempId).getTemplateName());
                        Map<String, List<OpsTaskGenInfo>> tempGroup = ens.stream().filter(i -> StringUtils.hasText(i.getTaskOwnerId())).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        Instant g1 = Instant.now();
                        tempGroup.forEach((ownerId, values) -> {
                                    String name = values.get(0).getTaskOwnerVal();
                                    LeaderInfoVO dvo = new LeaderInfoVO();
                                    tempVO.getChild().add(dvo);

                                    List<OpsTaskGenInfo> sList_p = Lists.newArrayList();
                                    List<String> sIds = values.stream().map(i -> String.valueOf(i.getId())).distinct().collect(Collectors.toList());
                                    for (OpsTaskGenInfo p_info : pList) {
                                        if (StringUtils.hasText(p_info.getTaskChildIds())) {
                                            List<String> childIds = Arrays.stream(p_info.getTaskChildIds().split(",")).distinct().collect(Collectors.toList());
                                            for (String childId : childIds) {
                                                if (sIds.contains(childId)) {
                                                    sList_p.add(p_info);
                                                    break;
                                                }
                                            }
                                        } /*else if (p_info.getImportStatus() == 1) {
                                            sList_p.add(p_info);
                                        }*/
                                    }

                                    dvo.setId(ownerId).setName(name).setType(4)
                                            //.setDetail(values)
                                            .setDetail(buildTreeByChildGenInfo(sList_p, values))
                                            .setTotal((long) values.size());

                                }
                        );
                        Instant g2 = Instant.now();
                        log.info("orgConstruct1_for_tempGroup_处理:{} 毫秒", Duration.between(g1, g2).toMillis());
                    });
                }
            });
            res.add(deptVO);
        }
        Instant m4 = Instant.now();
        log.info("orgConstruct1_for_orgInfo_处理:{} 毫秒", Duration.between(m3, m4).toMillis());
        return res;
    }

    /**
     * 查询所有非完成任务列表wrapper
     *
     * @param dateTime 业务日期
     * @return
     */
    private Wrapper<OpsTaskGenInfo> warnWrapperAll(String dateTime) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                OpsTaskGenInfo::getId, OpsTaskGenInfo::getParentId, OpsTaskGenInfo::getTaskChildIds,
                OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskEndTime, OpsTaskGenInfo::getOwnerOrgId,
                OpsTaskGenInfo::getTaskOwnerId, OpsTaskGenInfo::getTaskOwnerVal, OpsTaskGenInfo::getTaskOwnerType,
                OpsTaskGenInfo::getTaskBindTemplateId);
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, 3).ne(OpsTaskGenInfo::getTaskCompleteStatus, 5);
        queryWrapper.lt(OpsTaskGenInfo::getTaskEndTime, StringUtils.hasText(dateTime) ? DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.parse(dateTime, "yyyy-MM-dd"), -1)) : new Date());
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        return queryWrapper;
    }

    /**
     * 组装tree结构
     *
     * @param list
     * @return
     */
    public List<OpsTaskGenInfo> buildTree(List<OpsTaskGenInfo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, OpsTaskGenInfo> genMap = list.stream().collect(Collectors.toMap(OpsTaskGenInfo::getId, i -> i));
        List<OpsTaskGenInfo> rootNodes = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (null == genInfo.getParentId() || genInfo.getParentId() == 0) {
                rootNodes.add(genInfo);
            } else {
                OpsTaskGenInfo parent = genMap.get(genInfo.getParentId());
                if (null != parent) {
                    if (null == parent.getChildren()) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(genInfo);
                } else {
                    rootNodes.add(genInfo);
                }
            }
        }
        sortTreeNodes(rootNodes);
        return rootNodes;
    }

    /**
     * 深拷贝
     *
     * @param src
     * @param <T>
     * @return
     */
    public static <T> List<T> deepCopy(List<T> src) {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            return (List<T>) in.readObject();
        } catch (IOException e) {
            e.printStackTrace();
            return new ArrayList<>();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 树结构数据排序
     *
     * @param rootNodes
     */
    public static void sortTreeNodes(List<OpsTaskGenInfo> rootNodes) {
        rootNodes.forEach(node -> {
            if (null != node.getChildren() && CollUtil.isNotEmpty(node.getChildren())) {
                node.getChildren()
                        .sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
                sortTreeNodes(node.getChildren());
            }
        });
        rootNodes.sort(Comparator.comparingLong(OpsTaskGenInfo::getId));
    }

    @Override
    public Object dailyList(String date) {
        //先查询所有日常任务
        List<OpsTaskGenInfo> dailyTasks = opsTaskGenInfoService.allTaskForNowDay(date);
        //todo 查询所有明细任务追加
        addDetailTypeTask(dailyTasks, date);
        //分组测试
        Map<String, List<OpsTaskGenInfo>> orgMapping = dailyTasks.stream().filter(i -> i.getOwnerOrgId() != null && i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 5).collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));
        //结构化
        return orgConstructDaily(orgMapping);
    }

    @Override
    public Object dailyListV1(String date) {
        Instant t1 = Instant.now();
        List<OpsTaskGenInfo> allList = opsTaskGenInfoService.allTaskForNowDayV1(date);
        Map<String, String> idToNameMap = userService.findNameIdMapping();
        Instant t2 = Instant.now();
        log.info("dailyListV1_日常任务+idToNameMap:{} 毫秒", Duration.between(t1, t2).toMillis());
        List<OpsTaskGenInfo> pList = allList.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList());
        List<OpsTaskGenInfo> dailyTasks = allList.stream().filter(i -> !StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList());
        addDetailTypeTaskV1(dailyTasks, date, pList);
        Instant t3 = Instant.now();
        log.info("dailyListV1_追加明细数据:{} 毫秒", Duration.between(t2, t3).toMillis());

        Instant t4 = Instant.now();
        //log.info("dailyListV1_替换实际经办人和复核人:{} 毫秒", Duration.between(t3, t4).toMillis());
        //分组测试
        Map<String, List<OpsTaskGenInfo>> orgMapping = dailyTasks.stream().filter(i -> i.getOwnerOrgId() != null && i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 5).collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));
        Instant t5 = Instant.now();
        log.info("dailyListV1_orgMapping:{} 毫秒", Duration.between(t4, t5).toMillis());
        //结构化
        List<LeaderInfoVO> rl = orgConstructDailyV1(orgMapping, pList, idToNameMap);
        Instant t6 = Instant.now();
        log.info("dailyListV1_orgConstructDailyV1:{} 毫秒", Duration.between(t5, t6).toMillis());
        traverseLeaderTree(rl, idToNameMap);
        Instant t7 = Instant.now();
        log.info("dailyListV1_traverseLeaderTree:{} 毫秒", Duration.between(t6, t7).toMillis());
        log.info("dailyListV1_方法执行总时间:{} 毫秒", Duration.between(t1, t7).toMillis());
        return rl;
    }

    /**
     * 遍历树接口数据，替换实际经办人和复核人
     *
     * @param list
     * @param idToNameMap
     */
    public static void traverseGenTree(List<OpsTaskGenInfo> list, Map<String, String> idToNameMap) {
        for (OpsTaskGenInfo gen : list) {
            if (StringUtils.hasText(gen.getOperationCompleteId())
                    && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                if (StringUtils.hasText(tmpOperationName)) {
                    gen.setOperationCompleteId(tmpOperationName);
                }
            } else {
                gen.setOperationCompleteId(gen.getTaskOwnerVal());
            }
            if (StringUtils.hasText(gen.getOperationCheckId())
                    && idToNameMap.containsKey(gen.getOperationCheckId())) {
                String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                if (StringUtils.hasText(tmpCheckName)) {
                    gen.setOperationCheckId(tmpCheckName);
                }
            } else {
                gen.setOperationCheckId(gen.getTaskCheckVal());
            }
            if (CollUtil.isNotEmpty(gen.getChildren())) {
                traverseGenTree(gen.getChildren(), idToNameMap);
            }
        }
    }

    /**
     * 遍历返回结构体数据
     *
     * @param list
     */
    public static void traverseLeaderTree(List<LeaderInfoVO> list, Map<String, String> idToNameMap) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (LeaderInfoVO vo : list) {
            if (CollUtil.isNotEmpty(vo.getChild()) && CollUtil.isEmpty(vo.getDetailComplete()) && CollUtil.isEmpty(vo.getDetailUnComplete())) {
                traverseLeaderTree(vo.getChild(), idToNameMap);
            } else {

                if (CollUtil.isEmpty(vo.getChild()) && (CollUtil.isNotEmpty(vo.getDetailComplete()) || CollUtil.isNotEmpty(vo.getDetailUnComplete()))) {
                    if (CollUtil.isNotEmpty(vo.getDetailComplete())) {
                        traverseGenTree(vo.getDetailComplete(), idToNameMap);
                    }
                    if (CollUtil.isNotEmpty(vo.getDetailUnComplete())) {
                        traverseGenTree(vo.getDetailUnComplete(), idToNameMap);
                    }
                }
            }
        }
        for (LeaderInfoVO vo : list) {
            if (CollUtil.isNotEmpty(vo.getChild()) && CollUtil.isEmpty(vo.getDetailComplete()) && CollUtil.isEmpty(vo.getDetailUnComplete())) {
                traverseLeaderTree(vo.getChild(), idToNameMap);
            } else {
                if (CollUtil.isEmpty(vo.getChild())
                        && !StringUtils.hasText(vo.getName())
                        && (CollUtil.isNotEmpty(vo.getDetailComplete()) || CollUtil.isNotEmpty(vo.getDetailUnComplete()))) {
                    vo.getDetailComplete().addAll(vo.getDetailUnComplete());
                    List<String> completes = vo.getDetailComplete().stream().filter(i -> i.getOperationDisplayType() == 1).map(OpsTaskGenInfo::getOperationCompleteId).collect(Collectors.toList());
                    List<String> checks = vo.getDetailComplete().stream().filter(i -> i.getOperationDisplayType() == 2).map(OpsTaskGenInfo::getOperationCheckId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(completes)) {
                        vo.setName(completes.get(0));
                    } else if (CollUtil.isNotEmpty(checks)) {
                        vo.setName(checks.get(0));
                    }
                }
            }
        }
    }

    /**
     * 将叶节点列表增加上对应的根节点信息
     *
     * @param allList 所有的根节点列表
     * @param p_sList 叶节点列表
     * @return
     */
    private List<OpsTaskGenInfo> buildTreeByChildGenInfo(List<OpsTaskGenInfo> allList, List<OpsTaskGenInfo> p_sList) {
        Instant n1 = Instant.now();
        if (CollUtil.isNotEmpty(p_sList)) {
            List<OpsTaskGenInfo> sList = deepCopy(p_sList);
            List<String> sIds = sList.stream().map(i -> String.valueOf(i.getId())).distinct().collect(Collectors.toList());
            List<OpsTaskGenInfo> sList_p = Lists.newArrayList();
            allList.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds()))
                    .collect(Collectors.toMap(
                            i -> Arrays.stream(i.getTaskChildIds().split(",")).collect(Collectors.toSet()),
                            i -> i, (m, n) -> m))
                    .entrySet()
                    .forEach(entry -> {
                        Set<String> set = entry.getKey();
                        if (sIds.stream().anyMatch(set::contains)) {
                            sList_p.add(entry.getValue());
                        }
                    });
            sList.addAll(sList_p);
            //sList.addAll(allList.stream().filter(i -> ObjectUtil.isNotNull(i.getImportStatus()) && i.getImportStatus().equals(1)).distinct().collect(Collectors.toList()));
            Instant n2 = Instant.now();
            log.info("buildTreeByChildGenInfo_collectParents:{} 毫秒", Duration.between(n1, n2).toMillis());
            List<OpsTaskGenInfo> rl = buildTree(deepCopy(sList));
            Instant n3 = Instant.now();
            log.info("buildTreeByChildGenInfo_buildTree:{} 毫秒", Duration.between(n2, n3).toMillis());
            log.info("buildTreeByChildGenInfo_方法执行时间:{} 毫秒", Duration.between(n1, n3).toMillis());
            return rl;
        }
        Instant n4 = Instant.now();
        log.info("orgConstruct1_buildTreeByChildGenInfo:{} 毫秒", Duration.between(n1, n4).toMillis());
        return p_sList;
    }

    /**
     * 递归获取叶节点列表对应的所有的根列表信息
     *
     * @param s_info     叶节点对象
     * @param allList    所有根节点列表
     * @param parentList 叶节点对应的根节点列表
     */
    private void collectParents(OpsTaskGenInfo s_info, List<OpsTaskGenInfo> allList, List<OpsTaskGenInfo> parentList) {
        List<OpsTaskGenInfo> f_list = allList.stream().filter(i -> i.getId().equals(s_info.getParentId())).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(f_list)) {
            parentList.add(f_list.get(0));
            collectParents(f_list.get(0), allList, parentList);
        }
    }

    /**
     * 结构转化处理
     *
     * @param orgMapping 明细列表map
     * @param pList      根节点列表
     * @return
     */
    private List<LeaderInfoVO> orgConstructDailyV1(Map<String, List<OpsTaskGenInfo>> orgMapping,
                                                   List<OpsTaskGenInfo> pList,
                                                   Map<String, String> idToNameMap) {
        List<OpsSysOrg> orgInfo = opsSysOrgService.deptConPostInfos();
        Map<String, OpsTaskTemplate> mp = taskTemplateService.list().stream().collect(Collectors.toMap(OpsTaskTemplate::getId, i -> i, (n2, n1) -> n1));
        List<LeaderInfoVO> res = new ArrayList<>();
        for (OpsSysOrg org : orgInfo) {
            //部门层级的数据组织
            LeaderInfoVO deptVO = new LeaderInfoVO();
            deptVO.setName(org.getOrgName()).setId(String.valueOf(org.getId())).setType(1);
            //部门岗位为空则删除
            if (org.getIdMap().isEmpty()) {
                continue;
            }
            //部门下岗位进行数据分组与计算
            org.getIdMap().forEach((id, en) -> {
                LeaderInfoVO postVO = new LeaderInfoVO();
                postVO.setType(2).setName(en.getOrgName()).setId(id);
                if (orgMapping.containsKey(id)) {
                    deptVO.getChild().add(postVO);
                    List<OpsTaskGenInfo> postArr = orgMapping.get(id);
                    //岗位数据统计
                    //总数 + 经办+ 复核
                    postVO.setTotal((long) postArr.size() + postArr.stream().filter(i -> i.getTaskCheckReq() != null && i.getTaskCheckReq().equals("1")).count());
                    //完成 + 经办+复核 总数
                    postVO.setOver(postArr.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count()
                            + postArr.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3) && ((i.getTaskCheckReq() != null && i.getTaskCheckReq().equals("1")))).count());
                    postVO.setRate((NumberUtil.div(postVO.getOver(), postVO.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP)) + "%");
                    //先按岗位下的模板进行分组
                    Map<String, List<OpsTaskGenInfo>> postGroup = postArr.stream().collect(Collectors.groupingBy(task -> Optional.ofNullable(task.getTaskBindTemplateId()).orElse("-100")));
                    postGroup.forEach((tempId, ens) -> {
                        //模板下- 经办内容计算添加
                        LeaderInfoVO tempVO = new LeaderInfoVO();
                        postVO.getChild().add(tempVO);
                        tempVO.setId(tempId).setType(3).setName(mp.get(tempId) == null ? "其他" : mp.get(tempId).getTemplateName());
                        Map<String, LeaderInfoVO> owner = new HashMap<>();
                        Map<String, LeaderInfoVO> check = new HashMap<>();

                        //经办到人的
                        Map<String, List<OpsTaskGenInfo>> tempGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("2")).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        tempGroup.forEach((ownerId, values) -> {
                                    //经办数据统计(经办也分两种 2 是经办到人不用特殊处理 1 是经办到岗位的可以取 operation_complete_id 来处理合并 )
                                    String name = values.get(0).getTaskOwnerVal();
                                    LeaderInfoVO detailVO = new LeaderInfoVO();
                                    detailVO.setId(ownerId).setName(name).setType(4)
                                            //.setDetail(values)
                                            .setTotal((long) values.size()).
                                            setOver(values.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count());
                                    detailVO.setRate(NumberUtil.div(detailVO.getOver(), detailVO.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP) + "%");

                                    detailVO.setDetailComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                                    detailVO.setDetailUnComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                                    owner.put(ownerId, detailVO);
                                }
                        );
                        //经办到岗位的
                        Map<String, List<OpsTaskGenInfo>> tempPostGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("1") && i.getOperationCompleteId() == null).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        tempPostGroup.forEach((ownerId, post) -> {
                            //到岗的没有实际操作人
                            //经办数据统计(经办也分两种 2 是经办到人不用特殊处理 1 是经办到岗位的可以取 operation_complete_id 来处理合并 )
                            String name = post.get(0).getTaskOwnerVal();

                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, post, ownerId);

                            detailVO.setDetail(new ArrayList<>());
                            detailVO.setDetailComplete(buildTreeByChildGenInfo(pList, post.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                            detailVO.setDetailUnComplete(buildTreeByChildGenInfo(pList, post.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                            owner.put(ownerId, detailVO);

                        });
                        Map<String, List<OpsTaskGenInfo>> tempPersonGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("1") && i.getOperationCompleteId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOperationCompleteId));
                        tempPersonGroup.forEach((k, v) -> {
                            //到岗的但是有实际操作人的
                            //如果有对应的人员，则进行合并
                            if (owner.containsKey(k)) {
                                LeaderInfoVO alVo = owner.get(k);

                                alVo.getDetailComplete().addAll(buildTreeByChildGenInfo(pList, v.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                                alVo.getDetailUnComplete().addAll(buildTreeByChildGenInfo(pList, v.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                                recalculateVOV1(alVo, v);
                            } else {
                                LeaderInfoVO vo = new LeaderInfoVO();
                                fillDailyVO(vo, idToNameMap.get(k), v, k);

                                vo.setDetail(new ArrayList<>());
                                vo.setDetailComplete(buildTreeByChildGenInfo(pList, v.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                                vo.setDetailUnComplete(buildTreeByChildGenInfo(pList, v.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                                owner.put(k, vo);
                            }
                        });
                        tempVO.getChild().addAll(owner.values());
                        /**
                         * 复核处理 -----------------------------------------
                         */
                        //模板下复核类型是具体人员的进行统计处理
                        Map<String, List<OpsTaskGenInfo>> checkGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("2")).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskCheckId));
                        checkGroup.forEach((checkId, values) -> {
                            //复核数据统计(复核数据分两种 2 是复核到人的 按人来处理 1 复核到岗的取operation_check_id 来处理  )
                            //将复核数据的operationDisplayType值修改为2
                            values.forEach(v -> {
                                v.setOperationDisplayType(2);
                            });
                            String name = values.get(0).getTaskCheckVal();

                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, values, checkId);

                            detailVO.setDetail(new ArrayList<>());
                            detailVO.setDetailComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                            detailVO.setDetailUnComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                            check.put(checkId, detailVO);
                        });
                        //具体复核是岗位的处理
                        //模板下复核类型是具体人员的进行统计处理
                        Map<String, List<OpsTaskGenInfo>> checkPostGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("1") && i.getOperationCheckId() == null).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskCheckId));
                        checkPostGroup.forEach((checkId, post) -> {
                            //将复核数据的operationDisplayType值修改为2
                            post.forEach(v -> {
                                v.setOperationDisplayType(2);
                            });
                            //到岗的没有实际操作人
                            String name = post.get(0).getTaskOwnerVal();

                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, post, checkId);

                            detailVO.setDetail(new ArrayList<>());
                            detailVO.setDetailComplete(buildTreeByChildGenInfo(pList, post.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                            detailVO.setDetailUnComplete(buildTreeByChildGenInfo(pList, post.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                            check.put(checkId, detailVO);
                        });
                        //到岗位-有实际复核人的
                        Map<String, List<OpsTaskGenInfo>> checkPersonGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("1") && i.getOperationCheckId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOperationCheckId));
                        checkPersonGroup.forEach((checkId, values) -> {
                            //将复核数据的operationDisplayType值修改为2
                            values.forEach(v -> {
                                v.setOperationDisplayType(2);
                            });
                            //到岗的但是有实际操作人的
                            //如果有对应的人员，则进行合并
                            if (check.containsKey(checkId)) {
                                LeaderInfoVO alVo = check.get(checkId);

                                alVo.getDetailComplete().addAll(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                                alVo.getDetailUnComplete().addAll(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                                recalculateVOV1(alVo, values);
                            } else {
                                LeaderInfoVO vo = new LeaderInfoVO();
                                fillDailyVO(vo, idToNameMap.get(checkId), values, checkId);

                                vo.setDetail(new ArrayList<>());
                                vo.setDetailComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3)).collect(Collectors.toList())));
                                vo.setDetailUnComplete(buildTreeByChildGenInfo(pList, values.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() != 3)).collect(Collectors.toList())));

                                check.put(checkId, vo);
                            }
                        });
                        tempVO.getChild().addAll(check.values());
                    });
                }
            });
            res.add(deptVO);
        }
        return res;
    }

    /**
     * 产品明细任务
     *
     * @param dailyTasks 日常任务数据
     * @param date       日期
     */
    private void addDetailTypeTask(List<OpsTaskGenInfo> dailyTasks, String date) {
        //查询当前日期内的ops_fund_info 信息表，将 内容转为 ops_task_gen_info 即可
        List<OpsTaskFundInfo> infos = fundInfoService.getTaskFundListForMonitor(date)
                .stream().filter(i -> StringUtils.hasText(i.getStartDate()) && StringUtils.hasText(i.getEndDate())).collect(Collectors.toList());
        if (!infos.isEmpty()) {
            //不为空生成gen_info
            for (OpsTaskFundInfo fund : infos) {
                if (StringUtils.hasText(fund.getTaskReplicaId())) {
                    OpsTaskGenInfo item = new OpsTaskGenInfo();
                    fillDefGenInfo(item, fund);
                    dailyTasks.add(item);
                }
            }
        }
    }

    /**
     * 补全叶节点数据以及根节点数据
     *
     * @param dailyTasks 叶节点列表
     * @param date       业务日期
     * @param pList      根节点列表
     */
    private void addDetailTypeTaskV1(List<OpsTaskGenInfo> dailyTasks, String date, List<OpsTaskGenInfo> pList) {
        //查询当前日期内的ops_fund_info 信息表，将 内容转为 ops_task_gen_info 即可
        List<OpsTaskFundInfo> infos = fundInfoService.getTaskFundListForMonitor(date)
                .stream()
                .filter(i -> StringUtils.hasText(i.getStartDate()) && StringUtils.hasText(i.getEndDate()))
                .collect(Collectors.toList());
        if (!infos.isEmpty()) {
            //明细关联的任务id->根+叶任务列表->转化为gen
            List<String> replicaIds = infos.stream()
                    .filter(i -> StringUtils.hasText(i.getTaskReplicaId()))
                    .map(OpsTaskFundInfo::getTaskReplicaId).distinct().collect(Collectors.toList());
            List<OpsTaskGenInfo> basic2GenList = taskAttrBasicReplicaService.queryParentAndSelfListBySelfIds(replicaIds)
                    .stream()
                    .map(replica -> {
                        OpsTaskGenInfo gen = new OpsTaskGenInfo();
                        BeanUtil.copyProperties(replica, gen, CopyOptions.create().ignoreNullValue().ignoreError());
                        gen.setImportStatus(1);
                        return gen;
                    }).sorted(Comparator.comparing(OpsTaskGenInfo::getParentId)).collect(Collectors.toList());
            //明细->gen叶节点信息
            List<OpsTaskGenInfo> fund2GenList = infos.stream()
                    .filter(i -> (StringUtils.hasText(i.getTaskReplicaId()) && StringUtils.hasText(i.getTaskOwnerId())))
                    .map(fund -> {
                        OpsTaskGenInfo gen = new OpsTaskGenInfo();
                        fundInfo2GenInfo(fund, gen);
                        return gen;
                    }).collect(Collectors.toList());
            /*pList.addAll(basic2GenList);
            dailyTasks.addAll(fund2GenList);*/
            List<OpsTaskGenInfo> targetAllGens = Stream.concat(basic2GenList.stream(), fund2GenList.stream()).collect(Collectors.toList());
            OpsTaskGenInfoServiceImpl.getGroupedDescendantIds(targetAllGens);
            pList.addAll(targetAllGens.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList()));
            dailyTasks.addAll(targetAllGens.stream().filter(i -> !StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList()));
        }
    }

    /**
     * 明细数据转gen数据
     *
     * @param fund 明细
     * @param gen  任务
     */
    private void fundInfo2GenInfo(OpsTaskFundInfo fund, OpsTaskGenInfo gen) {
        gen.setTaskOwnerVal(fund.getTaskOwnerVal());
        gen.setTaskOwnerType(fund.getTaskOwnerType());
        gen.setTaskOwnerId(fund.getTaskOwnerId());
        gen.setOwnerOrgId(fund.getOwnerOrgId());
        gen.setId(Long.valueOf(fund.getId()));
        gen.setParentId(Long.valueOf(fund.getTaskReplicaId()));
        gen.setTaskName(fund.getFundName());
        gen.setTaskCompleteStatus(fund.getCompleteStatus().equals("1") ? 1 : 3);
        gen.setTaskBindTemplateId(fund.getTaskBindTemplateId());
        gen.setTaskEndTime(StringUtils.hasText(fund.getEndDate()) ? DateUtil.parse(fund.getEndDate() + " 18:00:00", "yyyy-MM-dd HH:mm:ss") : new Date());
    }

    /**
     * 产品明细任务
     *
     * @param dailyTasks 日常任务数据
     * @param date       日期
     */
    private void addDetailTypeTaskForWarn(List<OpsTaskGenInfo> dailyTasks, String date) {
        //查询当前日期内的ops_fund_info 信息表，将 内容转为 ops_task_gen_info 即可
        List<OpsTaskFundInfo> infos = fundInfoService.getTaskFundListForMonitorWarn(date)
                .stream().filter(i -> StringUtils.hasText(i.getStartDate()) && StringUtils.hasText(i.getEndDate())).collect(Collectors.toList());

        if (!infos.isEmpty()) {
            //不为空生成gen_info
            for (OpsTaskFundInfo fund : infos) {
                if (StringUtils.hasText(fund.getTaskReplicaId())) {
                    OpsTaskGenInfo item = new OpsTaskGenInfo();
                    fillDefGenInfo(item, fund);
                    dailyTasks.add(item);
                }
            }
        }
    }

    /**
     * 增加明细数据
     *
     * @param dailyTasks 叶节点列表
     * @param date       业务日期
     * @param pList      根节点列表
     */
    private void addDetailTypeTaskForWarnV1(List<OpsTaskGenInfo> dailyTasks, String date, List<OpsTaskGenInfo> pList) {
        List<OpsTaskFundInfo> infos = fundInfoService.getTaskFundListForMonitorWarn(date).stream()
                .filter(i -> StringUtils.hasText(i.getTaskOwnerId()) && StringUtils.hasText(i.getStartDate()) && StringUtils.hasText(i.getEndDate()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(infos)) {
            return;
        }
        //获取组合明细类型任务单元id数组
        List<String> taskReplicaIds = infos.stream()
                .map(OpsTaskFundInfo::getTaskReplicaId)
                .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskReplicaIds)) {
            List<OpsTaskGenInfo> basic2GenList = taskAttrBasicReplicaService.queryParentAndSelfListBySelfIds(taskReplicaIds)
                    .stream()
                    .map(replica -> {
                        OpsTaskGenInfo gen = new OpsTaskGenInfo();
                        BeanUtil.copyProperties(replica, gen, CopyOptions.create().ignoreNullValue().ignoreError());
                        gen.setImportStatus(1);
                        return gen;
                    }).sorted(Comparator.comparing(OpsTaskGenInfo::getParentId)).collect(Collectors.toList());
            List<OpsTaskGenInfo> fund2GenList = infos.stream()
                    .filter(i -> (StringUtils.hasText(i.getTaskReplicaId()) && StringUtils.hasText(i.getTaskOwnerId())))
                    .map(fund -> {
                        OpsTaskGenInfo gen = new OpsTaskGenInfo();
                        fundInfo2GenInfo(fund, gen);
                        return gen;
                    }).collect(Collectors.toList());
            /*pList.addAll(basic2GenList);
            dailyTasks.addAll(fund2GenList);*/
            List<OpsTaskGenInfo> targetAllGens = Stream.concat(basic2GenList.stream(), fund2GenList.stream()).collect(Collectors.toList());
            OpsTaskGenInfoServiceImpl.getGroupedDescendantIds(targetAllGens);
            pList.addAll(targetAllGens.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList()));
            dailyTasks.addAll(targetAllGens.stream().filter(i -> !StringUtils.hasText(i.getTaskChildIds())).collect(Collectors.toList()));
        }
    }

    private void fillDefGenInfo(OpsTaskGenInfo info, OpsTaskFundInfo fundInfo) {
        info.setTaskOwnerVal(fundInfo.getTaskOwnerVal());
        info.setTaskOwnerType(fundInfo.getTaskOwnerType());
        info.setTaskOwnerId(fundInfo.getTaskOwnerId());
        info.setOwnerOrgId(fundInfo.getOwnerOrgId());
        info.setId(Long.valueOf(fundInfo.getTaskReplicaId()));
        info.setTaskName(fundInfo.getFundName());
        info.setTaskCompleteStatus(fundInfo.getCompleteStatus().equals("1") ? 1 : 3);
        info.setTaskBindTemplateId(fundInfo.getTaskBindTemplateId());
        info.setTaskEndTime(StringUtils.hasText(fundInfo.getEndDate()) ? DateUtil.parse(fundInfo.getEndDate() + " 18:00:00", "yyyy-MM-dd HH:mm:ss") : new Date());
    }

    /**
     * 日常任务内容 - 统计 - 结构化处理
     *
     * @param orgMapping 部门映射
     * @return construct
     */
    private List<LeaderInfoVO> orgConstructDaily(Map<String, List<OpsTaskGenInfo>> orgMapping) {
        List<OpsSysOrg> orgInfo = opsSysOrgService.deptConPostInfos();
        Map<String, OpsTaskTemplate> mp = taskTemplateService.list().stream().collect(Collectors.toMap(OpsTaskTemplate::getId, i -> i, (n2, n1) -> n1));
        List<LeaderInfoVO> res = new ArrayList<>();
        for (OpsSysOrg org : orgInfo) {
            //部门层级的数据组织
            LeaderInfoVO deptVO = new LeaderInfoVO();
            deptVO.setName(org.getOrgName()).setId(String.valueOf(org.getId())).setType(1);
            //部门岗位为空则删除
            if (org.getIdMap().isEmpty()) {
                continue;
            }
            //部门下岗位进行数据分组与计算
            org.getIdMap().forEach((id, en) -> {
                LeaderInfoVO postVO = new LeaderInfoVO();
                postVO.setType(2).setName(en.getOrgName()).setId(id);
                if (orgMapping.containsKey(id)) {
                    deptVO.getChild().add(postVO);
                    List<OpsTaskGenInfo> postArr = orgMapping.get(id);
                    //岗位数据统计
                    //总数 + 经办+ 复核
                    postVO.setTotal((long) postArr.size() + postArr.stream().filter(i -> i.getTaskCheckReq() != null && i.getTaskCheckReq().equals("1")).count());
                    //完成 + 经办+复核 总数
                    postVO.setOver(postArr.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count()
                            + postArr.stream().filter(i -> (i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3) && ((i.getTaskCheckReq() != null && i.getTaskCheckReq().equals("1")))).count());
                    postVO.setRate((NumberUtil.div(postVO.getOver(), postVO.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP)) + "%");
                    //先按岗位下的模板进行分组
                    Map<String, List<OpsTaskGenInfo>> postGroup = postArr.stream().collect(Collectors.groupingBy(task -> Optional.ofNullable(task.getTaskBindTemplateId()).orElse("-100")));
                    postGroup.forEach((tempId, ens) -> {
                        //模板下- 经办内容计算添加
                        LeaderInfoVO tempVO = new LeaderInfoVO();
                        postVO.getChild().add(tempVO);
                        tempVO.setId(tempId).setType(3).setName(mp.get(tempId) == null ? "其他" : mp.get(tempId).getTemplateName());
                        Map<String, LeaderInfoVO> owner = new HashMap<>();
                        Map<String, LeaderInfoVO> check = new HashMap<>();

                        //经办到人的
                        Map<String, List<OpsTaskGenInfo>> tempGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("2")).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        tempGroup.forEach((ownerId, values) -> {
                                    //经办数据统计(经办也分两种 2 是经办到人不用特殊处理 1 是经办到岗位的可以取 operation_complete_id 来处理合并 )
                                    String name = values.get(0).getTaskOwnerVal();
                                    LeaderInfoVO detailVO = new LeaderInfoVO();
                                    detailVO.setId(ownerId).setName(name).setType(4)
                                            .setDetail(values).setTotal((long) values.size()).
                                            setOver(values.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count());
                                    detailVO.setRate(NumberUtil.div(detailVO.getOver(), detailVO.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP) + "%");
                                    owner.put(ownerId, detailVO);
                                }
                        );
                        //经办到岗位的
                        Map<String, List<OpsTaskGenInfo>> tempPostGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("1") && i.getOperationCompleteId() == null).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        tempPostGroup.forEach((ownerId, post) -> {
                            //到岗的没有实际操作人
                            //经办数据统计(经办也分两种 2 是经办到人不用特殊处理 1 是经办到岗位的可以取 operation_complete_id 来处理合并 )
                            String name = post.get(0).getTaskOwnerVal();
                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, post, ownerId);
                            owner.put(ownerId, detailVO);

                        });
                        Map<String, List<OpsTaskGenInfo>> tempPersonGroup = ens.stream().filter(i -> i.getTaskOwnerType() != null &&
                                i.getTaskOwnerType().equals("1") && i.getOperationCompleteId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOperationCompleteId));
                        tempPersonGroup.forEach((k, v) -> {
                            //到岗的但是有实际操作人的
                            //如果有对应的人员，则进行合并
                            if (owner.containsKey(k)) {
                                LeaderInfoVO alVo = owner.get(k);
                                recalculateVO(alVo, v);
                            } else {
                                LeaderInfoVO vo = new LeaderInfoVO();
                                fillDailyVO(vo, userService.findNameIdMapping().get(k), v, k);
                                owner.put(k, vo);
                            }
                        });
                        tempVO.getChild().addAll(owner.values());
                        /**
                         * 复核处理 -----------------------------------------
                         */
                        //模板下复核类型是具体人员的进行统计处理
                        Map<String, List<OpsTaskGenInfo>> checkGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("2")).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskCheckId));
                        checkGroup.forEach((checkId, values) -> {
                            //复核数据统计(复核数据分两种 2 是复核到人的 按人来处理 1 复核到岗的取operation_check_id 来处理  )
                            String name = values.get(0).getTaskCheckVal();
                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, values, checkId);
                            check.put(checkId, detailVO);
                        });
                        //具体复核是岗位的处理
                        //模板下复核类型是具体人员的进行统计处理
                        Map<String, List<OpsTaskGenInfo>> checkPostGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("1") && i.getOperationCheckId() == null).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskCheckId));
                        checkPostGroup.forEach((checkId, post) -> {
                            //到岗的没有实际操作人
                            String name = post.get(0).getTaskOwnerVal();
                            LeaderInfoVO detailVO = new LeaderInfoVO();
                            fillDailyVO(detailVO, name, post, checkId);
                            check.put(checkId, detailVO);
                        });
                        //到岗位-有实际复核人的
                        Map<String, List<OpsTaskGenInfo>> checkPersonGroup = ens.stream().filter(i -> i.getTaskCheckReq() != null
                                && i.getTaskCheckReq().equals("1")
                                && i.getTaskCheckId() != null
                                && i.getTaskCheckType() != null
                                && i.getTaskCheckType().equals("1") && i.getOperationCheckId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOperationCheckId));
                        checkPersonGroup.forEach((checkId, values) -> {
                            //到岗的但是有实际操作人的
                            //如果有对应的人员，则进行合并
                            if (check.containsKey(checkId)) {
                                LeaderInfoVO alVo = check.get(checkId);
                                recalculateVO(alVo, values);
                            } else {
                                LeaderInfoVO vo = new LeaderInfoVO();
                                fillDailyVO(vo, userService.findNameIdMapping().get(checkId), values, checkId);
                                check.put(checkId, vo);
                            }
                        });
                        tempVO.getChild().addAll(check.values());
                    });
                }
            });
            res.add(deptVO);
        }
        return res;
    }

    private void recalculateVO(LeaderInfoVO alVo, List<OpsTaskGenInfo> v) {
        alVo.getDetail().addAll(v);
        alVo.setOver(alVo.getOver() + v.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count());
        alVo.setTotal(alVo.getTotal() + v.size());
        alVo.setRate(NumberUtil.div(alVo.getOver(), alVo.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP) + "%");
    }

    private void recalculateVOV1(LeaderInfoVO alVo, List<OpsTaskGenInfo> v) {
        //alVo.getDetail().addAll(v);
        alVo.setOver(alVo.getOver() + v.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count());
        alVo.setTotal(alVo.getTotal() + v.size());
        alVo.setRate(NumberUtil.div(alVo.getOver(), alVo.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP) + "%");
    }

    private void fillDailyVO(LeaderInfoVO detailVO, String name, List<OpsTaskGenInfo> post, String ownerId) {
        detailVO.setId(ownerId).setName(name).setType(4)
                .setDetail(post).setTotal((long) post.size()).
                setOver(post.stream().filter(i -> i.getTaskCompleteStatus() != null && i.getTaskCompleteStatus() == 3).count());
        detailVO.setRate(NumberUtil.div(detailVO.getOver(), detailVO.getTotal()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP) + "%");
    }


    @Override
    public List<LeaderInfoVO> tempList(String date) {
        //查询所有的任务
        List<OpsTaskGenInfo> res = opsTaskGenInfoService.list(todoWrapper(date));
        //分组
        Map<String, List<OpsTaskGenInfo>> orgMapping = res.stream().filter(i -> i.getOwnerOrgId() != null).collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));
        //分组返回
        return orgConstructTemp(orgMapping);
    }

    private List<LeaderInfoVO> orgConstructTemp(Map<String, List<OpsTaskGenInfo>> orgMapping) {
        List<OpsSysOrg> orgInfo = opsSysOrgService.deptConPostInfos();
        List<LeaderInfoVO> res = new ArrayList<>();
        for (OpsSysOrg org : orgInfo) {
            //部门层级的数据组织
            LeaderInfoVO deptVO = new LeaderInfoVO();
            deptVO.setName(org.getOrgName()).setId(String.valueOf(org.getId())).setType(1);
            //部门岗位为空则删除
            if (org.getIdMap().isEmpty()) {
                continue;
            }
            //部门下岗位进行数据分组与计算
            org.getIdMap().forEach((id, en) -> {
                LeaderInfoVO postVO = new LeaderInfoVO();
                postVO.setType(2).setName(en.getOrgName()).setId(id);
                if (orgMapping.containsKey(id)) {
                    postVO.setDetail(orgMapping.get(id));
                    deptVO.getChild().add(postVO);
                }
            });
            res.add(deptVO);
        }
        return res;
    }

    /**
     * 处理数据与返回结构组织
     *
     * @param orgMapping 部门数据
     * @return ls
     */
    private List<LeaderInfoVO> orgConstruct(Map<String, List<OpsTaskGenInfo>> orgMapping) {
        List<OpsSysOrg> orgInfo = opsSysOrgService.deptConPostInfos();
        Map<String, OpsTaskTemplate> mp = taskTemplateService.list().stream().collect(Collectors.toMap(OpsTaskTemplate::getId, i -> i, (n2, n1) -> n1));
        List<LeaderInfoVO> res = new ArrayList<>();
        for (OpsSysOrg org : orgInfo) {
            //部门层级的数据组织
            LeaderInfoVO deptVO = new LeaderInfoVO();
            deptVO.setName(org.getOrgName()).setId(String.valueOf(org.getId())).setType(1);
            //部门岗位为空则删除
            if (org.getIdMap().isEmpty()) {
                continue;
            }
            //部门下岗位进行数据分组与计算
            org.getIdMap().forEach((id, en) -> {
                LeaderInfoVO postVO = new LeaderInfoVO();
                postVO.setType(2).setName(en.getOrgName()).setId(id);
                if (orgMapping.containsKey(id)) {
                    deptVO.getChild().add(postVO);
                    List<OpsTaskGenInfo> postArr = orgMapping.get(id);
                    postVO.setTotal((long) postArr.size());
                    //先按岗位下的模板进行分组
                    Map<String, List<OpsTaskGenInfo>> postGroup = postArr.stream().collect(Collectors.groupingBy(task -> Optional.ofNullable(task.getTaskBindTemplateId()).orElse("-100")));
                    postGroup.forEach((tempId, ens) -> {
                        LeaderInfoVO tempVO = new LeaderInfoVO();
                        postVO.getChild().add(tempVO);
                        tempVO.setId(tempId).setType(3).setName(mp.get(tempId) == null ? "其他" : mp.get(tempId).getTemplateName());
                        Map<String, List<OpsTaskGenInfo>> tempGroup = ens.stream().filter(i -> StringUtils.hasText(i.getTaskOwnerId())).collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskOwnerId));
                        tempGroup.forEach((ownerId, values) -> {
                                    String name = values.get(0).getTaskOwnerVal();
                                    LeaderInfoVO detailVO = new LeaderInfoVO();
                                    detailVO.setId(ownerId).setName(name).setType(4)
                                            .setDetail(values).setTotal((long) values.size());
                                    tempVO.getChild().add(detailVO);
                                }
                        );
                    });
                }
            });
            res.add(deptVO);
        }
        return res;
    }

    private Wrapper<OpsTaskGenInfo> warnWrapper(String dateTime) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskEndTime,
                OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskOwnerId, OpsTaskGenInfo::getTaskOwnerVal, OpsTaskGenInfo::getTaskOwnerType,
                OpsTaskGenInfo::getTaskBindTemplateId);
        queryWrapper.isNull(OpsTaskGenInfo::getTaskChildIds);
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, 3).ne(OpsTaskGenInfo::getTaskCompleteStatus, 5);
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        //如果有时间参数就是参数值，没时间参数则是当前时间
        queryWrapper.lt(OpsTaskGenInfo::getTaskEndTime, StringUtils.hasText(dateTime) ? DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.parse(dateTime, "yyyy-MM-dd"), -1)) : new Date());
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        return queryWrapper;
    }

    public boolean defferCondition(OpsTaskGenInfo i) {
        //如果不是延时任务
        if (i.getTaskDeferredType() != null && i.getTaskDeferredType().equals("0")) {
            return true;
        }
        //如果是延时任务且是当天内还未延时的数据
        if (i.getTaskDeferredType() != null && i.getTaskDeferredType().equals("1")) {
            //判定延时任务是否为当天且还未延时
            //延时阈值为0，则延时无效
            if (i.getTaskDeferredCount() == 0) {
                return true;
            }
            //如果延时阈值大于0 ，且当天任务还 未被自动任务调整结束时间
            return i.getTaskDeferredCount() <= 0 || !DateUtil.format(i.getTaskEndTime(), "yyyy-MM-dd").equals(i.getTaskGenTime());
        }
        return true;
    }

    private Wrapper<OpsTaskGenInfo> todoWrapper(String dateTime) {
        //第一次查询都为根节点，即pid=0 的
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskEndTime,
                OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskOwnerId, OpsTaskGenInfo::getTaskOwnerVal, OpsTaskGenInfo::getTaskOwnerType,
                OpsTaskGenInfo::getTaskBindTemplateId);
        queryWrapper.eq(OpsTaskGenInfo::getParentId, 0);
        queryWrapper.eq(OpsTaskGenInfo::getTaskType, "temp");
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.last(" and date_format(task_start_time,'%Y-%m-%d') <= '" + dateTime + "' and date_format(task_end_time,'%Y-%m-%d') >='" + dateTime + "' order by create_time desc");

       String date="sd";
        date.replace("-", "").replace(",","");
        return queryWrapper;
    }
}
