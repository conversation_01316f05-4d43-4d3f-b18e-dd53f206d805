package sdata.ops.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.system.model.entity.OpsSysCalendar;
import sdata.ops.base.system.model.entity.OpsTradeType;
import sdata.ops.base.system.model.vo.TaskScriptResultVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.TimeUtil;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import sdata.ops.system.mapper.OpsTradeTypeMapper;
import sdata.ops.system.service.OpsSysCalendarService;
import sdata.ops.system.service.OpsTradeTypeService;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class OpsTradeTypeServiceImpl extends ServiceImpl<OpsTradeTypeMapper, OpsTradeType> implements OpsTradeTypeService {

    private IndicatorInfoFeign indicatorInfoFeign;

    @Autowired
    @Lazy
    public void setIndicatorInfoFeign(IndicatorInfoFeign indicatorInfoFeign) {
        this.indicatorInfoFeign = indicatorInfoFeign;
    }

    private final OpsSysCalendarService calendarService;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(8, 16, 60L, TimeUnit.SECONDS, new LinkedBlockingDeque<>());

    /**
     * 根据交易日类型ID，查询当日是否复核条件
     * <p>
     * todo 新增: 逻辑需要优化,返回真或假之前需要判定去重，另一个需要对查询指标进行一些参数保存,保存到绑定的任务上来辅助后续的自动完成状态查询 或者其他动作
     * 脚本返回对象为固定 commonRule{match:true|false,params:{}}
     * match返回规则命中脚本，params为附加参数如有则存储当前方法获取到该match为true时，要查询当前id在当天内是否已经执行，如果是则结果置为false
     * </p>
     *
     * @param id 交易日类型ID
     * @return 返回结果
     */
    @Override
    public TaskScriptResultVO checkDateType(String id) {
        OpsTradeType tradeType = this.getById(id);
        if (tradeType == null) {
            log.error(String.format("交易日类型查询id:%s不存在", id));
            return new TaskScriptResultVO();
        }
        String indicatorId = tradeType.getIndicatorId();
        if (indicatorId == null) {
            log.error(String.format("交易日类型：%s,未查询到指标", tradeType.getName()));
            return new TaskScriptResultVO();
        }
        // 调用指标
        IndicatorInfoDTO dto = new IndicatorInfoDTO();
        dto.setId(indicatorId);
        dto.setParam(new JSONObject());
        dto.getParam().set("id",indicatorId);
        R<Object> rpc = indicatorInfoFeign.rpc(dto);
        return JSONUtil.toBean(rpc.getData().toString(), TaskScriptResultVO.class);
    }

    @Override
    public boolean checkWorkdayByToday() {
        String dateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        return calendarService.getBaseMapper().exists(Wrappers.lambdaQuery(OpsSysCalendar.class).eq(OpsSysCalendar::getCalendarDate, dateStr).eq(OpsSysCalendar::getTrade, "Y"));
    }

    @Override
    public String getLastWorkDay(String date){
        String rangeDate=DateUtil.format(DateUtil.offsetDay(DateUtil.parse(date,"yyyyMMdd"),-11),"yyyy-MM-dd");
        return calendarService.getLastWorkByFixed(date,rangeDate);
    }

    /**
     * 根据交易日类型ID，查询当日是否复核条件
     *
     * @param idList id列表
     * @return 返回结果
     */
    @Override
    public Map<String, TaskScriptResultVO> batchCheckDateType(List<String> idList) {
        idList = idList.stream().distinct().collect(Collectors.toList());
        Map<String, Future<TaskScriptResultVO>> futureMap = new HashMap<>(64);
        // Feign接口的Request 默认是主线程和子线程不共享的，当异步调用Feign接口会因为获取不到ServletRequestAttributes报空指针
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        idList.forEach(r -> {
            // 启动线程
            Future<TaskScriptResultVO> submit = executor.submit(() -> {
                // 在线程中设置attributes
                RequestContextHolder.setRequestAttributes(attributes);
                return this.checkDateType(r);
            });
            futureMap.put(r, submit);
        });
        Map<String, TaskScriptResultVO> resultMap = new HashMap<>(64);
        // 阻塞获取线程结果
        for (String key : futureMap.keySet()) {
            try {
                resultMap.put(key, futureMap.get(key).get());
            } catch (Exception e) {
                log.error("脚本调用失败id={}",key,e);
                resultMap.put(key, new TaskScriptResultVO());
            }
        }
        return resultMap;
    }

    @Override
    public void triggerScriptExecute(String id, String absFilePath,String date) {
        // 调用指标
        IndicatorInfoDTO dto = new IndicatorInfoDTO();
        dto.setId(id);
        dto.setParam(new JSONObject());
        dto.getParam().set("id",id);
        dto.getParam().set("path",absFilePath);
        dto.getParam().set("bizDate",date);
        R<Object> rpc = indicatorInfoFeign.rpc(dto);
    }


    @Override
    public R<Object> triggerScriptExecuteForExport(String id, String stDate, String etDate) {
        // 调用指标
        IndicatorInfoDTO dto = new IndicatorInfoDTO();
        dto.setId(id);
        dto.setParam(new JSONObject());
        dto.getParam().set("id",id);
        dto.getParam().set("st",stDate);
        dto.getParam().set("et",etDate);
        return indicatorInfoFeign.rpc(dto);
    }

    @Override
    public String getNextWorkDay(String today) {
        today=DateUtil.format(DateUtil.parse(today,"yyyyMMdd"),"yyyy-MM-dd");
        String rangeDate=DateUtil.format(DateUtil.offsetDay(DateUtil.parse(today,"yyyy-MM-dd"),+11),"yyyy-MM-dd");
        return calendarService.getNextWorkByFixed(today,rangeDate);
    }

    @Override
    public String getNextWorkDayOffset(String day, Integer offset) {
        day= TimeUtil.convertToStandardFormat(day);
        String rangeDate=DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day,"yyyy-MM-dd"),+11),"yyyy-MM-dd");
        return calendarService.getNextWork(day,rangeDate,offset);
    }

    @Override
    public String getLastWorkDayOffset(String day, int abs) {
        day=TimeUtil.convertToStandardFormat(day);
        String rangeDate=DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day,"yyyy-MM-dd"),-11),"yyyy-MM-dd");
        return calendarService.getLastWork(day,rangeDate,abs);
    }

}
