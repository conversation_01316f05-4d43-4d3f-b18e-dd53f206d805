package sdata.ops.system.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.Properties;

@Configuration
@EnableScheduling
public class QuartzConfig {
//    @Bean
//    public SchedulerFactoryBean schedulerFactoryBean() {
//        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
//        schedulerFactoryBean.setSchedulerName("MyTaskScheduler");
//        schedulerFactoryBean.setApplicationContextSchedulerContextKey("applicationContext");
//        schedulerFactoryBean.setOverwriteExistingJobs(true);
//        schedulerFactoryBean.setStartupDelay(5); // 5 seconds delay
//        // Customize Quartz configuration
//        schedulerFactoryBean.setQuartzProperties(quartzProperties());
//        return schedulerFactoryBean;
//    }
//
//    private Properties quartzProperties() {
//        Properties properties = new Properties();
//        properties.setProperty("org.quartz.scheduler.instanceName", "MyTaskScheduler");
//        properties.setProperty("org.quartz.scheduler.instanceId", "AUTO");
//        properties.setProperty("org.quartz.jobStore.class", "org.quartz.simple.RAMJobStore");
//        properties.setProperty("org.quartz.threadPool.threadCount", "5");
//        return properties;
//    }

}
