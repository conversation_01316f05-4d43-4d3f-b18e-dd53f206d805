package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsSysDataPermDimensionValue;
import sdata.ops.system.mapper.OpsSysDataPermDimensionValueMapper;
import sdata.ops.system.service.OpsSysDataPermDimensionValueService;

/**
* <AUTHOR>
* @description 针对表【ops_sys_data_perm_dimension_value(权限维度值存储表)】的数据库操作Service实现
* @createDate 2025-08-25 17:47:36
*/
@Service
public class OpsSysDataPermDimensionValueServiceImpl extends ServiceImpl<OpsSysDataPermDimensionValueMapper, OpsSysDataPermDimensionValue>
    implements OpsSysDataPermDimensionValueService {

}




