package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsTaskDynamicLog;

/**
 * <AUTHOR>
 * @description 针对表【OPS_TASK_DYNAMIC_LOG(自定义任务已经触发成功的日志信息)】的数据库操作Mapper
 * @createDate 2024-08-09 09:56:56
 * @Entity generator.domain.OpsTaskDynamicLog
 */
@Mapper
public interface OpsTaskDynamicLogMapper extends BaseMapper<OpsTaskDynamicLog> {

}




