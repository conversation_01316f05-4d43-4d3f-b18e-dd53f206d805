package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.dto.MapperDTO;
import sdata.ops.base.system.model.dto.OrgUserDTO;
import sdata.ops.base.system.model.entity.SystemUser;

import java.util.List;

@Mapper
public interface SystemUserMapper extends BaseMapper<SystemUser> {

    List<SystemUser> selectAllocatedList(SystemUser user);

    List<SystemUser> selectUnallocatedList(SystemUser user);

    List<MapperDTO>  findAllOrgAndRoleName();

    void updateUserStatus(@Param("id")String id,@Param("status")Integer status);

    List<MapperDTO> queryAllUserShortInfo();

    List<OrgUserDTO> queryAllUserShortFailName();

    List<SystemUser> findUserByLeaderId(@Param("orgId") String orgId);

    String getLeaderForOrgId(@Param("userId") String userId);
}
