package sdata.ops.system.controller;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.base.flow.model.vo.FlowTestVO;
import sdata.ops.base.system.model.vo.SystemTestVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.system.job.TaskJob;

@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
public class SystemTestController {



    @ControllerAuditLog(value = "获取系统测试数据", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/b")
    public SystemTestVO getSystem(){
        return new SystemTestVO().setSystemId("c").setSystemName("system");
    }


    private final TaskJob job;
    @ControllerAuditLog(value = "执行系统测试任务", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/oath")
    public R<Object> res(@RequestParam("type")String type){
        if(type.equals("oadata")){
            job.obtThirdSystemDataForSch();
        }
        if(type.equals("oaexc")){
            job.updateSpecDetailTask();
        }
        return R.success("true");
    }

}
