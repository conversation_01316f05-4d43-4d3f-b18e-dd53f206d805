package sdata.ops.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.entity.OpsViewTemplate;
import sdata.ops.system.mapper.OpsViewTemplateMapper;
import sdata.ops.system.service.OpsViewGroupRelationService;
import sdata.ops.system.service.OpsViewTemplateService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_view_template】的数据库操作Service实现
 * @createDate 2025-05-28 19:20:00
 */
@Service
@RequiredArgsConstructor
public class OpsViewTemplateServiceImpl extends ServiceImpl<OpsViewTemplateMapper, OpsViewTemplate>
        implements OpsViewTemplateService {

    private final OpsViewGroupRelationService groupRelation;

    @Override
    public IPage<OpsViewTemplate> pageCustom(IPage<OpsViewTemplate> pageEntity, Integer groupId, String name) {
        //如果groupId为空则返回所有
        if (groupId == null) {
            return page(pageEntity, Wrappers.lambdaQuery(OpsViewTemplate.class).like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(name), OpsViewTemplate::getName, name));
        }
        //查询出所有关联的id
        List<Object> ids = groupRelation.listObjs(Wrappers.lambdaQuery(OpsViewGroupRelation.class).select(OpsViewGroupRelation::getViewId).eq(OpsViewGroupRelation::getGroupId, groupId));
        if (ids.isEmpty()) {
            return new Page<>();
        }
        //批量查询相关模板
        LambdaUpdateWrapper<OpsViewTemplate> wrapper = Wrappers.lambdaUpdate(OpsViewTemplate.class);
        wrapper.like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(name), OpsViewTemplate::getName, name);
        wrapper.in(OpsViewTemplate::getId, ids);
        return page(pageEntity, wrapper);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelation(OpsViewGroupRelation ls) {
        groupRelation.remove(Wrappers.lambdaQuery(OpsViewGroupRelation.class).eq(OpsViewGroupRelation::getViewId, ls.getViewId()));
        groupRelation.save(ls);
    }
}
