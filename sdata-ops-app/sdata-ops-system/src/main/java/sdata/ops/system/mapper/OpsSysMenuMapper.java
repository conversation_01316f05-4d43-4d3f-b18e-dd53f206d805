package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsSysMenu;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_menu(菜单权限表)】的数据库操作Mapper
* @createDate 2024-06-03 19:48:02
* @Entity sdata.ops.base.system.api.entity.OpsOpsSysMenu
*/
@Mapper
 public interface OpsSysMenuMapper extends BaseMapper<OpsSysMenu> {


    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
     List<OpsSysMenu> selectMenuList(OpsSysMenu menu);

    /**
     * 根据用户所有权限
     *
     * @return 权限列表
     */
     List<String> selectMenuPerms();

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
     List<OpsSysMenu> selectMenuListByUserId(OpsSysMenu menu);

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
     List<String> selectMenuPermsByRoleId(String roleId);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
     List<String> selectMenuPermsByUserId(String userId);

    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */
     List<OpsSysMenu> selectMenuTreeAll();

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
     List<OpsSysMenu> selectMenuTreeByUserId(String userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
     List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") int menuCheckStrictly);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
     OpsSysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
     int hasChildByMenuId(Long menuId);

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
     int insertMenu(OpsSysMenu menu);

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
     int updateMenu(OpsSysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @return 结果
     */
     OpsSysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);

    void updateMenuVisible(@Param("id") String id, @Param("status") Integer status);

    void updateMenuStatus(@Param("id") String id, @Param("status") Integer status);
}




