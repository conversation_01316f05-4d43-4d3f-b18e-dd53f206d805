package sdata.ops.system.controller;


import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.config.propertites.LoginConfigParams;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.handler.TokenHandler;
import sdata.ops.common.core.model.CasResult;
import sdata.ops.common.core.model.TokenResult;
import sdata.ops.common.core.util.AesUtils;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.handler.CasHandler;
import sdata.ops.system.handler.LoginAuthHandler;
import sdata.ops.system.service.SystemUserService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("/loginController")
@RequiredArgsConstructor
public class SystemLoginController {

    private final LoginConfigParams loginConfig;

    private final SystemUserService systemUserService;

    private final Map<String, LoginAuthHandler> handlerMap;

    private final Map<String, CasHandler> casHandlerMap;

    @ControllerAuditLog(value = "用户登录", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @RequestMapping("/login")
    @Transactional(rollbackFor = Exception.class)
    public String opsSystemLogin(String username, String password, HttpServletRequest request, HttpServletResponse response) throws IOException {

        //登录情况来看
        //1.获取用户名密码 - 查询是否有效 - 响应参数token || 分系统本身登录功能与LDAP登录功能
        if (loginConfig.getLoginModel().equals("SYSTEM") || loginConfig.getLoginModel().equals("LDAP")) {
            Assert.notNull(username, "登录账号不能为空!");
            Assert.notNull(password, "登录密码不能为空!");
            password = AesUtils.decryptStr(password);
            SystemUser user = handlerMap.get(loginConfig.getLoginModel()).userVerity(username, password);
            if (Objects.isNull(user)) {
                LoginConfigParams.writeJsonResponse(response, R.fail(MessageConstant.LOGIN_FAIL));
                return null;
            }
            loginAction(user, null, response);
        }
        if (loginConfig.getLoginModel().equals("CAS")) {
            String ticket = request.getParameter(loginConfig.getCasKeyword());
            //无票据则发起重定向到cas认证地址
            if (!StringUtils.hasText(ticket)) {
                String currUrl = request.getRequestURL().toString();
                String clientLoginUrl = currUrl + "?back=" + LoginConfigParams.encodeUrl(loginConfig.getCasCallback());
                String serverAuthUrl = loginConfig.getCasServer() + "?" + loginConfig.getCasRedirectKeyword() + "=" + clientLoginUrl;
                response.sendRedirect(serverAuthUrl);
            } else {
                //cas登录成功后重定向
                //校验 ticket
                //1.ticket 校验不通过响应失败信息
                //2.ticket 校验通过响应本系统token
                CasResult result = casHandlerMap.get(loginConfig.getCasHandler()).verifyTicket(ticket);
                if (result.getState()) {
                    SystemUser systemUser = systemUserService.findOrCreateUser(result.getAccount(), null);
                    loginAction(systemUser, null, response);
                } else {
                    LoginConfigParams.writeJsonResponse(response, R.fail(MessageConstant.CAS_LOGIN_FAIL));
                }
            }
            return null;
        }
        if (loginConfig.getLoginModel().equals("TOKEN")) {
            String userToken = request.getParameter("user-token");
            TokenHandler tokenHandler = SpringBeanUtil.getBean(loginConfig.getTokenHandler());
            TokenResult result = tokenHandler.verifyToken(userToken);
            SystemUser systemUser = systemUserService.findOrCreateUser(result.getAccount(), null);
            loginAction(systemUser, userToken, response);
        }
        return null;
    }

    private void loginAction(SystemUser user, String token, HttpServletResponse response) {
        StpUtil.login(user.getId(), SaLoginConfig.setExtraData(Map.of("userId", user.getId(),
                "username", user.getUsername(),
                "name", user.getName(),
                "role", user.getRole())));
        StpUtil.getSession().set("user", user);
        String tokens = StrUtil.isBlank(token) ? StpUtil.getTokenSession().getToken() : token;
        LoginConfigParams.writeJsonResponse(response, R.data(genericRes(tokens, user)));
    }

    private Object genericRes(String tokens, SystemUser user) {
        Map<String, Object> res = new HashMap<>();
        user.setPassword(null);
        res.put("user", user);
        res.put("accessToken", tokens);
        return res;
    }

    @ControllerAuditLog(value = "退出登录", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/loginOut")
    @ResponseBody
    public R<String> loginOut() {
        StpUtil.logout();
        return R.success("退出登录");
    }

}
