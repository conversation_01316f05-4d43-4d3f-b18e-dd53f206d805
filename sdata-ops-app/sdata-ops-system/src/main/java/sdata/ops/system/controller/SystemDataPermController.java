package sdata.ops.system.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysDataPerm;
import sdata.ops.base.system.model.vo.OpsPermSaveVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.DataMetaService;
import sdata.ops.system.service.OpsSysDataPermService;
import sdata.ops.system.service.SystemUserService;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 数据权限接口
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/dataPerm")
public class SystemDataPermController {


    private final OpsSysDataPermService opsSysDataPermService;

    private final SystemUserService systemUserService;

    private final DataMetaService dataMetaService;

    /**
     * 数据配置信息列表
     *
     * @return list
     */
    @ControllerAuditLog(value = "数据权限配置列表接口", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list")
    public R<Object> modelList(@RequestParam(value = "permName", required = false) String permName,
                               @RequestParam(value = "status", required = false) String status,
                               @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {

        LambdaQueryWrapper<OpsSysDataPerm> likeQuery = new LambdaQueryWrapper<>();
        likeQuery.like(StringUtils.hasText(permName), OpsSysDataPerm::getPermName, permName);
        likeQuery.eq(StringUtils.hasText(status), OpsSysDataPerm::getStatus, status);
        Page<OpsSysDataPerm> pageEntity = new Page<>(page, pageSize);
        Map<String, String> mapper = systemUserService.findNameIdMapping();
        IPage<OpsSysDataPerm> ls = opsSysDataPermService.page(pageEntity, likeQuery).convert(i -> {
            i.setCreateBy(mapper.get(i.getCreateBy()));
            i.setUpdateBy(mapper.get(i.getUpdateBy()));
            return i;
        });
        return R.data(ls);
    }


    /**
     * 数据配置信息列表,仅包含id和 name
     *
     * @return list
     */
    @ControllerAuditLog(value = "数据权限配置列表接口（只返回id与描述内容）", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/listShort")
    public R<Object> modelListShort() {
        LambdaQueryWrapper<OpsSysDataPerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OpsSysDataPerm::getId, OpsSysDataPerm::getPermName);
        List<OpsSysDataPerm> ls = opsSysDataPermService.list(wrapper);
        return R.data(ls);
    }

    /**
     * 数据配置列表保存
     */
    @ControllerAuditLog(value = "数据权限配置保存或更新接口", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    @PostMapping("/save")
    public R<String> saveEntity(@RequestBody OpsPermSaveVO perm) {
        opsSysDataPermService.saveOrUpdateAllConf(perm);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 数据权限配置删除
     */
    @ControllerAuditLog(value = "数据权限配置删除", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/delete")
    public R<String> deleteEntity(@RequestParam("id") String id) {
        opsSysDataPermService.deletedOperation(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 数据权限引用数据来源类型
     *
     * @return list
     */
    @ControllerAuditLog(value = "数据权限配置数据来源列表查询", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/model/list")
    public R<Object> permModelList() throws SQLException {
        return R.data(dataMetaService.getTableList());
    }

    /**
     * 数据权限引用数据来源类型配置存储
     *
     * @param name tableName
     * @return ls
     */
    @ControllerAuditLog(value = "数据权限-引用数据来源类型配置存储", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/model/result")
    public R<Object> permModelSave(@RequestParam("tableName") String name) throws SQLException {
        return R.data(dataMetaService.getTableMetaData(name));
    }

    /**
     * 数据权限-编辑详情获取
     *
     * @param id 权限id
     * @return detail
     */
    @ControllerAuditLog(value = "数据权限-编辑详情获取", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getDetailById")
    public R<OpsPermSaveVO> detail(@RequestParam("id") String id) {
        return R.data(opsSysDataPermService.queryDetail(id));
    }


    /**
     * openFeign 调用
     *
     * @param id 权限id
     * @return 权限信息
     */
    @ControllerAuditLog(value = "数据权限-权限详情（openFeign 调用）", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getById")
    public R<Object> getById(@RequestParam("id") String id) {
        return R.data(opsSysDataPermService.getById(id));
    }

    /**
     * openFeign 调用
     *
     * @param id     权限id
     * @param userId 用户id
     * @return 配置内容
     */
    @ControllerAuditLog(value = "数据权限-指定用户的权限详情（openFeign 调用）", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getDataPermById")
    public JSONObject getDataPermById(@RequestParam("id") String id, @RequestParam("userId") String userId) {
        return (opsSysDataPermService.getDataPermById(id, userId));
    }

    @ControllerAuditLog(value = "数据权限-修改状态（openFeign 调用）", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/changeStatus")
    public R<Object> getDataPermById(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        LambdaUpdateWrapper<OpsSysDataPerm> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsSysDataPerm::getStatus, status).eq(OpsSysDataPerm::getId, id);
        return (R.data(opsSysDataPermService.update(updateWrapper)));
    }
}
