package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsSysDataModel;
import sdata.ops.system.mapper.OpsSysDataModelMapper;
import sdata.ops.system.service.OpsSysDataModelService;

/**
* <AUTHOR>
* @description 针对表【ops_sys_data_model(数据授权来源信息配置表)】的数据库操作Service实现
* @createDate 2024-07-02 16:45:58
*/
@Service
public class OpsSysDataModelServiceImpl extends ServiceImpl<OpsSysDataModelMapper, OpsSysDataModel>
    implements OpsSysDataModelService {

}




