package sdata.ops.system.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.dto.FilterDTO;
import sdata.ops.base.indicator.model.vo.FilterVo;
import sdata.ops.base.system.model.dto.AuditLogQueryDto;
import sdata.ops.base.system.model.vo.AuditLogExportVo;
import sdata.ops.common.annotation.ExportGroup;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.model.AuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.AuditLogService;
import sdata.ops.system.util.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/auditlog")
@RequiredArgsConstructor
public class AuditLogController {
    private final AuditLogService auditLogService;

    @ControllerAuditLog(value = "审计日志-下拉菜单", moduleName = ModuleName.SYSTEM, operateType = OperateType.QUERY)
    @GetMapping("/filter")
    public R<List<FilterVo>> fileter(FilterDTO dto) {
        if ("moduleName".equals(dto.getKey())) {
            List<FilterVo> list = Arrays.stream(ModuleName.class.getFields())
                    .map(f -> {
                        try {
                            return f.get(null);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .map(String::valueOf)
                    .map(n -> new FilterVo(n, n))
                    .collect(Collectors.toList());
            return R.data(list);
        }
        if ("operatorCode".equals(dto.getKey())) {
            List<FilterVo> list = auditLogService.query()
                    .select("DISTINCT operator_code, operator_name")
                    .orderByAsc("operator_code")
                    .list()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(n -> new FilterVo(n.getOperatorName(), n.getOperatorCode()))
                    .collect(Collectors.toList());
            return R.data(list);
        }
        return R.data(Collections.emptyList());
    }

    @ControllerAuditLog(value = "审计日志-分页查询", moduleName = ModuleName.SYSTEM, operateType = OperateType.QUERY)
    @GetMapping("/page")
    public R<Object> page(AuditLogQueryDto dto) {
        Page<AuditLog> page = auditLogService.lambdaQuery()
                .select(
                        AuditLog::getId,
                        AuditLog::getModuleName,
                        AuditLog::getOperatorName,
                        AuditLog::getOperatorCode,
                        AuditLog::getOperatorId,
                        AuditLog::getOperatorIp,
                        AuditLog::getUrl,
                        AuditLog::getOperatorTime,
                        AuditLog::getLogCode,
                        AuditLog::getLogName,
                        AuditLog::getTraceId,
                        AuditLog::getStatus,
                        AuditLog::getNickname,
                        AuditLog::getClientLocation
                )
                .eq(StringUtils.isNotBlank(dto.getOperatorCode()), AuditLog::getOperatorCode, dto.getOperatorCode())
                .ge(dto.getOpStartTime() != null, AuditLog::getOperatorTime, dto.getOpStartTime())
                .le(dto.getOpEndTime() != null, AuditLog::getOperatorTime, dto.getOpEndTime())
                .orderByDesc(AuditLog::getOperatorTime)
                .page(new Page<>(dto.getPageNo(), dto.getPageSize()));
        return R.data(page);
    }

    @GetMapping("detail")
    @ControllerAuditLog(value = "审计日志-详情", moduleName = ModuleName.SYSTEM, operateType = OperateType.QUERY)
    public R<Object> detail(@RequestParam("id") String id) {
        return R.data(auditLogService.getById(id));
    }

    @GetMapping("export")
    @ControllerAuditLog(value = "审计日志-导出", moduleName = ModuleName.SYSTEM, operateType = OperateType.EXPORT)
    public R<Object> export(@Validated(ExportGroup.class) AuditLogQueryDto dto, HttpServletResponse response) {
        // 计算两个日期不能超过15天
        if (DateUtil.between(dto.getOpStartTime(), dto.getOpEndTime(), DateUnit.DAY) > 15) {
            return R.fail("导出时间不能超过15天");
        }
        // 查询数据
        List<AuditLogExportVo> list = auditLogService.lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getOperatorCode()), AuditLog::getOperatorCode, dto.getOperatorCode())
                .ge(AuditLog::getOperatorTime, dto.getOpStartTime())
                .le(AuditLog::getOperatorTime, dto.getOpEndTime())
                .orderByDesc(AuditLog::getOperatorTime)
                .list()
                .stream()
                .map(AuditLogExportVo::of)
                .collect(Collectors.toList());
        // 导出excel
        ExcelUtil.exportExcel(list, "审计日志.xlsx", response);
        return null;
    }

    @DeleteMapping
    @ControllerAuditLog(value = "审计日志-删除", moduleName = ModuleName.SYSTEM, operateType = OperateType.DELETE)
    public R<Object> delete(@Validated(ExportGroup.class) @RequestBody AuditLogQueryDto dto) {
        // 删除指定时间区间内数据
        auditLogService.clear(dto.getOpStartTime(), dto.getOpEndTime());
        return R.success("删除成功");
    }
}
