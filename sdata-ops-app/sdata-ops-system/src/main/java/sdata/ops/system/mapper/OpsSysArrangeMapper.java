package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.dto.OpsSysArrangeDTO;
import sdata.ops.base.system.model.dto.OpsSysShiftDTO;
import sdata.ops.base.system.model.entity.OpsSysArrange;
import sdata.ops.base.system.model.entity.OpsSysShift;
import sdata.ops.base.system.model.vo.OpsSysArrangeVO;

import java.util.List;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:35
 */

@Mapper
public interface OpsSysArrangeMapper extends BaseMapper<OpsSysArrange> {


    Integer findByShiftId(@Param("id") String id);

    List<OpsSysArrangeVO> list(@Param("id") String id, @Param("startDate") String startDate, @Param("endDate") String endDate);

    void del(@Param("opsSysArrange") OpsSysArrangeDTO opsSysArrange);

    Integer findByOrgShiftUserDate(@Param("opsSysArrange") OpsSysArrange opsSysArrange);

    void delByDate(@Param("opsSysArrangedDTO") OpsSysArrangeDTO opsSysArrangedDTO);
}
