package sdata.ops.system.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import sdata.ops.base.system.model.dto.OpsSysCalendarDTO;
import sdata.ops.base.system.model.entity.OpsSysCalendar;
import sdata.ops.base.system.model.entity.OpsSysCalendarConfig;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.system.job.TaskException;
import sdata.ops.system.mapper.OpsSysCalendarConfigMapper;
import sdata.ops.system.mapper.OpsSysCalendarMapper;
import sdata.ops.system.service.OpsSysCalendarService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
@Service
@RequiredArgsConstructor

public class OpsSysCalendarServiceImpl extends ServiceImpl<OpsSysCalendarMapper, OpsSysCalendar> implements OpsSysCalendarService {

    private final OpsSysCalendarConfigMapper opsSysCalendarConfigMapper;

    /**
     * 初始化日历
     *
     * @param startYear 开始年度 yyyy
     * @param endYear   结束年度 yyyy
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void init(String startYear, String endYear,String market) {
        // 1、删除数据
        LambdaQueryWrapper<OpsSysCalendar> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.ge(OpsSysCalendar::getCalendarDate, startYear + "-01-01");
        deleteWrapper.le(OpsSysCalendar::getCalendarDate, endYear + "-12-31");
        deleteWrapper.eq(OpsSysCalendar::getMarket, market);
        this.remove(deleteWrapper);

        // 2、生成日历
        //查询对应的节假日配置,初始化
         LambdaQueryWrapper<OpsSysCalendarConfig> opsSysCalendarConfigLambdaQueryWrapper =
                Wrappers.lambdaQuery();
        opsSysCalendarConfigLambdaQueryWrapper.eq(OpsSysCalendarConfig::getMarket,market);
        List<OpsSysCalendarConfig> opsSysCalendarConfigList =
                opsSysCalendarConfigMapper.selectList(opsSysCalendarConfigLambdaQueryWrapper);
        LocalDate startDate = LocalDate.of(Integer.parseInt(startYear), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(endYear), 12, 31);
        List<OpsSysCalendar> calendarList = new ArrayList<>(1024);
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            OpsSysCalendar calendar = new OpsSysCalendar();
            calendar.setCalendarDate(date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            calendar.setTrade(CommonConstant.TRADE_N);
            if (date.getDayOfWeek().getValue() <= 5) {
                // 周一到周五,默认为工作日
                calendar.setTrade(CommonConstant.TRADE_Y);
            }
            //判定是否符合节假日配置
            for (OpsSysCalendarConfig opsSysCalendarConfig : opsSysCalendarConfigList) {
                if(
                        calendar.getCalendarDate().compareTo(opsSysCalendarConfig.getStartDate())>=0
                        && calendar.getCalendarDate().compareTo(opsSysCalendarConfig.getStopDate())<=0
                ){
                    calendar.setTrade(CommonConstant.TRADE_N);
                    calendar.setRemark(opsSysCalendarConfig.getRemark());
                }
            }

            calendar.setMarket(market);
            calendar.setYearVal(String.valueOf(date.getYear()));
            calendar.setMonthVal(String.valueOf(date.getMonthValue()));
            calendar.setWeekVal(String.valueOf(date.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR)));
            calendar.setDayWeek(String.valueOf(date.getDayOfWeek().getValue()));

            calendar.setDateTime(DateUtil.parse(date.getYear()+"-"+date.getMonthValue()+"-"+date.getDayOfMonth()+" 00:00:00","yyyy-MM-dd HH:mm:ss"));
            calendarList.add(calendar);
        }
        if (!calendarList.isEmpty()) {
            super.saveBatch(calendarList);
        }
    }

    /**
     * 更新日历
     *
     * @param dto 节假日的日期
     */
    @Override
    public void updateCalendar(OpsSysCalendarDTO dto) {
        List<String> dateList = new ArrayList<>(64);
        // 元旦
        dateList.addAll(this.between(dto.getNewYearDayStart(), dto.getNewYearDayEnd()));
        // 春节
        dateList.addAll(this.between(dto.getSpringFestivalStart(), dto.getSpringFestivalEnd()));
        // 清明节
        dateList.addAll(this.between(dto.getTombSweepingDayStart(), dto.getTombSweepingDayEnd()));
        // 劳动节
        dateList.addAll(this.between(dto.getLabourDayStart(), dto.getLabourDayEnd()));
        // 端午节
        dateList.addAll(this.between(dto.getDragonBoatFestivalStart(), dto.getDragonBoatFestivalEnd()));
        // 中秋节
        dateList.addAll(this.between(dto.getMidAutumnFestivalStart(), dto.getMidAutumnFestivalEnd()));
        // 国庆节
        dateList.addAll(this.between(dto.getNationalDayStart(), dto.getNationalDayEnd()));
        if (!dateList.isEmpty()) {
            LambdaUpdateWrapper<OpsSysCalendar> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(OpsSysCalendar::getCalendarDate, dateList);
            updateWrapper.set(OpsSysCalendar::getTrade, CommonConstant.TRADE_N);
            updateWrapper.set(OpsSysCalendar::getHoliday,"1");
            this.update(updateWrapper);
        }
    }

    /**
     * 未来日期的
     *
     * @param taskDeferredCount 延期阈值
     * @param taskEndTime
     * @return 未来的值
     */
    @Override
    public Date queryFeuDate(Integer taskDeferredCount, Date taskEndTime) throws TaskException {
        //先取今天的日期
        String today=DateUtil.format(new Date(),"yyyy-MM-dd");
        //再冗余计算未来*4倍的日期
        String moDay=DateUtil.format(DateUtil.offsetDay(new Date(),taskDeferredCount*40),"yyyy-MM-dd");
        OpsSysCalendar tradeDay=baseMapper.selectStringDate(today,moDay,taskDeferredCount);
        if(Objects.isNull(tradeDay)){
            throw new TaskException("延期任务执行异常,延期工作日期时间未找到", TaskException.Code.UNKNOWN);
        }
        String hourMinute=DateUtil.format(taskEndTime,"HH:mm");
        //返回延期后的时间
        return DateUtil.parse(tradeDay.getCalendarDate()+" "+hourMinute+":00","yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 通过开始时间和结束时间查询中间的交易日期
     * */
    @Override
    public List<String> listByDate(String startDate, String endDate) {
        return baseMapper.listByDate(startDate,endDate);
    }

    @Override
    public String getLastWorkByFixed(String date, String rangeDate) {
        return baseMapper.listRowNumberFixed(rangeDate,date);
    }

    @Override
    public String getNextWorkByFixed(String today, String rangeDate) {
        return baseMapper.nextWorkdayFixed(today,rangeDate);
    }

    @Override
    public String getNextWork(String day, String rangeDate, Integer offset) {
        return baseMapper.nextWorkday(day,rangeDate,offset);
    }

    @Override
    public String getLastWork(String day, String rangeDate, int abs) {
        return baseMapper.listRowNumber(day,rangeDate,abs);
    }

    @Override
    public List<OpsSysCalendar> listByDateAllMarket(String startDate, String endDate) {
        LambdaQueryWrapper<OpsSysCalendar> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(OpsSysCalendar::getCalendarDate, startDate, endDate);
        queryWrapper.eq(OpsSysCalendar::getTrade,"Y");
        return list(queryWrapper);
    }

    @Override
    public Boolean isTradeDay(LocalDate date, String market) {
        Objects.requireNonNull(market);
        Assert.isTrue(Arrays.asList("0", "1", "2", "3").contains(market), "market 参数只支持1,2,3");
        if ("0".equals(market)) {
            // 0 代表自然日，每天都是true
            return true;
        }

        return super.lambdaQuery()
                .eq(OpsSysCalendar::getCalendarDate, date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)))
                .eq(OpsSysCalendar::getMarket, market)
                .eq(OpsSysCalendar::getTrade, CommonConstant.TRADE_Y)
                .exists();
    }

    /**
     * 根据开始结束日期查询中间的日期
     *
     * @param startDateStr 开始时间 yyyy-MM-dd
     * @param endDateStr   结束时间 yyyy-MM-dd
     * @return 返回结果
     */
    private List<String> between(String startDateStr, String endDateStr) {
        if (StrUtil.isEmptyIfStr(startDateStr) || StrUtil.isEmptyIfStr(endDateStr)) {
            return new ArrayList<>();
        }
        if (startDateStr.equals(endDateStr)) {
            return List.of(startDateStr);
        }
        List<String> dateList = new ArrayList<>(8);
        LocalDate startDate = LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
        LocalDate endDate = LocalDate.parse(endDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dateList.add(date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        return dateList;
    }

}
