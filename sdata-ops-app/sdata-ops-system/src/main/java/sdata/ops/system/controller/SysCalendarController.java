package sdata.ops.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sdata.ops.base.system.model.dto.OpsSysCalendarDTO;
import sdata.ops.base.system.model.entity.OpsSysCalendar;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsSysCalendarService;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static sdata.ops.common.api.CommonConstant.STOP_TRADE_DAY_Y;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:55
 */
@RestController
@RequestMapping("/calendar")
@RequiredArgsConstructor
@Slf4j
public class SysCalendarController {

    private final OpsSysCalendarService calendarService;

    @GetMapping("/listByPage")
    public R<IPage<OpsSysCalendar>> listUserByPage(
            @RequestParam(required = false) String date,
            @RequestParam(required = false) String trade,
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        Page<OpsSysCalendar> pageEntity = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsSysCalendar> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StrUtil.isNotEmpty(date), OpsSysCalendar::getCalendarDate, date);
        calendarWrapper.eq(StrUtil.isNotEmpty(trade), OpsSysCalendar::getTrade, trade);
        calendarWrapper.eq(StrUtil.isNotEmpty(market),OpsSysCalendar::getMarket, market);
        calendarWrapper.orderByAsc(OpsSysCalendar::getCalendarDate);
        IPage<OpsSysCalendar> iPage = calendarService.page(pageEntity, calendarWrapper);
        return R.data(iPage);
    }

    @GetMapping("/list")
    public R<?> list(
            @RequestParam String date,
            @RequestParam(required = false, defaultValue = "") String trade,
            @RequestParam(required = false, defaultValue = CommonConstant.MARKET_A) String market
    ) {
        return R.data(getList(date,trade,market));
    }

    private List<OpsSysCalendar> getList(
            String date,
            String trade,
            String market
    ) {
        LambdaQueryWrapper<OpsSysCalendar> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StrUtil.isNotEmpty(date),OpsSysCalendar::getCalendarDate, date);
        calendarWrapper.eq(StrUtil.isNotBlank(trade),OpsSysCalendar::getTrade, trade);
        calendarWrapper.eq(StrUtil.isNotEmpty(market),OpsSysCalendar::getMarket, market);
        calendarWrapper.orderByAsc(OpsSysCalendar::getCalendarDate);
        List<OpsSysCalendar> opsSysCalendarList = calendarService.list(calendarWrapper);
        return opsSysCalendarList;
    }

    @PostMapping("/edit")
    public R<String> edit(@RequestBody OpsSysCalendarDTO dto) {
        calendarService.updateCalendar(dto);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 初始化日历
     */
    @GetMapping("/init")
    public R<?> init(
            @RequestParam String startYear,
            @RequestParam String endYear,
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market
    ) {
        calendarService.init(startYear, endYear,market);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @GetMapping("/existYear")
    public R<?> existYear() {
        List<OpsSysCalendar> years = calendarService.list(Wrappers.lambdaQuery(OpsSysCalendar.class).
                select(OpsSysCalendar::getYearVal).groupBy(OpsSysCalendar::getYearVal));
        if (years.isEmpty() || (years.size() == 1 && years.get(0) == null)) {
            return R.data(new ArrayList<>());
        }
        List<String> res = years.stream().filter(Objects::nonNull).map(OpsSysCalendar::getYearVal).collect(Collectors.toList());
        return R.data(res);
    }

    @GetMapping("/holiday")
    public R<Object> findHoliday(
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market
    ) {
        List<OpsSysCalendar> holiday = calendarService.
                list(Wrappers.lambdaQuery(OpsSysCalendar.class).
                        select(OpsSysCalendar::getCalendarDate)
                .eq(OpsSysCalendar::getHoliday, CommonConstant.HOLIDAY_Y)
                .eq(StrUtil.isNotBlank(market),OpsSysCalendar::getMarket, market)
                        .orderByAsc(OpsSysCalendar::getCalendarDate));
        if (!holiday.isEmpty()) {
            return R.data(holiday.stream().map(OpsSysCalendar::getCalendarDate).collect(Collectors.toList()));
        }
        return R.data(new ArrayList<String>());
    }

    /**
     * 批量保存
     */
    @PostMapping("/batchSave")
    public R<String> batchToTrade(
            @RequestBody List<OpsSysCalendar> opsSysCalendarList
    ) {
        for (OpsSysCalendar opsSysCalendar : opsSysCalendarList) {
            //处理休市日自动转换
            if(
                StrUtil.nullToEmpty(opsSysCalendar.getStopTradeDay()).equals(CommonConstant.STOP_TRADE_DAY_Y)
            ){
                opsSysCalendar.setTrade(CommonConstant.TRADE_N);
            }
            //处理交易日自动转换
            if(
                    StrUtil.nullToEmpty(opsSysCalendar.getTrade()).equals(CommonConstant.TRADE_Y)
            ){
                //交易日->非休市日
                opsSysCalendar.setStopTradeDay(CommonConstant.STOP_TRADE_DAY_N);
                //交易日->非半日市
                opsSysCalendar.setHalfDay(CommonConstant.HALF_DAY_N);
                //交易日->非节假日
                opsSysCalendar.setHoliday(CommonConstant.HOLIDAY_N);
            }
            calendarService.saveOrUpdateBatch(opsSysCalendarList);
        }
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 保存交易日信息
     */
    @PostMapping("/save")
    public R<String> save(
            @RequestBody OpsSysCalendar dto
    ) {
        List<OpsSysCalendar> dtoList = new ArrayList<>();
        dtoList.add(dto);
        calendarService.saveOrUpdate(dto);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 导出交易日信息
     */
    @GetMapping("/exportExcel")
    public void export(
            @RequestParam String date,
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market,
            HttpServletResponse response
    ) {
        try {
            List<OpsSysCalendar> opsSysCalendarList = getList(date,CommonConstant.TRADE_Y, market);
            List<String> colList = opsSysCalendarList.stream().map(OpsSysCalendar::getCalendarDate).collect(Collectors.toList());
            ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter (true);
            colList.add("交易日");
            writer.setColumnWidth(0, 20);
            for (String trade : colList) {
                writer.writeRow(Collections.singletonList(trade));
            }
            response.setContentType ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            String fileName = URLEncoder.encode ("交易日", StandardCharsets.UTF_8);
            response.setHeader ("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush (out, true);
            IoUtil.close (out);
            writer.close ();
        }catch (Exception ex){
            log.error("导出文件异常-原因如下：");
            log.error(ex.getMessage());
        }
    }

    /**
     * 导出交易日导入模板信息
     */
    @GetMapping("/exportExcelTemplate")
    public void exportExcelTemplate(
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market,
            HttpServletResponse response
    ) {
        try {
            List<String> colList = new ArrayList<>();
            ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter (true);
            writer.setColumnWidth(0, 20);
            colList.add("交易日");
            for (String trade : colList) {
                writer.writeRow(Collections.singletonList(trade));
            }
            response.setContentType ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            String fileName = URLEncoder.encode ("交易日", StandardCharsets.UTF_8);
            response.setHeader ("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush (out, true);
            IoUtil.close (out);
            writer.close ();
        }catch (Exception ex){
            log.error("导出模板文件异常-原因如下：");
            log.error(ex.getMessage());
        }
    }

    /**
     * 导入交易日信息
     */
    @PostMapping("/importExcel")
    public R<?> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false,defaultValue = CommonConstant.MARKET_A) String market
    ) {
        List<String > tradeList = new ArrayList<>();
        if (file.isEmpty()) {
            return R.fail("未选择文件");
        }
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            //获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row != null && row.getRowNum() >= 1) {
                    tradeList.add(getDefaultCellStringValue(row, 0));
                }
            }
            LambdaUpdateWrapper<OpsSysCalendar> updateWrapper =
                    Wrappers.lambdaUpdate();
            updateWrapper.in(OpsSysCalendar::getCalendarDate,tradeList);
            updateWrapper.set(OpsSysCalendar::getTrade,CommonConstant.TRADE_Y);
            calendarService.update(updateWrapper);
        } catch (Exception ex) {
            log.error("解析Excel文件时出错:原因如下:");
            ex.printStackTrace();
            return R.fail(MessageConstant.OPERATOR_FAIL);
        }
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    private String getDefaultCellStringValue(
            Row row,
            int i
    ){
        Cell cell = row.getCell(i);
        if (cell==null){
            return "";
        }
        return StrUtil.nullToDefault(cell.getStringCellValue(),"");
    }

}
