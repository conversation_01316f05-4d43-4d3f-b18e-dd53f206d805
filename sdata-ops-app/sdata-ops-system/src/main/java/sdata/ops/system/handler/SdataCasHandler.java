package sdata.ops.system.handler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sdata.ops.common.core.model.CasResult;
import sdata.ops.common.config.propertites.LoginConfigParams;

@Component("sdataCasHandler")
@RequiredArgsConstructor
public class SdataCasHandler implements CasHandler {

    private final LoginConfigParams loginConfig;

    @Override
    public CasResult verifyTicket(String ticket) {
        //todo 实现对应cas服务的ticket校验方法

        return null;
    }
}
