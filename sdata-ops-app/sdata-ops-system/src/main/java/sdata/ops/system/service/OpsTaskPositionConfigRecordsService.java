package sdata.ops.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.dto.PositionConfigDTO;
import sdata.ops.base.system.model.entity.OpsTaskPositionConfigRecords;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【OPS_TASK_POSITION_CONFIG_RECORDS(任务模板转派配置记录表)】的数据库操作Service
* @createDate 2025-01-20 15:12:54
*/
public interface OpsTaskPositionConfigRecordsService extends IService<OpsTaskPositionConfigRecords> {

    List<OpsTaskPositionConfigRecords> confList();

    boolean checkIsExisit(PositionConfigDTO configDTO);

    void confAdd(PositionConfigDTO configDTO);

    void confOverwrite(String id);


    void reset(String id, Integer permReset);
}
