package sdata.ops.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.OpsSysCalendar;
import sdata.ops.base.system.model.entity.OpsSysCalendarConfig;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.system.mapper.OpsSysCalendarConfigMapper;
import sdata.ops.system.mapper.OpsSysCalendarMapper;
import sdata.ops.system.service.OpsSysCalendarConfigService;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/09/02 17:07
 */
@Service
@RequiredArgsConstructor
public class OpsSysCalendarConfigServiceImpl extends ServiceImpl<OpsSysCalendarConfigMapper, OpsSysCalendarConfig> implements OpsSysCalendarConfigService {

    private final OpsSysCalendarConfigMapper opsSysCalendarConfigMapper;

    private final OpsSysCalendarMapper opsSysCalendarMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void customSaveList(List<OpsSysCalendarConfig> dtoList) {
        for (OpsSysCalendarConfig objNew : dtoList) {
            //其次添加/修改配置组信息
            if(StrUtil.isNotBlank(objNew.getId())){
                opsSysCalendarConfigMapper.updateById(objNew);
            }else {
                opsSysCalendarConfigMapper.insert(objNew);
            }
            //其次根据组信息,去更新对应的交易日信息,覆盖
            LambdaUpdateWrapper<OpsSysCalendar> updateNewWrapper = Wrappers.lambdaUpdate();
            updateNewWrapper.ge(OpsSysCalendar::getCalendarDate, objNew.getStartDate());
            updateNewWrapper.le(OpsSysCalendar::getCalendarDate, objNew.getStopDate());
            updateNewWrapper.eq(OpsSysCalendar::getMarket, objNew.getMarket());
            updateNewWrapper.set(OpsSysCalendar::getTrade, CommonConstant.TRADE_N);
            updateNewWrapper.set(OpsSysCalendar::getHoliday, CommonConstant.HOLIDAY_Y);
            updateNewWrapper.set(OpsSysCalendar::getRemark, objNew.getRemark());
            opsSysCalendarMapper.update(null,updateNewWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void customDeleteBatch(List<String> ids) {

        List<OpsSysCalendarConfig> opsSysCalendarConfigList = opsSysCalendarConfigMapper.selectBatchIds(ids);
        opsSysCalendarConfigMapper.deleteBatchIds(ids);
        for (OpsSysCalendarConfig opsSysCalendarConfig : opsSysCalendarConfigList) {
            //删除节假日对应的交易日配置后,自动设置对应的日期为非交易日
            LambdaUpdateWrapper<OpsSysCalendar> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.ge(OpsSysCalendar::getCalendarDate, opsSysCalendarConfig.getStartDate());
            updateWrapper.le(OpsSysCalendar::getCalendarDate, opsSysCalendarConfig.getStopDate());
            updateWrapper.eq(OpsSysCalendar::getMarket, opsSysCalendarConfig.getMarket());
            updateWrapper.eq(OpsSysCalendar::getRemark, opsSysCalendarConfig.getRemark());
            updateWrapper.set(OpsSysCalendar::getTrade, CommonConstant.TRADE_N);
            opsSysCalendarMapper.update(null,updateWrapper);
        }

    }

}
