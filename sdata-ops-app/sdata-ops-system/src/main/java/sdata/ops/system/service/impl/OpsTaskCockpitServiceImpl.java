package sdata.ops.system.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.common.core.util.QuarterDateUtils;
import sdata.ops.system.mapper.OpsTaskAttrBasicReplicaMapper;
import sdata.ops.system.mapper.OpsTaskCockpitMapper;
import sdata.ops.system.mapper.OpsTaskFundInfoMapper;
import sdata.ops.system.service.OpsSysOrgService;
import sdata.ops.system.service.OpsTaskCockpitService;
import sdata.ops.system.service.OpsTaskGenInfoService;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskCockpitServiceImpl implements OpsTaskCockpitService {

    private final OpsTaskCockpitMapper opsTaskCockpitMapper;

    private final OpsSysOrgService opsSysOrgService;

    private final OpsTaskGenInfoService taskGenInfoService;

    private final OpsTaskAttrBasicReplicaMapper opsTaskAttrBasicReplicaMapper;

    private final OpsTaskFundInfoMapper opsTaskFundInfoMapper;

    @Override
    public Map<String, Integer> getStatistics(String year, String month) {
        Map<String, Integer> map = new HashMap<>();
        map.put("total", opsTaskCockpitMapper.countByStatus(year, month, null));
        map.put("completeCount", opsTaskCockpitMapper.countByStatus(year, month, 3));
        return map;
    }

    @Override
    public List<Map<String, Object>> getDeptStatistics(String year, String month) {
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapper.eq(OpsSysOrg::getOrgType, 2);
        lambdaQueryWrapper.eq(OpsSysOrg::getStSign, 1);
        List<OpsSysOrg> opsSysOrgList = opsSysOrgService.list(lambdaQueryWrapper);
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        List<Map<String, Object>> list = opsTaskCockpitMapper.deptByStatus(year, month, null);
        List<Map<String, Object>> completeList = opsTaskCockpitMapper.deptByStatus(year, month, 3);
        List<Map<String, Object>> checkList = opsTaskCockpitMapper.deptInCheck(year, month);
        for (Map<String, Object> l : completeList) {
            Optional<Map<String, Object>> filter = checkList.stream().filter(e -> e.get("ownerOrgId").equals(l.get("ownerOrgId"))).findFirst();
            if (filter.isPresent()) {
                l.put("total", Long.valueOf(filter.get().get("total").toString()) + Long.valueOf(l.get("total").toString()));
            }
        }
        for (Map<String, Object> l : list) {
            Optional<Map<String, Object>> filter = checkList.stream().filter(e -> e.get("ownerOrgId").equals(l.get("ownerOrgId"))).findFirst();
            if (filter.isPresent()) {
                l.put("total", Long.valueOf(filter.get().get("total").toString()) + Long.valueOf(l.get("total").toString()));
            }
        }
        for (Map<String, Object> l : list) {
            Optional<Map<String, Object>> filter = completeList.stream().filter(e -> e.get("ownerOrgId").equals(l.get("ownerOrgId"))).findFirst();
            if (filter.isPresent()) {
                l.put("completeCount", filter.get().get("total"));
            } else {
                l.put("completeCount", 0);
            }
        }
        List<Map<String, Object>> fundList = opsTaskAttrBasicReplicaMapper.getAllFundCount(year, month);
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> l : list) {
            // 完成
            Optional<Map<String, Object>> completeFundFilter = fundList.stream().filter
                    (e -> e.get("ownerOrgId").equals(l.get("ownerOrgId")) && e.get("completeStatus") != null && e.get("completeStatus").equals("3"))
                    .findFirst();
            if (completeFundFilter.isPresent()) {
                l.put("completeCount", Long.valueOf(l.get("completeCount").toString()) + Long.valueOf(completeFundFilter.get().get("num").toString()));
                l.put("total", Long.valueOf(l.get("total").toString()) + Long.valueOf(completeFundFilter.get().get("num").toString()));
            }
            // 未完成
            Optional<Map<String, Object>> unCompleteFundFilter = fundList.stream().filter
                    (e -> e.get("ownerOrgId").equals(l.get("ownerOrgId")) && e.get("completeStatus") != null && e.get("completeStatus").equals("1"))
                    .findFirst();
            if (unCompleteFundFilter.isPresent()) {
                l.put("total", Long.valueOf(l.get("total").toString()) + Long.valueOf(unCompleteFundFilter.get().get("num").toString()));
            }
            if (!opsSysOrgChildList.stream().anyMatch(e -> e.getId().equals(Long.valueOf(l.get("ownerOrgId").toString()))))
                continue;
            // 分组逻辑
            Optional<Map<String, Object>> filter = result.stream().filter
                    (e -> Arrays.stream(opsSysOrgChildList.stream().filter(f -> f.getId().equals(Long.valueOf(l.get("ownerOrgId").toString())))
                            .findFirst().get().getAncestors().split(",")).anyMatch(a -> a.equals(e.get("ownerOrgId").toString())))
                    .findFirst();
            if (filter.isPresent()) {
                filter.get().put("total", Long.valueOf(filter.get().get("total").toString()) + Long.valueOf(l.get("total").toString()));
                filter.get().put("completeCount", Long.valueOf(filter.get().get("completeCount").toString()) + Long.valueOf(l.get("completeCount").toString()));
                Object lessDeptObj = filter.get().get("lessDept");
                System.out.println(lessDeptObj.getClass().getName());
                List<Map<String, Object>> lessDept = (List<Map<String, Object>>) filter.get().get("lessDept");
                lessDept.add(l);
                filter.get().put("lessDept", lessDept);
            } else {
                Map<String, Object> mapItem = new HashMap<>();
                List<Map<String, Object>> lessDept = new ArrayList<>();
                lessDept.add(l);
                mapItem.put("lessDept", lessDept);
                mapItem.put("total", l.get("total"));
                mapItem.put("completeCount", l.get("completeCount"));
                String ancestors = opsSysOrgChildList.stream().filter(f -> f.getId().equals(Long.valueOf(l.get("ownerOrgId").toString()))).findFirst().get().getAncestors();
                if (!opsSysOrgList.stream().filter(e -> Arrays.stream(ancestors.split(",")).anyMatch(a -> a.equals(e.getId().toString()))).findFirst().isPresent())
                    continue;
                OpsSysOrg opsSysOrg = opsSysOrgList.stream().filter(e -> Arrays.stream(ancestors.split(",")).anyMatch(a -> a.equals(e.getId().toString()))).findFirst().get();
                mapItem.put("orgName", opsSysOrg.getOrgName());
                mapItem.put("leader", opsSysOrg.getLeader());
                mapItem.put("ownerOrgId", opsSysOrg.getId().toString());
                result.add(mapItem);
            }
        }
        if (result.size() < opsSysOrgList.size()) {
            for (OpsSysOrg opsSysOrg : opsSysOrgList) {
                if (result.stream().noneMatch(e -> e.get("ownerOrgId").equals(opsSysOrg.getId().toString()))) {
                    Map<String, Object> mapItem = new HashMap<>();
                    mapItem.put("orgName", opsSysOrg.getOrgName());
                    mapItem.put("leader", opsSysOrg.getLeader());
                    mapItem.put("ownerOrgId", opsSysOrg.getId().toString());
                    List<Map<String, Object>> lessDept = new ArrayList<>();
                    mapItem.put("lessDept", lessDept);
                    mapItem.put("total", 0);
                    mapItem.put("completeCount", 0);
                    result.add(mapItem);
                }
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getMonthStatistics(String year, String month, String deptId) {
        List<Map<String, Object>> list;
        List<Map<String, Object>> checkList;
        List<Map<String, Object>> fundList;
        String startDate = LocalDate.of(Integer.valueOf(year), Integer.valueOf(month), 1).minusMonths(11).toString();
        String endDate = LocalDate.of(Integer.valueOf(year), Integer.valueOf(month), 1).minusMonths(-1).minusDays(1).toString();
        if (StringUtils.hasText(deptId)) {
            LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper();
            lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
            lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
            List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
            opsSysOrgChildList = opsSysOrgChildList.stream().filter(e -> Arrays.stream(e.getAncestors().split(",")).anyMatch(a -> a.equals(deptId))).collect(Collectors.toList());
            List<Long> ids = opsSysOrgChildList.stream().map(e -> e.getId()).collect(Collectors.toList());
            list = opsTaskCockpitMapper.countByMonth(startDate, endDate, deptId, ids);
            checkList = opsTaskCockpitMapper.checkCountByMonth(startDate, endDate, deptId, ids);
            fundList = opsTaskFundInfoMapper.getMonthFund(startDate, endDate, ids);
        } else {
            list = opsTaskCockpitMapper.countByMonth(startDate, endDate, deptId, null);
            checkList = opsTaskCockpitMapper.checkCountByMonth(startDate, endDate, deptId, null);
            fundList = opsTaskFundInfoMapper.getMonthFund(startDate, endDate, null);
        }
        for (Map<String, Object> l : list) {
            Optional<Map<String, Object>> checkFilter = checkList.stream().filter(e -> l.get("month").equals(e.get("month"))).findFirst();
            if (checkFilter.isPresent()) {
                l.put("total", Long.valueOf(l.get("total").toString()) + Long.valueOf(checkFilter.get().get("total").toString()));
            }
            Optional<Map<String, Object>> filter = fundList.stream().filter(e -> l.get("month").equals(e.get("month"))).findFirst();
            if (filter.isPresent()) {
                l.put("total", Long.valueOf(l.get("total").toString()) + Long.valueOf(filter.get().get("total").toString()));
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getDelayMonthStatistics(String year, String month, String deptId) {
        List<Map<String, Object>> list;
        List<Map<String, Object>> fundList;
        String startDate = LocalDate.of(Integer.valueOf(year), Integer.valueOf(month), 1).minusMonths(11).toString();
        String endDate = LocalDate.of(Integer.valueOf(year), Integer.valueOf(month), 1).minusMonths(-1).minusDays(1).toString();
        if (StringUtils.hasText(deptId)) {
            LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper();
            lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
            lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
            List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
            opsSysOrgChildList = opsSysOrgChildList.stream().filter(e -> Arrays.stream(e.getAncestors().split(",")).anyMatch(a -> a.equals(deptId))).collect(Collectors.toList());
            List<Long> ids = opsSysOrgChildList.stream().map(e -> e.getId()).collect(Collectors.toList());
            list = opsTaskCockpitMapper.delayCountByMonth(startDate, endDate, deptId, ids);
            fundList = opsTaskFundInfoMapper.getDelayMonthFund(startDate, endDate, ids);
        } else {
            list = opsTaskCockpitMapper.delayCountByMonth(startDate, endDate, deptId, null);
            fundList = opsTaskFundInfoMapper.getDelayMonthFund(startDate, endDate, null);
        }
        for (Map<String, Object> l : list) {
            Optional<Map<String, Object>> filter = fundList.stream().filter(e -> l.get("month").equals(e.get("month"))).findFirst();
            if (filter.isPresent()) {
                l.put("total", Long.valueOf(l.get("total").toString()) + Long.valueOf(filter.get().get("total").toString()));
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getLessDeptStatistics(String year, String month, String deptId) {
        List<Map<String, Object>> list;
        List<Map<String, Object>> checkList;
        List<Map<String, Object>> fundList;
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper<>();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        opsSysOrgChildList = opsSysOrgChildList.stream().filter(e -> Arrays.asList(e.getAncestors().split(",")).contains(deptId)).collect(Collectors.toList());
        List<Long> ids = opsSysOrgChildList.stream().map(OpsSysOrg::getId).collect(Collectors.toList());
        list = opsTaskCockpitMapper.lessDept(year, month, ids);
        checkList = opsTaskCockpitMapper.checkLessDept(year, month, ids);
        fundList = opsTaskFundInfoMapper.getDeptFund(year, month, ids);
        for (Map<String, Object> l : list) {
            Optional<Map<String, Object>> checkFilter = checkList.stream().filter(e -> l.get("ownerOrgId").equals(e.get("ownerOrgId"))).findFirst();
            if (checkFilter.isPresent()) {
                l.put("total", Long.parseLong(l.get("total").toString()) + Long.parseLong(checkFilter.get().get("total").toString()));
            }
            Optional<Map<String, Object>> filter = fundList.stream().filter(e -> l.get("ownerOrgId").equals(e.get("ownerOrgId"))).findFirst();
            if (filter.isPresent()) {
                l.put("total", Long.parseLong(l.get("total").toString()) + Long.parseLong(filter.get().get("total").toString()));
            }
        }
        return list;
    }

    @Override
    public Map<String, Object> getComprehensiveDept(String year, String month, String deptId) {
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        Map<String, String> comAllType3 = new HashMap<>();
        //取部门下所有岗位
        List<String> orgIds = new ArrayList<>();
        for (OpsSysOrg org : opsSysOrgChildList) {
            if (StringUtils.hasText(org.getAncestors())) {
                if (List.of(org.getAncestors().split(",")).contains(deptId)) {
                    comAllType3.put(String.valueOf(org.getId()), org.getOrgName());
                    orgIds.add(org.getId().toString());
                }
            }
        }
        YearMonth yearMonth = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        //获取该月的第一天
        Date firstDay = Date.from(yearMonth.atDay(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        //获取该月的最后一天
        Date lastDay = Date.from(yearMonth.atEndOfMonth().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        //执行查询(废弃)
//        List<Map<String, Object>> queryResult = opsTaskCockpitMapper.getComprehensiveDept(orgIds, firstDay, lastDay);
//        for (Map<String, Object> map : queryResult) {
//            map.put("orgName", comAllType3.get(map.get("orgId").toString()));
//        }
        //update-2024/10/21- 按照标签分组统计
        LambdaQueryWrapper<OpsTaskGenInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(OpsTaskGenInfo::getTaskStartTime, firstDay);
        wrapper.le(OpsTaskGenInfo::getTaskEndTime, lastDay);
        wrapper.eq(OpsTaskGenInfo::getWorkAmountFlag, 1);
        wrapper.eq(OpsTaskGenInfo::getTaskCompleteStatus, 3);
        wrapper.in(OpsTaskGenInfo::getOwnerOrgId, orgIds).last(" and TASK_TAGS is not null");
        wrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskTags, OpsTaskGenInfo::getWorkAmount, OpsTaskGenInfo::getOwnerOrgId);
        //查询综合部已完成数据
        List<OpsTaskGenInfo> datas = taskGenInfoService.list(wrapper);
        List<OpsTaskGenInfo> list1 = new ArrayList<>();
        List<OpsTaskGenInfo> list2 = new ArrayList<>();
        List<OpsTaskGenInfo> list3 = new ArrayList<>();
        for (OpsTaskGenInfo info : datas) {
            if (JSONUtil.parseArray(info.getTaskTags()).isEmpty()) {
                continue;
            }
            info.setTaskType(JSONUtil.parseArray(info.getTaskTags()).getJSONObject(0).getStr("name"));
            if (comAllType3.get(info.getOwnerOrgId()).equals("客户数据岗")) {
                list1.add(info);
            } else if (comAllType3.get(info.getOwnerOrgId()).equals("产品数据岗")) {
                list2.add(info);
            } else {
                list3.add(info);
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("客户数据组", formatComprehensiveDept(list1));
        result.put("产品数据组", formatComprehensiveDept(list2));
        result.put("合规风控行政权限组", formatComprehensiveDept(list3));
        return result;
    }

    @Override
    public Map<String, Object> getComprehensiveDeptV1(String year, String month, String deptId) {
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        Map<String, String> comAllType3 = new HashMap<>();
        //取部门下所有岗位
        List<String> orgIds = new ArrayList<>();
        for (OpsSysOrg org : opsSysOrgChildList) {
            if (StringUtils.hasText(org.getAncestors())) {
                if (List.of(org.getAncestors().split(",")).contains(deptId)) {
                    comAllType3.put(String.valueOf(org.getId()), org.getOrgName());
                    orgIds.add(org.getId().toString());
                }
            }
        }
        YearMonth yearMonth = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        //获取该月的第一天
        Date firstDay = Date.from(yearMonth.atDay(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        //获取该月的最后一天
        Date lastDay = Date.from(yearMonth.atEndOfMonth().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        //执行查询(废弃)
//        List<Map<String, Object>> queryResult = opsTaskCockpitMapper.getComprehensiveDept(orgIds, firstDay, lastDay);
//        for (Map<String, Object> map : queryResult) {
//            map.put("orgName", comAllType3.get(map.get("orgId").toString()));
//        }
        //update-2024/10/21- 按照标签分组统计
        LambdaQueryWrapper<OpsTaskGenInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(OpsTaskGenInfo::getTaskStartTime, firstDay);
        wrapper.le(OpsTaskGenInfo::getTaskEndTime, lastDay);
        wrapper.eq(OpsTaskGenInfo::getWorkAmountFlag, 1);
        wrapper.eq(OpsTaskGenInfo::getTaskCompleteStatus, 3);
        wrapper.in(OpsTaskGenInfo::getOwnerOrgId, orgIds).last(" and TASK_TAGS is not null");
        wrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskTags,
                OpsTaskGenInfo::getWorkAmount, OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskOwnerVal
        );
        //查询综合部已完成数据
        List<OpsTaskGenInfo> datas = taskGenInfoService.list(wrapper);
        List<OpsTaskGenInfo> list1 = new ArrayList<>();
        List<OpsTaskGenInfo> list2 = new ArrayList<>();
        List<OpsTaskGenInfo> list3 = new ArrayList<>();
        for (OpsTaskGenInfo info : datas) {
            if (JSONUtil.parseArray(info.getTaskTags()).isEmpty()) {
                continue;
            }
            info.setTaskType(JSONUtil.parseArray(info.getTaskTags()).getJSONObject(0).getStr("name"));
            if (comAllType3.get(info.getOwnerOrgId()).equals("客户数据岗")) {
                list1.add(info);
            } else if (comAllType3.get(info.getOwnerOrgId()).equals("产品数据岗")) {
                list2.add(info);
            } else {
                list3.add(info);
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("客户数据组", formatComprehensiveDeptV1(list1));
        result.put("产品数据组", formatComprehensiveDeptV1(list2));
        result.put("合规风控行政权限组", formatComprehensiveDeptV1(list3));
        return result;
    }

    /**
     * 交易部查询明细接口
     *
     * @param year    年
     * @param month   月
     * @param deptId  部门id(固定交易部)
     * @param quarter 第几季度
     * @return map
     */
    @Override
    public Map<String, Object> getTradeDeptDetail(String year, String month, String deptId, String quarter) {
        Map<String, Object> res = new HashMap<>();
        //先判定是月 ，还是季度
        Tuple2<Date, Date> dateRange = genDateRange(year, month, quarter);
        //查询dept下所有岗位任务，deleted=0 开始和结束时间 有计数的 进行group_By 分组查询
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper<>();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        Map<String, String> comAllType3 = new HashMap<>();
        //取部门下所有岗位
        List<String> orgIds = new ArrayList<>();
        for (OpsSysOrg org : opsSysOrgChildList) {
            if (StringUtils.hasText(org.getAncestors())) {
                if (List.of(org.getAncestors().split(",")).contains(deptId)) {
                    comAllType3.put(String.valueOf(org.getId()), org.getOrgName());
                    orgIds.add(org.getId().toString());
                }
            }
        }
        //查询
        //查询有备注的列表
        LambdaQueryWrapper<OpsTaskGenInfo> genInfosMark = new LambdaQueryWrapper<>();
        genInfosMark.le(OpsTaskGenInfo::getTaskEndTime, dateRange.getT2());
        genInfosMark.ge(OpsTaskGenInfo::getTaskStartTime, dateRange.getT1());
        genInfosMark.in(OpsTaskGenInfo::getOwnerOrgId, orgIds);
        genInfosMark.eq(OpsTaskGenInfo::getDeleted, 0);
        genInfosMark.last(" and task_complete_desc is not null  and task_complete_desc <> ' '");
        genInfosMark.select(OpsTaskGenInfo::getId,
                OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskCompleteDesc,
                OpsTaskGenInfo::getTaskStartTime, OpsTaskGenInfo::getTaskEndTime);
        List<OpsTaskGenInfo> allDesc = taskGenInfoService.list(genInfosMark);
        for (OpsTaskGenInfo info : allDesc) {
            info.setTaskOwnerVal(comAllType3.get(info.getOwnerOrgId()));
        }
        //进行分组 合并
        //查询有计数
        LambdaQueryWrapper<OpsTaskGenInfo> genInfos = new LambdaQueryWrapper<>();
        genInfos.le(OpsTaskGenInfo::getTaskEndTime, dateRange.getT2());
        genInfos.ge(OpsTaskGenInfo::getTaskStartTime, dateRange.getT1());
        genInfos.in(OpsTaskGenInfo::getOwnerOrgId, orgIds);
        genInfos.eq(OpsTaskGenInfo::getDeleted, 0);
        genInfos.isNotNull(OpsTaskGenInfo::getWorkAmount);
        genInfos.ne(OpsTaskGenInfo::getWorkAmount, 0);
        genInfos.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getWorkAmount, OpsTaskGenInfo::getTaskStartTime, OpsTaskGenInfo::getTaskEndTime);
        List<OpsTaskGenInfo> all = taskGenInfoService.list(genInfos);
        List<OpsTaskGenInfo> workAmount = groupAndSumTasks(all, comAllType3);
        res.put("amount", workAmount);
        res.put("descVal", allDesc);
        return res;
    }

    /**
     * 导出接口
     *
     * @param year
     * @param month
     * @param deptId
     * @param quarter
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> getTradeDeptDetailExport(String year, String month, String deptId, String quarter) {
        //先判定是月 ，还是季度
        Tuple2<Date, Date> dateRange = genDateRange(year, month, quarter);
        //查询dept下所有岗位任务，deleted=0 开始和结束时间 有计数的 进行group_By 分组查询
        LambdaQueryWrapper<OpsSysOrg> lambdaQueryWrapperChild = new LambdaQueryWrapper<>();
        lambdaQueryWrapperChild.eq(OpsSysOrg::getDeleted, 0);
        lambdaQueryWrapperChild.eq(OpsSysOrg::getOrgType, 3);
        List<OpsSysOrg> opsSysOrgChildList = opsSysOrgService.list(lambdaQueryWrapperChild);
        Map<String, String> comAllType3 = new HashMap<>();
        //取部门下所有岗位
        List<String> orgIds = new ArrayList<>();
        for (OpsSysOrg org : opsSysOrgChildList) {
            if (StringUtils.hasText(org.getAncestors())) {
                if (List.of(org.getAncestors().split(",")).contains(deptId)) {
                    comAllType3.put(String.valueOf(org.getId()), org.getOrgName());
                    orgIds.add(org.getId().toString());
                }
            }
        }
        //进行分组 合并
        //查询有计数
        LambdaQueryWrapper<OpsTaskGenInfo> genInfos = new LambdaQueryWrapper<>();
        genInfos.le(OpsTaskGenInfo::getTaskEndTime, dateRange.getT2());
        genInfos.ge(OpsTaskGenInfo::getTaskStartTime, dateRange.getT1());
        genInfos.in(OpsTaskGenInfo::getOwnerOrgId, orgIds);
        genInfos.eq(OpsTaskGenInfo::getDeleted, 0);
        genInfos.isNotNull(OpsTaskGenInfo::getWorkAmount);
        genInfos.ne(OpsTaskGenInfo::getWorkAmount, 0);
        genInfos.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getOwnerOrgId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getWorkAmount, OpsTaskGenInfo::getTaskStartTime, OpsTaskGenInfo::getTaskEndTime);
        List<OpsTaskGenInfo> all = taskGenInfoService.list(genInfos);
        List<OpsTaskGenInfo> workAmount = groupAndSumTasks(all, comAllType3);
        return workAmount;
    }


    public static List<OpsTaskGenInfo> groupAndSumTasks(List<OpsTaskGenInfo> tasks, Map<String, String> comAllType3) {
        // 按照 ownerOrgId 分组
        Map<String, List<OpsTaskGenInfo>> groupedByOwnerOrgId = tasks.stream()
                .collect(Collectors.groupingBy(OpsTaskGenInfo::getOwnerOrgId));

        // 处理每个分组
        List<OpsTaskGenInfo> result = new ArrayList<>();
        for (Map.Entry<String, List<OpsTaskGenInfo>> entry : groupedByOwnerOrgId.entrySet()) {
            String ownerOrgId = entry.getKey();
            List<OpsTaskGenInfo> groupTasks = entry.getValue();
            for (OpsTaskGenInfo groupTask : groupTasks) {
                groupTask.setTaskOwnerVal(comAllType3.get(ownerOrgId));
            }
            // 计算分组的工作量总和
            double sum = groupTasks.stream()
                    .mapToDouble(OpsTaskGenInfo::getWorkAmount)
                    .sum();

            // 获取任意一个任务的 taskName（假设同一个分组内的任务名称相同）
            String taskName = groupTasks.isEmpty() ? null : groupTasks.get(0).getTaskName();

            // 创建 GroupedTaskInfo 对象
            OpsTaskGenInfo groupedTaskInfo = new OpsTaskGenInfo();
            groupedTaskInfo.setTaskName(taskName);
            groupedTaskInfo.setWorkAmount((int) sum);
            groupedTaskInfo.setTaskOwnerVal(comAllType3.get(ownerOrgId));
            groupedTaskInfo.setChildren(groupTasks);
            // 添加到结果列表
            result.add(groupedTaskInfo);
        }
        return result;
    }

    private Tuple2<Date, Date> genDateRange(String year, String month, String quarter) {
        if (month != null) {
            YearMonth yearMonth = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
            //获取该月的第一天
            Date firstDay = Date.from(yearMonth.atDay(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            //获取该月的最后一天
            Date lastDay = Date.from(yearMonth.atEndOfMonth().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
            return Tuples.of(firstDay, lastDay);
        }
        if (quarter != null) {
            return QuarterDateUtils.getQuarterStartEnd(Integer.parseInt(year), Integer.parseInt(quarter));
        }
        throw new RuntimeException("时间范围计算失败");
    }

    private Map<String, Map<String, Object>> formatComprehensiveDept(List<OpsTaskGenInfo> list) {
        return list.stream().collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskType, Collectors.collectingAndThen(Collectors.toList(), ls -> {
            int total = ls.stream().mapToInt(OpsTaskGenInfo::getWorkAmount).sum();
            Map<String, Object> res = new HashMap<>();
            res.put("total", total);
            res.put("data", ls);
            return res;
        })));
    }

    /**
     * 数据格式化
     *
     * @param list
     * @return
     */
    private Map<Object, Object> formatComprehensiveDeptV1(List<OpsTaskGenInfo> list) {
        return list.stream().collect(Collectors.groupingBy(OpsTaskGenInfo::getTaskType))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        i -> i.getKey(),
                        i -> (i.getValue().stream()
                                .collect(Collectors.groupingBy((task -> task.getTaskName() + "-" + task.getTaskOwnerVal())))
                                .entrySet().stream()
                                .map(gen -> gen.getValue().get(0))
                                .collect(Collectors.toList())
                        )
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        i -> i.getKey(),
                        i -> {
                            Map<String, Object> tmp_m = new HashMap<>();
                            tmp_m.put("total", i.getValue().stream().mapToInt(OpsTaskGenInfo::getWorkAmount).sum());
                            tmp_m.put("data", i.getValue());
                            return tmp_m;
                        }));
    }
}
