package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsSysDataPermAuthSubject;
import sdata.ops.system.mapper.OpsSysDataPermAuthSubjectMapper;
import sdata.ops.system.service.OpsSysDataPermAuthSubjectService;

/**
* <AUTHOR>
* @description 针对表【ops_sys_data_perm_auth_subject(授权主体表)】的数据库操作Service实现
* @createDate 2025-08-25 17:47:36
*/
@Service
public class OpsSysDataPermAuthSubjectServiceImpl extends ServiceImpl<OpsSysDataPermAuthSubjectMapper, OpsSysDataPermAuthSubject>
    implements OpsSysDataPermAuthSubjectService {

}




