package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasic;
import sdata.ops.system.mapper.OpsTaskAttrBasicMapper;
import sdata.ops.system.service.OpsTaskAttrBasicService;

/**
* <AUTHOR>
* @description 针对表【ops_task_attr_basic】的数据库操作Service实现
* @createDate 2024-07-03 11:21:10
*/
@Service
public class OpsTaskAttrBasicServiceImpl extends ServiceImpl<OpsTaskAttrBasicMapper, OpsTaskAttrBasic>
    implements OpsTaskAttrBasicService {

}




