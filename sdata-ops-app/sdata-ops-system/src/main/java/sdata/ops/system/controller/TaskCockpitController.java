package sdata.ops.system.controller;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsTaskCockpitService;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/taskCockpit")
@RequiredArgsConstructor
public class TaskCockpitController {

    private final OpsTaskCockpitService opsTaskCockpitService;

    /***
     *
     * @param year
     * @param month
     * @return
     */
    @GetMapping("/getStatistics")
    public R<Object> getStatistics(@RequestParam String year, @RequestParam String month) {

        return R.data(opsTaskCockpitService.getStatistics(year, month));
    }

    /***
     *
     * @param year
     * @param month
     * @return
     */
    @GetMapping("/getDeptStatistics")
    public R<Object> getDeptStatistics(@RequestParam String year, @RequestParam String month) {

        return R.data(opsTaskCockpitService.getDeptStatistics(year, month));
    }


    /***
     *
     * @param year
     * @param deptId
     * @param month
     * @return
     */
    @GetMapping("/getMonthStatistics")
    public R<Object> getMonthStatistics(@RequestParam String year, @RequestParam String month, @RequestParam String deptId) {

        return R.data(opsTaskCockpitService.getMonthStatistics(year, month, deptId));
    }

    /***
     *
     * @param year
     * @param deptId
     * @param month
     * @return
     */
    @GetMapping("/getDelayMonthStatistics")
    public R<Object> getDelayMonthStatistics(@RequestParam String year, @RequestParam String month, @RequestParam String deptId) {

        return R.data(opsTaskCockpitService.getDelayMonthStatistics(year, month, deptId));
    }

    /***
     *
     * @param year
     * @param month
     * @param deptId
     * @return
     */
    @GetMapping("/getLessDeptStatistics")
    public R<Object> getLessDeptStatistics(@RequestParam String year, @RequestParam String month, @RequestParam String deptId) {

        return R.data(opsTaskCockpitService.getLessDeptStatistics(year, month, deptId));
    }

    /***
     *
     * @param year
     * @param month
     * @param deptId
     * @return
     */
    @GetMapping("/getComprehensiveDept")
    public R<Object> getComprehensiveDept(@RequestParam String year, @RequestParam String month, @RequestParam String deptId) {
        return R.data(opsTaskCockpitService.getComprehensiveDept(year, month, deptId));
    }

    @GetMapping("/getComprehensiveDept/v1")
    public R<Object> getComprehensiveDeptV1(@RequestParam String year, @RequestParam String month, @RequestParam String deptId) {
        return R.data(opsTaskCockpitService.getComprehensiveDeptV1(year, month, deptId));
    }

    /***
     *
     * @param year  年
     * @param month 月
     * @param quarter 季度
     * @param deptId 部门id (交易部门id)
     * @return 查询有备注的任务 和 有计数的任务  合计(两个列表)
     */
    @GetMapping("/getTradeDeptDetail")
    public R<Object> getTradeDept(@RequestParam("year") String year, @RequestParam(value = "month", required = false) String month, @RequestParam String deptId, @RequestParam(value = "quarter", required = false) String quarter) {
        return R.data(opsTaskCockpitService.getTradeDeptDetail(year, month, deptId, quarter));
    }

    /**
     * 统计结果导出接口
     *
     * @param year
     * @param month
     * @param deptId
     * @param quarter
     * @param response
     */
    @GetMapping("/getTradeDeptDetailExport")
    public void getTradeDeptDetailExport(@RequestParam("year") String year,
                                         @RequestParam(value = "month", required = false) String month,
                                         @RequestParam String deptId,
                                         @RequestParam(value = "quarter", required = false) String quarter,
                                         HttpServletResponse response) {
        try {
            List<OpsTaskGenInfo> workAmount = opsTaskCockpitService.getTradeDeptDetailExport(year, month, deptId, quarter);
            if (CollUtil.isEmpty(workAmount)) {
                return;
            }
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("sheet1");
            Row headerRow = sheet.createRow(0);
            String[] headers = {"岗位名称", "业务名称", "计数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            for (int i = 0; i < workAmount.size(); i++) {
                OpsTaskGenInfo gen = workAmount.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(gen.getTaskOwnerVal());
                row.createCell(1).setCellValue(gen.getTaskName());
                row.createCell(2).setCellValue(gen.getWorkAmount());
            }
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(new String("统计结果".getBytes(), "UTF-8"), "UTF-8") + ".xlsx");
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
