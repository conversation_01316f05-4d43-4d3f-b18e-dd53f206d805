package sdata.ops.system.service.impl;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.OpsSysMenu;
import sdata.ops.base.system.model.entity.OpsSysRole;
import sdata.ops.base.system.model.vo.MetaVo;
import sdata.ops.base.system.model.vo.RouterVo;
import sdata.ops.base.system.model.vo.TreeSelect;
import sdata.ops.common.api.Constants;
import sdata.ops.common.api.UserConstants;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsSysRoleMapper;
import sdata.ops.system.mapper.OpsSysRoleMenuMapper;
import sdata.ops.system.service.OpsSysMenuService;
import sdata.ops.system.mapper.OpsSysMenuMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_menu(菜单权限表)】的数据库操作Service实现
 * @createDate 2024-06-03 19:48:02
 */
@Service
@RequiredArgsConstructor
public class OpsSysMenuServiceImpl extends ServiceImpl<OpsSysMenuMapper, OpsSysMenu>
        implements OpsSysMenuService {


    private final OpsSysMenuMapper menuMapper;

    private final OpsSysRoleMapper roleMapper;

    private final OpsSysRoleMenuMapper opsSysRoleMenuMapper;


    @Override
    public List<OpsSysMenu> selectMenuList(String userId) {
        return selectMenuList(new OpsSysMenu(), userId);
    }

    @Override
    public List<OpsSysMenu> selectMenuList(OpsSysMenu menu, String userId) {


        List<OpsSysMenu> menuList = null;
        // 管理员显示所有菜单信息
        if (SecureUtil.isAdmin(userId)) {
            menuList = menuMapper.selectMenuList(menu);
        } else {
            menu.getParams().put("userId", userId);
            menuList = menuMapper.selectMenuListByUserId(menu);
        }
        return menuList;
    }


    @Override
    public List<OpsSysMenu> selectMenuListNoAuth(OpsSysMenu menu) {
        return menuMapper.selectMenuList(menu);
    }

    @Override
    public Set<String> selectMenuPermsByUserId(String userId) {
        List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public Set<String> selectMenuPermsByRoleId(String roleId) {
        List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public List<OpsSysMenu> selectMenuTreeByUserId(String userId) {
        List<OpsSysMenu> menus = null;
        if (SecureUtil.isAdmin(userId)) {
            menus = menuMapper.selectMenuTreeAll();
        } else {
            menus = menuMapper.selectMenuTreeByUserId(userId);
        }
        return getChildPerms(menus, 0);
    }

    @Override
    public List<Long> selectMenuListByRoleId(String roleId) {
        OpsSysRole role = roleMapper.selectRoleById(Long.valueOf(roleId));
        return menuMapper.selectMenuListByRoleId(Long.valueOf(roleId), role.getMenuCheckStrictly());
    }

    @Override
    public List<RouterVo> buildMenus(List<OpsSysMenu> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (OpsSysMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQuery());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + ""), menu.getPath()));
            List<OpsSysMenu> cMenus = menu.getChildren();
            if (cMenus.isEmpty() && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + ""), menu.getPath()));
                children.setQuery(menu.getQuery());
                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (menu.getParentId().intValue() == 0 && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    @Override
    public List<OpsSysMenu> buildMenuTree(List<OpsSysMenu> menus) {
        List<OpsSysMenu> returnList = new ArrayList<>();
        if (menus != null && !menus.isEmpty()) {
            menus.sort(Comparator.comparingInt(OpsSysMenu::getOrderNum));
        }
        List<Long> tempList = menus.stream().map(OpsSysMenu::getId).collect(Collectors.toList());
        for (OpsSysMenu menu : menus) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<OpsSysMenu> menus) {
        List<OpsSysMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public OpsSysMenu selectMenuById(Long menuId) {
        return menuMapper.selectMenuById(menuId);
    }

    @Override
    public boolean hasChildByMenuId(Long menuId) {
        int result = menuMapper.hasChildByMenuId(menuId);
        return result > 0;
    }

    @Override
    public boolean checkMenuExistRole(Long menuId) {
        int result = opsSysRoleMenuMapper.checkMenuExistRole(menuId);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMenu(OpsSysMenu menu) {
        return menuMapper.insert(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMenu(OpsSysMenu menu) {
        return menuMapper.updateById(menu);
    }

    @Override
    public int deleteMenuById(Long menuId) {
        return menuMapper.deleteMenuById(menuId);
    }

    @Override
    public boolean checkMenuNameUnique(OpsSysMenu menu) {
        long menuId = menu.getId() == null ? -1L : menu.getId();
        OpsSysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
        if (Objects.nonNull(info) && info.getId() != menuId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void updateMenuVisible(String id, Integer status) {
        menuMapper.updateMenuVisible(id, status);
    }

    @Override
    public void updateMenuStatus(String id, Integer status) {
        menuMapper.updateMenuStatus(id, status);
    }

    @Override
    public List<OpsSysMenu> selectMenuListByAuth(OpsSysMenu menu, String userId) {
        List<OpsSysMenu> menus;
        if (SecureUtil.isAdmin(userId)) {
            menus = menuMapper.selectMenuList(menu);
        } else {
            //先查配置权限菜单
            List<OpsSysMenu> allMenus = menuMapper.selectMenuList(menu);
            menu.getParams().put("userId", userId);
            menus = menuMapper.selectMenuListByUserId(menu);
            //如果不为空则查询id中是否有存在父级的
            if (menus != null && !menus.isEmpty()) {
                List<OpsSysMenu> parentMenus = new ArrayList<>();
                for (OpsSysMenu sysMenu : menus) {
                    if (sysMenu.getParentId() != 0L) {
                        deepFindParent(sysMenu.getParentId(), allMenus, parentMenus);
                    }
                }
                menus.addAll(parentMenus);
            }
        }
        return new ArrayList<>(menus.stream().collect(Collectors.toMap(OpsSysMenu::getId, i -> i, (n1, n2) -> n1)).values());
    }

    private void deepFindParent(Long parentId, List<OpsSysMenu> allMenus, List<OpsSysMenu> parentMenus) {
        for (OpsSysMenu allMenu : allMenus) {
            if (Objects.equals(allMenu.getId(), parentId)) {
                parentMenus.add(allMenu);
                if (allMenu.getParentId() != 0L) {
                    deepFindParent(allMenu.getParentId(), allMenus, parentMenus);
                }
                break;
            }
        }
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<OpsSysMenu> getChildPerms(List<OpsSysMenu> list, int parentId) {
        List<OpsSysMenu> returnList = new ArrayList<OpsSysMenu>();
        for (OpsSysMenu t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list 分类表
     * @param t    子节点
     */
    private void recursionFn(List<OpsSysMenu> list, OpsSysMenu t) {
        // 得到子节点列表
        List<OpsSysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (OpsSysMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<OpsSysMenu> list, OpsSysMenu t) {
        return !getChildList(list, t).isEmpty();
    }

    /**
     * 得到子节点列表
     */
    private List<OpsSysMenu> getChildList(List<OpsSysMenu> list, OpsSysMenu t) {
        List<OpsSysMenu> tlist = new ArrayList<OpsSysMenu>();
        for (OpsSysMenu n : list) {
            if (n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }


    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(OpsSysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            routerName = "";
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(OpsSysMenu menu) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (menu.getParentId().intValue() != 0 && isInnerLink(menu)) {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }


    public String innerLinkReplaceEach(String path) {
        return StringUtils.replaceEach(path, new String[]{Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":"},
                new String[]{"", "", "", "/", "/"});
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(OpsSysMenu menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu)) {
            component = UserConstants.INNER_LINK;
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(OpsSysMenu menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(OpsSysMenu menu) {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && HttpUtil.isHttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(OpsSysMenu menu) {
        return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }
}




