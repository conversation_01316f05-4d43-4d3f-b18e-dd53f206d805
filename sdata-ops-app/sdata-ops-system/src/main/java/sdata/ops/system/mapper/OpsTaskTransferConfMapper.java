package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsTaskTransferConf;

/**
* <AUTHOR>
* @description 针对表【OPS_TASK_TRANSFER_CONF(任务转派配置-未来日期自动转派)】的数据库操作Mapper
* @createDate 2024-09-10 09:56:31
* @Entity generator.domain.OpsTaskTransferConf
*/
@Mapper
public interface OpsTaskTransferConfMapper extends BaseMapper<OpsTaskTransferConf> {

}




