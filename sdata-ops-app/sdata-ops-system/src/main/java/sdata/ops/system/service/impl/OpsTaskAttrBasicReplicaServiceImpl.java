package sdata.ops.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;

import sdata.ops.system.job.TaskException;
import sdata.ops.system.mapper.OpsTaskAttrBasicReplicaMapper;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;
import sdata.ops.system.service.OpsTaskFundInfoService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_attr_basic_replica】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
@RequiredArgsConstructor
public class OpsTaskAttrBasicReplicaServiceImpl extends ServiceImpl<OpsTaskAttrBasicReplicaMapper, OpsTaskAttrBasicReplica>
        implements OpsTaskAttrBasicReplicaService {

    private final OpsTaskFundInfoService fundInfoService;



    @Override
    public List<OpsTaskAttrBasicReplica> viewList(String id) {
        return baseMapper.viewListByTemplateId(id);
    }

    @Override
    public List<OpsTaskAttrBasicReplica> queryListByTemplateId(String templateId) {
        return baseMapper.queryListByTemplateId(templateId);
    }


    @Override
    public List<OpsTaskAttrBasicReplica> queryListByTemplateIdOfCreateType1() {
        return baseMapper.queryListByTemplateIdOfCreateType1();
    }


    @Override
    public List<OpsSpecThirdInfo> queryUnProcessDataByUserId(String userId) {
        return baseMapper.queryUnProcessDataByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifySpecTaskInfo(String userId, String dataId, String taskId, String triggerId) throws TaskException {
        //先通过triggerId查询绑定的指标id,如果triggerId==-1,则使用缺省值来写入即可保证被消费
        String indicatorId = triggerId.equals("-1") ? "120" : baseMapper.queryIndicatorByTriggerId(triggerId);
        //写入script_temp表标记该条数据已经处理
        Date date = new Date();
        String nowVal = DateUtil.format(date, "yyyyMMdd");
        Long id = IdWorker.getId();
        baseMapper.insertScriptTempTable(id, indicatorId, dataId, nowVal, date);
        //写入明细表
        OpsSpecThirdInfo info = baseMapper.querySingleThirdInfo(dataId);
//        R<Object> r=indicatorInfoFeign.executeSpecTaskResult(taskId,info.getContent(),info.getMetaInfo());
//        if(!r.isSuccess()){
//            throw new TaskException("获取oa信息详情失败", TaskException.Code.UNKNOWN);
//        }
//        List<SpecTaskDetailDTO> res= (List<SpecTaskDetailDTO>) r.getData();
//        if(res.isEmpty()){
//            throw new TaskException("获取oa信息详情失败", TaskException.Code.UNKNOWN);
//        }
        JSONObject cot = JSONUtil.parseObj(info.getContent());
        OpsTaskFundInfo opsTaskFundInfo = new OpsTaskFundInfo();
        opsTaskFundInfo.setTaskReplicaId(taskId);
        opsTaskFundInfo.setFundName(cot.getStr("name"));
//        opsTaskFundInfo.setFundCode(res.get(0).getFundCode());
//        opsTaskFundInfo.setStartDate(res.get(0).getStartDate());
//        opsTaskFundInfo.setEndDate(res.get(0).getEndDate());
        opsTaskFundInfo.setBizDate(nowVal);
        opsTaskFundInfo.setDataId(dataId);
        opsTaskFundInfo.setCompleteStatus("1");
        fundInfoService.save(opsTaskFundInfo);
    }

    @Override
    public List<OpsTaskAttrBasicReplica> queryReplicaHaveImportListByTemplateId(List<String> templateIdList) {

        return baseMapper.queryListHaveImportByTemplateId(templateIdList);
    }

    /**
     * 获取任务单元  通过上线模板ids 与 经办人或者 复核人 id
     *
     * @param templateIds 模板ids
     * @param fromId      目标人
     * @return ls
     */
    @Override
    public List<OpsTaskAttrBasicReplica> queryReplicaListByTemplateIdAndUserIdWithOwner(List<String> templateIds, String fromId) {
        return baseMapper.queryListByTemplateIdAndUserIdWithOwner(templateIds, fromId);
    }

    /**
     * 通过ids获取本身以及父信息列表
     *
     * @param ids
     * @return
     */
    @Override
    public List<OpsTaskAttrBasicReplica> queryParentAndSelfListBySelfIds(List<String> ids) {
        return baseMapper.queryParentAndSelfListBySelfIds(ids);
    }
}




