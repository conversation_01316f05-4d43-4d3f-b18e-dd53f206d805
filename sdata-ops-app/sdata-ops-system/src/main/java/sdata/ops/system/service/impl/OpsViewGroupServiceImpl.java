package sdata.ops.system.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsViewGroupDto;
import sdata.ops.base.system.model.entity.OpsViewGroup;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.system.mapper.OpsViewGroupMapper;
import sdata.ops.system.service.OpsViewGroupRelationService;
import sdata.ops.system.service.OpsViewGroupService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_view_group】的数据库操作Service实现
 * @createDate 2025-05-22 16:38:01
 */
@Service
@RequiredArgsConstructor
public class OpsViewGroupServiceImpl extends ServiceImpl<OpsViewGroupMapper, OpsViewGroup>
        implements OpsViewGroupService {


    private final OpsViewGroupRelationService relationService;

    @Override
    public List<OpsViewGroup> treeList() {
        List<OpsViewGroup> metaList = list();
        if (CollUtil.isEmpty(metaList)) {
            return new ArrayList<>();
        }
        List<OpsViewGroupDto> groupViewCount = relationService.countNumGroupByGroupId();
        Map<Long, Integer> groupViewCountMap = groupViewCount.stream().collect(Collectors.toMap(OpsViewGroupDto::getGroupId, OpsViewGroupDto::getCountNum));
        Map<Long, OpsViewGroup> metaMap = metaList.stream().collect(Collectors.toMap(OpsViewGroup::getId, i -> i));
        for (OpsViewGroup meta : metaList) {
            meta.setCount(groupViewCountMap.getOrDefault(meta.getId(), 0));
            Long parentId = meta.getPId();
            if (parentId != null && !parentId.equals(0L)) { // 根节点的parentId为0
                OpsViewGroup parent = metaMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(meta);
                }
            }
        }
        // 收集根节点
        List<OpsViewGroup> result = new ArrayList<>();
        for (OpsViewGroup meta : metaList) {
            if (meta.getPId() == null || meta.getPId().equals(0L)) { // 假设根节点的parentId为0
                result.add(meta);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteGroup(Long id) {
        removeById(id);
        relationService.remove(Wrappers.lambdaQuery(OpsViewGroupRelation.class).eq(OpsViewGroupRelation::getGroupId, id));
    }


}
