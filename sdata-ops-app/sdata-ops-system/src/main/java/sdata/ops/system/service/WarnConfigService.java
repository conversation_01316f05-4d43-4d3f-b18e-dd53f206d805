package sdata.ops.system.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.WarnConfig;

/**
*/
public interface WarnConfigService extends IService<WarnConfig> {

    @Transactional(rollbackFor = Exception.class)
    void saveTableConf(String taskId, JSONObject config);

    JSONObject queryJson(String taskId);
}
