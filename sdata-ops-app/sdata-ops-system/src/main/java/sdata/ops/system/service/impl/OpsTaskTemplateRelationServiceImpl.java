package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsTaskTemplateRelation;
import sdata.ops.system.mapper.OpsTaskTemplateRelationMapper;
import sdata.ops.system.service.OpsTaskTemplateRelationService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_template_relation(菜单权限表)】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
public class OpsTaskTemplateRelationServiceImpl extends ServiceImpl<OpsTaskTemplateRelationMapper, OpsTaskTemplateRelation>
        implements OpsTaskTemplateRelationService {

    @Override
    public List<String> findIds(String templateId) {
        return baseMapper.findReplicaIds(templateId);
    }
}




