package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.dto.MapperDTO;
import sdata.ops.base.system.model.entity.OpsSysUserOrg;
import sdata.ops.base.system.model.entity.OpsSysUserRole;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.SystemUserVO;
import sdata.ops.common.config.propertites.LoginConfigParams;
import sdata.ops.system.mapper.SystemUserMapper;
import sdata.ops.system.service.OpsSysUserOrgService;
import sdata.ops.system.service.OpsSysUserRoleService;
import sdata.ops.system.service.SystemUserService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SystemUserServiceImpl extends ServiceImpl<SystemUserMapper, SystemUser> implements SystemUserService {


    private final LoginConfigParams loginConfig;

    private final OpsSysUserRoleService userRoleService;

    private final OpsSysUserOrgService userOrgService;

    /***
     * 查询用户是否存在，不存在创建
     * @param username  账户
     * @param password  密码
     * @return userEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SystemUser findOrCreateUser(String username, String password) {
        //如果密码不为空，则认定为system登录(约定如此)
        if (StringUtils.hasText(password)) {
            List<SystemUser> arr = list(Wrappers.lambdaQuery(SystemUser.class).eq(SystemUser::getUsername, username).
                    eq(SystemUser::getPassword, password).eq(SystemUser::getStatus, 0));
            return arr.isEmpty() ? null : arr.get(0);
        }
        //域控验证登录只查询用户是否存在与系统用户表中
        List<SystemUser> arr = list(Wrappers.lambdaQuery(SystemUser.class).eq(SystemUser::getUsername, username));
        //如果域控登录与cas认证登录通过后，系统内表不存在用户，且系统未进行手动同步数据的情况下，自动默认新增用户
        if (arr.isEmpty() && !loginConfig.getUserAsync()) {
            return createUser(username);
        }
        //返回用户信息
        return arr.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUser(SystemUserVO vo) {
        //先保存或更新用户
        SystemUser user = vo.convertEn();
        saveOrUpdate(user);
        //删除表角色关系
        if (StringUtils.hasText(vo.getRole())) {
            userRoleService.remove(Wrappers.lambdaQuery(OpsSysUserRole.class).eq(OpsSysUserRole::getUserId, user.getId()));
            List<OpsSysUserRole> add = new ArrayList<>();
            for (String roleId : vo.getRole().split(",")) {
                OpsSysUserRole item = new OpsSysUserRole();
                item.setUserId(Long.parseLong(user.getId()));
                item.setRoleId(Long.parseLong(roleId));
                add.add(item);
            }
            userRoleService.saveBatch(add);
        }
        //删除部门关系表
        if (StringUtils.hasText(vo.getDept())) {
            userOrgService.remove(Wrappers.lambdaQuery(OpsSysUserOrg.class).eq(OpsSysUserOrg::getUserId, user.getId()));
            List<OpsSysUserOrg> add = new ArrayList<>();
            for (String deptId : vo.getDept().split(",")) {
                OpsSysUserOrg item = new OpsSysUserOrg();
                item.setUserId(Long.parseLong(user.getId()));
                item.setOrgId(Long.parseLong(deptId));
                add.add(item);
            }
            userOrgService.saveBatch(add);
        }

    }

    /**
     * 根据条件查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SystemUser> selectUserList(SystemUser user) {
        return null;
    }

    /**
     * 根据条件查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SystemUser> selectAllocatedList(SystemUser user) {
        return baseMapper.selectAllocatedList(user);
    }

    @Override
    public List<SystemUser> selectUnallocatedList(SystemUser user) {
        return baseMapper.selectUnallocatedList(user);
    }

    @Override
    public SystemUser selectUserByUserName(String userName) {
        return getOne(Wrappers.lambdaQuery(SystemUser.class).eq(SystemUser::getUsername, userName));
    }

    @Override
    public SystemUser selectUserById(Long userId) {
        return getOne(Wrappers.lambdaQuery(SystemUser.class).eq(SystemUser::getId, userId));
    }

    @Override
    public String selectUserRoleGroup(String userName) {
        return null;
    }

    @Override
    public String selectUserPostGroup(String userName) {
        return null;
    }

    @Override
    public boolean checkUserNameUnique(SystemUser user) {
        return false;
    }

    @Override
    public boolean checkPhoneUnique(SystemUser user) {
        return false;
    }

    @Override
    public boolean checkEmailUnique(SystemUser user) {
        return false;
    }

    @Override
    public void checkUserAllowed(SystemUser user) {

    }

    @Override
    public void checkUserDataScope(Long userId) {

    }

    @Override
    public int insertUser(SystemUser user) {
        return super.baseMapper.insert(user);
    }

    @Override
    public boolean registerUser(SystemUser user) {
        return false;
    }

    @Override
    public int updateUser(SystemUser user) {
        return 0;
    }

    @Override
    public void insertUserAuth(Long userId, Long[] roleIds) {

    }

    @Override
    public int updateUserStatus(SystemUser user) {
        return 0;
    }

    @Override
    public int updateUserProfile(SystemUser user) {
        return 0;
    }

    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return false;
    }

    @Override
    public int resetPwd(SystemUser user) {
        return 0;
    }

    @Override
    public int resetUserPwd(String userName, String password) {
        return 0;
    }

    @Override
    public int deleteUserById(Long userId) {
        return 0;
    }

    @Override
    public int deleteUserByIds(Long[] userIds) {
        return 0;
    }

    @Override
    public String importUser(List<SystemUser> userList, Boolean isUpdateSupport, String operName) {
        return null;
    }

    @Override
    public Map<String, String> findRoleAndOrgNameMapper() {
        List<MapperDTO> as = super.baseMapper.findAllOrgAndRoleName();
        Map<String, String> map = new HashMap<>();
        for (MapperDTO dto : as) {
            map.put(dto.getId(), dto.getName());
        }
        return map;
    }

    /***
     * 用户默认创建
     * @param username 唯一账户
     * @return entity
     */
    @Transactional(rollbackFor = Exception.class)
    public SystemUser createUser(String username) {
        SystemUser user = new SystemUser();
        user.setUsername(username);
        user.setPassword("sys");
        user.setId(IdWorker.getId() + "");
        //默认新增用户自动入库添加默认角色2，普通菜单角色
        user.setRole("2");
        //保存关系
        save(user);
        OpsSysUserRole role=new OpsSysUserRole();
        role.setUserId(Long.valueOf(user.getId()));
        role.setRoleId(2L);
        userRoleService.save(role);
        return user;
    }

    @Override
    public void updateStatus(String id, Integer status) {
        baseMapper.updateUserStatus(id, status);
    }

    @Override
    public Map<String, String> findNameIdMapping() {
        List<MapperDTO> idName = baseMapper.queryAllUserShortInfo();
        return idName.stream().filter(i->i.getName()!=null&&i.getId()!=null).collect(Collectors.toMap(MapperDTO::getId, MapperDTO::getName));
    }

    @Override
    public List<SystemUser> findUserByLeaderId(String userId) {
        String orgId=baseMapper.getLeaderForOrgId(userId);
        if(StringUtils.hasText(orgId)) {
            orgId="'%"+orgId+"%'";
            return baseMapper.findUserByLeaderId(orgId);
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, String> queryNameIdMap() {
        List<MapperDTO> idName = baseMapper.queryAllUserShortInfo();
        return idName.stream().collect(Collectors.toMap(MapperDTO::getName,MapperDTO::getId));
    }

    @Override
    public List<String> diffOrgInfoByUserId(String fromId, String toId) {
        return userOrgService.diffInfoBy(fromId,toId);
    }
}
