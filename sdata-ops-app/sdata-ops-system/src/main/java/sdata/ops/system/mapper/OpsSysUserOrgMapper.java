package sdata.ops.system.mapper;

import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsSysUserOrg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import sdata.ops.base.system.model.entity.SystemUser;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_user_post(用户与机构岗位关联表)】的数据库操作Mapper
* @createDate 2024-06-03 19:48:03
*/
public interface OpsSysUserOrgMapper extends BaseMapper<OpsSysUserOrg> {

    List<SystemUser> queryUserInfoByOrgId(@Param("orgId") String orgId);

    List<SystemUser> queryUserInfoNotAuthByOrgId(@Param("orgId") String orgId,@Param("name")String name,@Param("phone")String phone);

    void updateUserOrgStr(@Param("orgId") Long orgId, @Param("userId") Long userId);

    List<String> diffInfoBy(@Param("fromId") String fromId, @Param("toId") String toId);
}




