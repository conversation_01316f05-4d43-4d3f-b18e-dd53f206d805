package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsTaskTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_template(菜单权限表)】的数据库操作Mapper
 * @createDate 2024-07-09 15:23:13
 */
public interface OpsTaskTemplateMapper extends BaseMapper<OpsTaskTemplate> {

    IPage<OpsTaskTemplate> findAllByMultiCondition(Page<OpsTaskTemplate> page, @Param("name") String templateName, @Param("status") String templateStatus, @Param("type") String templateType);


    void removeRelations(@Param("id") String id);

    void removeReplicaRelations(@Param("id") String id);

    List<String> listReplicaIds(@Param("id") Long taskId);
}




