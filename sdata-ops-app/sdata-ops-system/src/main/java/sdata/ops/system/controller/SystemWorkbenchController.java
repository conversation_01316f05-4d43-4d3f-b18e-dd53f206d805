package sdata.ops.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.BaseEntity;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewPreferences;
import sdata.ops.base.system.model.vo.OpsViewPreferencesVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.OpsViewPreferencesService;
import sdata.ops.system.service.OpsViewService;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/workbench")
@RequiredArgsConstructor
public class SystemWorkbenchController {


    private final OpsViewService viewService;


    @GetMapping("/viewList")
    public R<Object> list() {
        String id = SecureUtil.currentUserId();
        return R.data(viewService.viewList(id));
    }

    @GetMapping("/viewPreferences")
    public R<Object> listByPermissions() {
        String id = SecureUtil.currentUserId();
        return R.data(viewService.shortViewList(id));
    }


    @PostMapping("/saveViewPreferences")
    public R<Object> saveViewPreferences(@RequestBody List<OpsViewPreferencesVO> preferences) {
        viewService.removePreferences(preferences);
        return R.success("保存成功");
    }

}
