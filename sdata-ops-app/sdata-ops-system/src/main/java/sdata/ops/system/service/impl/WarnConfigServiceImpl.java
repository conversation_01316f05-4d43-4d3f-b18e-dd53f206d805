package sdata.ops.system.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.WarnConfig;
import sdata.ops.system.mapper.WarnConfigMapper;
import sdata.ops.system.service.WarnConfigService;

/**
* <AUTHOR>
* @description 针对表【WARN_CONFIG】的数据库操作Service实现
* @createDate 2025-01-02 10:10:00
*/
@Service
public class WarnConfigServiceImpl extends ServiceImpl<WarnConfigMapper, WarnConfig>
    implements WarnConfigService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTableConf(String taskId, JSONObject config){
        WarnConfig warnConfig=new WarnConfig();
        warnConfig.setConfigId(taskId);
        warnConfig.setConfig(JSONUtil.toJsonStr(config));
        saveOrUpdate(warnConfig);
    }

    @Override
    public JSONObject queryJson(String taskId){
        WarnConfig warnConfig=getById(taskId);
        if(warnConfig != null){
            return JSONUtil.parseObj(warnConfig.getConfig());
        } else {
            return null;
        }
    }
}




