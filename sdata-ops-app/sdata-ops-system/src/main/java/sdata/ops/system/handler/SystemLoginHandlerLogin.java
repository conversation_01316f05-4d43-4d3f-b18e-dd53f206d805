package sdata.ops.system.handler;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.system.service.SystemUserService;

@Slf4j
@Component("SYSTEM")
@RequiredArgsConstructor
public class SystemLoginHandlerLogin implements LoginAuthHandler {

    private final SystemUserService systemUserService;

    @Override
    public SystemUser userVerity(String username, String password) {
        return systemUserService.findOrCreateUser(username, password);
    }

    @Override
    public void silentLogin(String username, String password, String token, String ticket) {

    }
}
