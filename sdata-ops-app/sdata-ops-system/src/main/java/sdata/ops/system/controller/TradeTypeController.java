package sdata.ops.system.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsTradeType;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.OpsTradeTypeService;

/**
 * <AUTHOR>
 * @since 2024/7/23 15:21
 */
@RestController
@RequestMapping("/tradeType")
@RequiredArgsConstructor
public class TradeTypeController {

    final private OpsTradeTypeService tradeTypeService;

    @ControllerAuditLog(value = "交易类型-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/listByPage")
    public R<?> listUserByPage(@RequestParam(required = false) String name,
                               @RequestParam(required = false, defaultValue = "1") int page,
                               @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        Page<OpsTradeType> pageEntity = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsTradeType> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StrUtil.isNotEmpty(name), OpsTradeType::getName, name);
        calendarWrapper.orderByDesc(OpsTradeType::getOrderSort);
        IPage<OpsTradeType> iPage = tradeTypeService.page(pageEntity, calendarWrapper);
        return R.data(iPage);
    }

    @ControllerAuditLog(value = "交易类型-列表查", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list")
    public R<?> list() {
        return R.data(tradeTypeService.list(Wrappers.lambdaQuery(OpsTradeType.class).ne(OpsTradeType::getTypeVal,2).orderByDesc(OpsTradeType::getOrderSort)));
    }

    @ControllerAuditLog(value = "交易类型-保存", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    @PostMapping("/save")
    public R<?> save(@RequestBody OpsTradeType tradeType) {
        tradeTypeService.saveOrUpdate(tradeType);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @ControllerAuditLog(value = "交易类型-谰", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/detail")
    public R<?> detail(@RequestParam String id) {
        return R.data(tradeTypeService.getById(id));
    }

    @ControllerAuditLog(value = "删除", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @PostMapping("/delete")
    public R<?> delete(@RequestBody OpsTradeType tradeType) {
        tradeTypeService.removeById(tradeType.getId());
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 交易日类型校验
     */
    @ControllerAuditLog(value = "交易日类型校验", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/check")
    public R<?> test(@RequestParam String id) {
        return R.data(tradeTypeService.checkDateType(id));
    }


    @ControllerAuditLog(value = "是否工作日", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/workCheck")
    public R<Boolean> workCheck(@RequestParam(value = "date",required = false)String date){
        return R.data(tradeTypeService.checkWorkdayByToday());
    }
}
