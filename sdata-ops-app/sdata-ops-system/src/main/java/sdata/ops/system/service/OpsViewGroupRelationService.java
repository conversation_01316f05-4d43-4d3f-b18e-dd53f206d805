package sdata.ops.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.dto.OpsViewGroupDto;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【ops_view_group_relation】的数据库操作Service
* @createDate 2025-05-22 16:40:49
*/
public interface OpsViewGroupRelationService extends IService<OpsViewGroupRelation> {

    List<OpsViewGroupDto> countNumGroupByGroupId();


    Map<String,String>    templateIdGroupMapper();
}
