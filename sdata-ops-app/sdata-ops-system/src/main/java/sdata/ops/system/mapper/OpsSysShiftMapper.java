package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.dto.OpsSysShiftDTO;
import sdata.ops.base.system.model.entity.OpsSysShift;

import java.util.List;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:35
 */

@Mapper
public interface OpsSysShiftMapper extends BaseMapper<OpsSysShift> {

    IPage<OpsSysShift> list(Page<OpsSysShift> opsSysShiftPage, @Param("opsSysOrg") OpsSysShiftDTO opsSysOrg);

    List<OpsSysShift> getByOrgId(@Param("id") String id);
}
