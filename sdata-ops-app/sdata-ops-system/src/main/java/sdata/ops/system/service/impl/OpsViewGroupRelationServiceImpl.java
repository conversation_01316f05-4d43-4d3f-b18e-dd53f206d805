package sdata.ops.system.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.dto.OpsViewGroupDto;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.system.mapper.OpsViewGroupRelationMapper;
import sdata.ops.system.service.OpsViewGroupRelationService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【ops_view_group_relation】的数据库操作Service实现
* @createDate 2025-05-22 16:40:49
*/
@Service
public class OpsViewGroupRelationServiceImpl extends ServiceImpl<OpsViewGroupRelationMapper, OpsViewGroupRelation>
implements OpsViewGroupRelationService {

    @Override
    public List<OpsViewGroupDto> countNumGroupByGroupId() {
        return baseMapper.countNumGroupByGroupId();
    }

    @Override
    public Map<String, String> templateIdGroupMapper() {
        return list().stream().collect(Collectors.toMap(i -> i.getViewId() + "", i->i.getGroupId()+""));
    }
}
