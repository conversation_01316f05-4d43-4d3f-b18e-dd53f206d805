package sdata.ops.system.service;

import sdata.ops.base.system.model.entity.OpsTaskGenInfo;

import java.util.List;
import java.util.Map;

public interface OpsTaskCockpitService {

    Map<String, Integer> getStatistics(String year, String month);

    List<Map<String, Object>> getDeptStatistics(String year, String month);

    List<Map<String, Object>> getMonthStatistics(String year, String month, String deptId);

    List<Map<String, Object>> getDelayMonthStatistics(String year, String month, String deptId);

    List<Map<String, Object>> getLessDeptStatistics(String year, String month, String deptId);

    Map<String, Object> getComprehensiveDept(String year, String month, String deptId);
    Map<String, Object> getComprehensiveDeptV1(String year, String month, String deptId);

    Map<String, Object> getTradeDeptDetail(String year, String month, String deptId, String quarter);

    /**
     * 导出接口
     *
     * @param year
     * @param month
     * @param deptId
     * @param quarter
     * @return
     */
    List<OpsTaskGenInfo> getTradeDeptDetailExport(String year, String month, String deptId, String quarter);
}
