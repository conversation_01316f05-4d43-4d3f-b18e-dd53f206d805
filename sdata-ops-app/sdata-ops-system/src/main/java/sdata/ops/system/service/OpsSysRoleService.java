package sdata.ops.system.service;

import sdata.ops.base.system.model.entity.OpsSysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsSysUserRole;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【ops_sys_role(角色信息表)】的数据库操作Service
* @createDate 2024-06-03 19:48:03
*/
 public interface OpsSysRoleService extends IService<OpsSysRole> {


    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
     List<OpsSysRole> selectRoleList(OpsSysRole role);

    Map<String,List<String>> queryRoleWithMenuIds();

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
     List<OpsSysRole> selectRolesByUserId(Long userId);

    /**
     * 根据用户ID查询角色权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
     Set<String> selectRolePermissionByUserId(Long userId);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
     List<OpsSysRole> selectRoleAll();

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
     List<Long> selectRoleListByUserId(Long userId);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    OpsSysRole selectRoleById(Long roleId);

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
     boolean checkRoleNameUnique(OpsSysRole role);

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
     boolean checkRoleKeyUnique(OpsSysRole role);

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
     void checkRoleAllowed(OpsSysRole role);

    /**
     * 校验角色是否有数据权限
     *
     * @param roleIds 角色id
     */
     void checkRoleDataScope(Long... roleIds);

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
     int countUserRoleByRoleId(Long roleId);

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
     int insertRole(OpsSysRole role);

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
     int updateRole(OpsSysRole role);

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
     int updateRoleStatus(OpsSysRole role);

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
     int authDataScope(OpsSysRole role);

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
     int deleteRoleById(Long roleId);

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
     int deleteRoleByIds(Long[] roleIds);

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
     int deleteAuthUser(OpsSysUserRole userRole);

    /**
     * 批量取消授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
     int deleteAuthUsers(Long roleId, Long[] userIds);

    /**
     * 批量选择授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
     int insertAuthUsers(Long roleId, Long[] userIds);
}
