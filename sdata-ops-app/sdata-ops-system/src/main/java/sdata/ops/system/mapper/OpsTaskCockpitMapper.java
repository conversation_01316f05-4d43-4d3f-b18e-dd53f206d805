package sdata.ops.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface OpsTaskCockpitMapper {

    Integer countByStatus(@Param("year") String year, @Param("month") String month, @Param("status") Integer status);

    List<Map<String, Object>> deptByStatus(@Param("year") String year, @Param("month") String month, @Param("status") Integer status);

    List<Map<String, Object>> deptInCheck(@Param("year") String year, @Param("month") String month);

    List<Map<String, Object>> countByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptId") String deptId, @Param("ids") List<Long> ids);

    List<Map<String, Object>> checkCountByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptId") String deptId, @Param("ids") List<Long> ids);

    List<Map<String, Object>> delayCountByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptId") String deptId, @Param("ids") List<Long> ids);

    List<Map<String, Object>> lessDept(@Param("year") String year, @Param("month") String month, @Param("ids") List<Long> ids);

    List<Map<String, Object>> checkLessDept(@Param("year") String year, @Param("month") String month, @Param("ids") List<Long> ids);

    List<Map<String, Object>> getComprehensiveDept(@Param("ls") List<String> orgIds, @Param("st") Date st, @Param("et") Date et);
}
