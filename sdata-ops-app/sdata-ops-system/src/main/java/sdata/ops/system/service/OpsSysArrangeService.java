package sdata.ops.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.dto.OpsSysArrangeDTO;
import sdata.ops.base.system.model.entity.OpsSysArrange;

public interface OpsSysArrangeService extends IService<OpsSysArrange> {

    Object list(String id, String startDate,String endDate);

    Integer findByShiftId(String id);

    void cover(OpsSysArrangeDTO opsSysArrange);

    void batchScheduling(OpsSysArrangeDTO opsSysArrange);


    Integer findByOrgShiftUserDate(OpsSysArrange opsSysArrange);


}
