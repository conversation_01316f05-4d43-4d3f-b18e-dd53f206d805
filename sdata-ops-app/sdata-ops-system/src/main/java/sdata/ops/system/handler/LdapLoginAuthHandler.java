package sdata.ops.system.handler;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.common.config.propertites.LoginConfigParams;
import sdata.ops.system.config.DynamicSSLSocketFactory;
import sdata.ops.system.service.SystemUserService;

import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.util.Hashtable;

@Slf4j
@Component("LDAP")
@RequiredArgsConstructor
public class LdapLoginAuthHandler implements LoginAuthHandler {

    private final LoginConfigParams loginConfigParams;

    private final SystemUserService systemUserService;

    @Override
    public SystemUser userVerity(String username, String password) {
        StringBuilder loginUser = new StringBuilder(username);
        //dc配置不为空，则域控账户追加dc
        if (!loginConfigParams.getLdapDc().isEmpty()) {
            loginUser.append("@").append(String.join(".", loginConfigParams.getLdapDc()));
        }
        //校验域控响应是否该用户有效
        if (authenticate(loginUser.toString(), password, loginConfigParams.getLdapUrl())) {
            //有效返回本系统同步用户信息
            return systemUserService.findOrCreateUser(username, null);
        }

        return null;
    }

    @Override
    public void silentLogin(String username, String password, String token, String ticket) {

    }


    /**
     * 域控链接验证，不做数据用户同同步
     *
     * @param username 域控账户
     * @param password 密码
     * @param ldapUrl  服务器地址
     * @return bool
     */
    public boolean authenticate(String username, String password, String ldapUrl) {


        if (getPort(ldapUrl) == 636) {
            return sslLdap(username, password, ldapUrl);
        }
        if (getPort(ldapUrl) == 389) {
            return normalLdap(username, password, ldapUrl);
        }

        return false;
    }

    private int getPort(String ldapUrl) {
        try {
            // 解析 URL
            String protocol = ldapUrl.substring(0, ldapUrl.indexOf(':'));
            String hostAndPort = ldapUrl.substring(ldapUrl.indexOf("//") + 2);
            int port = -1;

            // 检查是否有显式端口
            if (hostAndPort.contains(":")) {
                port = Integer.parseInt(hostAndPort.split(":")[1]);
            } else {
                // 如果没有显式端口，使用默认端口
                port = (protocol.equals("ldaps")) ? 636 : 389;
            }
            return port;
        } catch (Exception e) {
            log.error("Invalid URL: {} " ,ldapUrl,e);
        }
        return -1;
    }

    private boolean normalLdap(String username, String password, String ldapUrl) {

        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl); // LDAP 服务器地址
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username); // LDAP 用户的 DN"cn="+username + ",ou=develop,dc=ADTEST,dc=cn"
        env.put(Context.SECURITY_CREDENTIALS, password);

        try {
            DirContext context = new InitialDirContext(env);
            context.close();
            log.info("LDAP 验证通过 : {}", username);
            return true;
        } catch (NamingException e) {
            log.error("LDAP 验证失败 {}", username, e);
            return false;
        }

    }

    private boolean sslLdap(String username, String password, String ldapUrl) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username);
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put(Context.SECURITY_PROTOCOL, "ssl");
        env.put("java.naming.ldap.factory.socket", DynamicSSLSocketFactory.class.getName());

        try {
            DirContext context = new InitialDirContext(env);
            context.close();
            log.info("ldaps 验证通过 : {}", username);
            return true;
        } catch (NamingException e) {
            log.error("ldaps 验证失败 {}", username, e);
            return false;
        }
    }
}
