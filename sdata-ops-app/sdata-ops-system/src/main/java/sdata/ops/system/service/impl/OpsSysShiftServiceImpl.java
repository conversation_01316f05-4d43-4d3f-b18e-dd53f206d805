package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.dto.OpsSysShiftDTO;
import sdata.ops.base.system.model.entity.OpsSysShift;
import sdata.ops.system.mapper.OpsSysShiftMapper;
import sdata.ops.system.service.OpsSysShiftService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_attr_basic_replica】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
public class OpsSysShiftServiceImpl extends ServiceImpl<OpsSysShiftMapper, OpsSysShift>
        implements OpsSysShiftService {

    @Override
    public IPage<OpsSysShift> list(OpsSysShiftDTO opsSysOrg) {
        if (opsSysOrg.getPageNo() == null) {
            opsSysOrg.setPageNo(1);
        }
        if (opsSysOrg.getPageSize() == null) {
            opsSysOrg.setPageSize(10);
        }
        if (StringUtils.isNotBlank(opsSysOrg.getName())) {
            opsSysOrg.setName("%" + opsSysOrg.getName() + "%");
        }
        if (StringUtils.isNotBlank(opsSysOrg.getRemark())) {
            opsSysOrg.setRemark("%" + opsSysOrg.getRemark() + "%");
        }
        return baseMapper.list(new Page<>(opsSysOrg.getPageNo(), opsSysOrg.getPageSize()), opsSysOrg);
    }

    @Override
    public List<OpsSysShift> getByOrgId(String id) {
        return baseMapper.getByOrgId(id);
    }
}




