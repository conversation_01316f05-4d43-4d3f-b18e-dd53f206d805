package sdata.ops.system.controller;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sdata.ops.common.api.R;
import sdata.ops.system.service.LeaderMonitorService;

@RestController
@RequestMapping("/leaderDashboard")
@RequiredArgsConstructor
public class LeaderMonitorController {

    private final LeaderMonitorService leaderMonitorService;

    /**
     * 超时任务分部门展示
     *
     * @return ls
     */
    @GetMapping("/warnList")
    public R<Object> warnTaskList(@RequestParam("date") String date) {
        return R.data(leaderMonitorService.warnList(date));
    }

    /**
     * 日常任务监控情况
     */
    @GetMapping("/dailyList")
    public R<Object> dailyTask(@RequestParam("date") String date) {
        return R.data(leaderMonitorService.dailyList(date));
    }

    /**
     * 部门工作台-超时任务v1
     *
     * @return ls
     */
    @GetMapping("/warnList/v1")
    public R<Object> warnTaskListV1(@RequestParam("date") String date) {
        return R.data(leaderMonitorService.warnListV1(date));
    }

    /**
     * 部门工作台-日常任务监控情况v1
     */
    @GetMapping("/dailyList/v1")
    public R<Object> dailyTaskV1(@RequestParam("date") String date) {
        return R.data(leaderMonitorService.dailyListV1(date));
    }

    /**
     * 临时任务监控情况
     *
     * @param date 日期
     * @return ls
     */
    @GetMapping("/tempList")
    public R<Object> tempList(@RequestParam("date") String date) {
        return R.data(leaderMonitorService.tempList(date));
    }

}
