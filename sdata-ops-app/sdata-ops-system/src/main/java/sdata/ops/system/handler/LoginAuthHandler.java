package sdata.ops.system.handler;

import sdata.ops.base.system.model.entity.SystemUser;

/***
 * 用户登录校验
 * 使用本系统登录页且在本系统输入账户密码情况下登录
 */
public interface LoginAuthHandler {


    /***
     * 用户校验方法
     * @param username  用户名
     * @param password  密码
     * @return bool
     */
    SystemUser userVerity(String username, String password);

    /***
     * 静默登录，在不需要系统本身登录情况使用该方法，一般在过滤器中使用
     * @param username   用户名
     * @param password  密码
     * @param token     token字符
     * @param ticket    sso票根
     */
    void silentLogin(String username, String password, String token, String ticket);
}
