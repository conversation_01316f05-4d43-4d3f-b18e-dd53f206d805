package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.dto.UserLinkDTO;
import sdata.ops.base.system.model.entity.UserLink;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.UserLinkMapper;
import sdata.ops.system.service.UserLinkService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserLinkServiceImpl implements UserLinkService {

    private final UserLinkMapper userLinkMapper;

    @Override
    public List<UserLink> listByUserId(Long userId) {
        LambdaQueryWrapper<UserLink> query = new LambdaQueryWrapper<>();
        query.eq(UserLink::getUserId, userId)
             .orderByAsc(UserLink::getSort);
        return userLinkMapper.selectList(query);
    }

    @Override
    public void addLink(UserLinkDTO dto) {
        UserLink link = new UserLink();
        BeanUtils.copyProperties(dto, link);
        link.setUserId(Long.valueOf(SecureUtil.currentUserId()));

        // 校验数据
        validateLink(link);

        userLinkMapper.insert(link);
    }

    @Override
    public void updateLink(Long id, UserLinkDTO dto) {
        UserLink link = userLinkMapper.selectById(id);
        if (link == null) {
            throw new RuntimeException("链接不存在");
        }

        // 只能修改自己的链接
        if (!link.getUserId().equals(Long.valueOf(SecureUtil.currentUserId()))) {
            throw new RuntimeException("无权操作");
        }

        BeanUtils.copyProperties(dto, link);
        // 校验数据
        validateLink(link);
        // 更新
        link.setId(id);
        userLinkMapper.updateById(link);
    }

    @Override
    public void deleteLink(Long id) {
        UserLink link = userLinkMapper.selectById(id);
        if (link == null) {
            return;
        }

        // 只能删除自己的链接
        if (!link.getUserId().toString().equals(SecureUtil.currentUserId())) {
            throw new RuntimeException("无权操作");
        }

        userLinkMapper.deleteById(id);
    }

    private void validateLink(UserLink link) {
        if (link.getLinkType() == 1 && StringUtils.isBlank(link.getRoutePath())) {
            throw new RuntimeException("站内链接必须设置路由地址");
        }
        if (link.getLinkType() == 2 && StringUtils.isBlank(link.getUrl())) {
            throw new RuntimeException("站外链接必须设置URL地址");
        }
    }
}
