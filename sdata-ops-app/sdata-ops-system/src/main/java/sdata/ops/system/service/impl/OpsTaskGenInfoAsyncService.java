package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.system.mapper.OpsTaskGenInfoMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OpsTaskGenInfoAsyncService {


    private final OpsTaskGenInfoMapper baseMapper;

    /**
     * 实际复核操作人信息更新(任务复核类型为岗位的)
     *
     * @param children 任务列表
     * @param userId   用户id
     */
    @Async("sdataopsTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateOperationCheckInfo(List<OpsTaskGenInfo> children, String userId) {
        List<Long> ids = children.stream().filter(i -> i.getTaskCheckType() != null && i.getTaskCheckType().equals("1")).map(OpsTaskGenInfo::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            baseMapper.updateOperationCheckInfo(ids, userId);
        }
    }

    @Async("sdataopsTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateOperationBatchCheckInfo(List<String> childIds, String userIds) {
        List<OpsTaskGenInfo> ls = baseMapper.selectList(Wrappers.lambdaQuery(OpsTaskGenInfo.class).select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskCheckType).in(OpsTaskGenInfo::getId, childIds));
        List<Long> hitIds = ls.stream().filter(i -> i.getTaskCheckType() != null && i.getTaskCheckType().equals("1")).map(OpsTaskGenInfo::getId).collect(Collectors.toList());
        if (!hitIds.isEmpty()) {
            baseMapper.updateOperationCheckInfo(hitIds, userIds);
        }
    }

    /**
     * 更新任务复核与完成操作人信息(任务完成或者复核是归属到岗位情况下才会有效)
     *
     * @param opsTaskGenInfo 信息本身
     * @param userId         用户id
     */
    @Async("sdataopsTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateOperationSetNull(OpsTaskGenInfo opsTaskGenInfo, String userId) {
        List<Long> infoIds = new ArrayList<>();
        List<Long> checkIds = new ArrayList<>();
        if (StringUtils.hasText(opsTaskGenInfo.getTaskChildIds())) {
            //先查出所有子节点，短内容
            List<String> ids = new ArrayList<>(List.of(opsTaskGenInfo.getTaskChildIds().split(",")));
            LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskOwnerType, OpsTaskGenInfo::getTaskCheckType);
            queryWrapper.in(OpsTaskGenInfo::getId, ids);
            List<OpsTaskGenInfo> list = baseMapper.selectList(queryWrapper);
            List<Long> hitIds = list.stream().filter(i -> i.getTaskOwnerType().equals("1")).map(OpsTaskGenInfo::getId).collect(Collectors.toList());
            infoIds.addAll(hitIds);
            List<Long> hitCheckIds = list.stream().filter(i -> i.getTaskCheckType() != null && i.getTaskCheckType().equals("1")).map(OpsTaskGenInfo::getId).collect(Collectors.toList());
            checkIds.addAll(hitCheckIds);
        }
        if (opsTaskGenInfo.getTaskOwnerType().equals("1")) {
            infoIds.add(opsTaskGenInfo.getId());
        }
        if (opsTaskGenInfo.getTaskCheckType() != null && opsTaskGenInfo.getTaskCheckType().equals("1")) {
            checkIds.add(opsTaskGenInfo.getId());
        }
        //如果不为空,完成操作人置为null
        if (!infoIds.isEmpty()) {
            baseMapper.resetOperationId(infoIds);
        }
        //如果不为空，复核操作任务置为null
        if (!checkIds.isEmpty()) {
            baseMapper.resetOperationCheckId(checkIds);
        }
    }

    /**
     * 用户完成操作信息记录(任务归属是岗位的情况下，记录实际操作人，第一次点击的)
     *
     * @param info   任务单元信息
     * @param key    关键词
     * @param userId 用户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskOperationInfo(OpsTaskGenInfo info, int key, String userId) {
        //叶子任务更新,且任务是归属到岗位的，则记录人员id
        if (key == 1 && info.getTaskOwnerType().equals("1")) {
            baseMapper.updateSingleTaskOperationInfo(info.getId(), userId);
        }
        //分支或者根任务完成
        if (key == 2) {
            //先查出所有子节点，短内容
            List<String> ids = new ArrayList<>(List.of(info.getTaskChildIds().split(",")));
            LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(OpsTaskGenInfo::getId,
                    OpsTaskGenInfo::getTaskOwnerType);
            queryWrapper.in(OpsTaskGenInfo::getId, ids);
            List<OpsTaskGenInfo> list =baseMapper.selectList(queryWrapper);
            list.add(info);
            List<Long> hitIds = list.stream().filter(i -> i.getTaskOwnerType().equals("1")).map(OpsTaskGenInfo::getId).collect(Collectors.toList());
            if (!hitIds.isEmpty()) {
                baseMapper.updateBatchTaskOperationInfo(hitIds, userId);
            }
        }
    }
}
