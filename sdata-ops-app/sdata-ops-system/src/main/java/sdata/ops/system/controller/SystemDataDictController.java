package sdata.ops.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysDictItem;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.OpsSysDictItemService;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/system-data-dict")
@RequiredArgsConstructor
public class SystemDataDictController {


    private final OpsSysDictItemService opsSysDictItemService;

    @ControllerAuditLog(value = "获取字典项分页列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/page")
    public R<Object> page(@RequestParam(required = false, defaultValue = "1")Integer page,
                                        @RequestParam(required = false, defaultValue = "10")Integer pageSize,
                                        @RequestParam(required = false)String dictType,
                                        @RequestParam(required = false)String dictValue){
        IPage<OpsSysDictItem> pageEntity = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsSysDictItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNoneBlank(dictType),OpsSysDictItem::getDictType, dictType)
                .like(StringUtils.isNoneBlank(dictValue),OpsSysDictItem::getDictValue, dictValue);
        return R.data(opsSysDictItemService.page(pageEntity, queryWrapper));
    }

    @ControllerAuditLog(value = "保存或更新字典项", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PostMapping("save")
    public R<Object> save(@RequestBody OpsSysDictItem opsSysDictItem){
        return R.data(opsSysDictItemService.saveOrUpdate(opsSysDictItem));
    }

    @ControllerAuditLog(value = "删除字典项", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @GetMapping("delete")
    public R<Object> delete(@RequestParam Long id){
        return R.data(opsSysDictItemService.removeById(id));
    }

    @ControllerAuditLog(value = "根据ID获取字典项", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("get")
    public R<Object> get(@RequestParam Long id){
        return R.data(opsSysDictItemService.getById(id));
    }

    @ControllerAuditLog(value = "根据类型获取字典项列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("listByType")
    public R<Object> list(@RequestParam String dictType){
        LambdaQueryWrapper<OpsSysDictItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsSysDictItem::getDictType, dictType);
        return R.data(opsSysDictItemService.list(queryWrapper));
    }

//    @GetMapping("listByTypes")
//    public R<List<OpsSysDictItem>> listByTypes(@RequestParam String[] dictTypes) {
//        LambdaQueryWrapper<OpsSysDictItem> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.in(OpsSysDictItem::getDictType, Arrays.asList(dictTypes));
//        return R.data(opsSysDictItemService.list(queryWrapper));
//    }

    @ControllerAuditLog(value = "获取所有字典项列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("list")
    public R<Object> list(){
        return R.data(opsSysDictItemService.list());
    }

}
