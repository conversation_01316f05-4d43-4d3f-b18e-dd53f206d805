package sdata.ops.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsTaskFundInfoDTO;
import sdata.ops.base.system.model.dto.OpsTaskInfoDeleteDTO;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsTaskAttrBasicReplicaMapper;
import sdata.ops.system.mapper.OpsTaskFundInfoMapper;
import sdata.ops.system.service.OpsTaskFundInfoService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/19 10:34
 */
@RequiredArgsConstructor
@Service
public class OpsTaskFundInfoServiceImpl extends ServiceImpl<OpsTaskFundInfoMapper, OpsTaskFundInfo> implements OpsTaskFundInfoService {

    final private OpsTaskAttrBasicReplicaMapper replicaMapper;

    /**
     * 更新任务完成状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OpsTaskInfoUpdateDTO dto, String operator) {
        String taskId = dto.getTaskReplicaId();
        List<OpsTaskFundInfo> detailList = dto.getDetailList();
        for (OpsTaskFundInfo fundInfo : detailList) {
            fundInfo.setTaskReplicaId(taskId);
            fundInfo.setOperator(operator);
            this.saveOrUpdate(fundInfo);
        }
    }

    /**
     * 更新任务完成状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFromIndicator(OpsTaskInfoUpdateDTO dto, String operator) {
        String taskId = dto.getTaskReplicaId();
        List<OpsTaskFundInfo> detailList = dto.getDetailList();
        List<OpsTaskFundInfo> save = new ArrayList<>();
        for (OpsTaskFundInfo fundInfo : detailList) {
            List<OpsTaskFundInfo> ls = list(Wrappers.lambdaQuery(OpsTaskFundInfo.class).
                    eq(StringUtils.isNotBlank(fundInfo.getFundCode()), OpsTaskFundInfo::getFundCode, fundInfo.getFundCode())
                    .eq(StringUtils.isNotBlank(fundInfo.getFundName()), OpsTaskFundInfo::getFundName, fundInfo.getFundName()).
                    eq(OpsTaskFundInfo::getStartDate, fundInfo.getStartDate())
                    .eq(OpsTaskFundInfo::getEndDate, fundInfo.getEndDate()));
            if (ls.isEmpty()) {
                fundInfo.setTaskReplicaId(taskId);
                fundInfo.setOperator(operator);
                save.add(fundInfo);
            }
            saveBatch(save);
        }
    }

    /**
     * 删除任务明显
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(OpsTaskInfoDeleteDTO dto) {
        List<Long> idList = dto.getIdList();
        if (CollectionUtil.isNotEmpty(idList)) {
            this.removeByIds(idList);
        }
    }


    /**
     * 批量保存产品信息
     *
     * @param dataList 数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateFundInfo(String taskId, String ownerOrgId, List<OpsTaskFundInfoDTO> dataList) {
        // 查询子集
        LambdaQueryWrapper<OpsTaskAttrBasicReplica> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OpsTaskAttrBasicReplica::getOwnerOrgId, ownerOrgId);
        queryWrapper.eq(OpsTaskAttrBasicReplica::getParentId, taskId);
        // TODO 是否加上线或未上线判断
        List<OpsTaskAttrBasicReplica> replicaList = replicaMapper.selectList(queryWrapper);
        if (replicaList.isEmpty()) {
            return;
        }
        for (OpsTaskFundInfoDTO dto : dataList) {
            String fundCode = dto.getFundCode();
            String fundName = dto.getFundName();
            String openDay = dto.getOpenDay();
            for (OpsTaskAttrBasicReplica replica : replicaList) {
                OpsTaskFundInfo save = new OpsTaskFundInfo();
                save.setFundCode(fundCode);
                save.setFundName(fundName);
                save.setTaskReplicaId(replica.getId());
                // TODO 根据openDay 处理开始结束时间
                save.setStartDate("");
                save.setEndDate("");
                save.setCompleteStatus("1");
                this.save(save);
            }
        }
    }


    /**
     * 关联产品的任务列表
     *
     * @param ownerOrgId 岗位ID
     * @return 返回结果
     */
    @Override
    public List<OpsTaskGenInfo> getOpsTaskInfoList(String ownerOrgId, String now) {
        List<OpsTaskGenInfo> result = new ArrayList<>();

        // 查询启用固定任务流程的任务单元
        List<OpsTaskAttrBasicReplica> replicaList = replicaMapper.queryListByImportStatus(ownerOrgId);
        if (replicaList.isEmpty()) {
            return result;
        }
        //获取流程任务单元明细层数据
        List<String> replicaIdList = replicaList.stream().map(OpsTaskAttrBasicReplica::getId).collect(Collectors.toList());
        //  更改查询规则使用 明细查询sql
        Map<String, List<OpsTaskFundInfo>> fundMap =baseMapper.queryMergeForDashboard(now,replicaIdList).stream().collect(Collectors.groupingBy(OpsTaskFundInfo::getTaskReplicaId));

        //数据统计与任务单元实体转换
        for (OpsTaskAttrBasicReplica replica : replicaList) {
            // 转成任务
            OpsTaskGenInfo genInfo = this.convert(replica);
            List<OpsTaskFundInfo> fundInfoList = fundMap.get(replica.getId());
            if (!CollectionUtil.isEmpty(fundInfoList)) {
                long count = fundInfoList.stream().filter(r -> r.getCompleteStatus() != null && r.getCompleteStatus().equals("3")).count();
                if (count == fundInfoList.size()) {
                    // 全部完成
                    genInfo.setTaskCompleteStatus(3);
                    genInfo.setTotalDetail(fundInfoList.size());
                    genInfo.setCompleteDetail(fundInfoList.size());
                } else {
                    // 进行中
                    genInfo.setTaskCompleteStatus(1);
                    genInfo.setTotalDetail(fundInfoList.size());
                    genInfo.setCompleteDetail(count);
                }
            } else {
                genInfo.setTaskCompleteStatus(3);
                genInfo.setTotalDetail(0);
                genInfo.setCompleteDetail(0);
            }
            result.add(genInfo);
        }

        // 转成树状

        Map<Long, List<OpsTaskGenInfo>> groupMap = result.stream().collect(Collectors.groupingBy(OpsTaskGenInfo::getParentId));

        List<OpsTaskGenInfo> leafNodeList = result.stream().filter(e -> result.stream().noneMatch(r -> r.getParentId().equals(e.getId()))).collect(Collectors.toList());
        for (OpsTaskGenInfo leafNode : leafNodeList) {
            if (leafNode.getCompleteDetail() != leafNode.getTotalDetail()) {
                leafNode.setTaskCompleteStatus(1);
            }
        }

        List<OpsTaskGenInfo> opsTaskGenInfoList = format(leafNodeList, result, true);
        List<OpsTaskGenInfo> parentList = opsTaskGenInfoList.stream().filter(r -> r.getParentId() == 0L).collect(Collectors.toList());

        for (OpsTaskGenInfo genInfo : parentList) {
            this.recursion(1, genInfo, groupMap);
        }

//        parentList.sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
//                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
        return parentList;
    }

    @Override
    public List<OpsTaskGenInfo> getOpsTaskInfoListByLeader(String ownerOrgId, String now) {
        List<OpsTaskGenInfo> result = new ArrayList<>();

        // 查询启用固定任务流程的任务单元
        List<OpsTaskAttrBasicReplica> replicaList = replicaMapper.queryListByImportStatusByPriority(ownerOrgId);
        if (replicaList.isEmpty()) {
            return result;
        }
        //获取流程任务单元明细层数据
        List<String> replicaIdList = replicaList.stream().map(OpsTaskAttrBasicReplica::getId).collect(Collectors.toList());
        //  更改查询规则使用 明细查询sql
        Map<String, List<OpsTaskFundInfo>> fundMap =baseMapper.queryMergeForDashboard(now,replicaIdList).stream().collect(Collectors.groupingBy(OpsTaskFundInfo::getTaskReplicaId));

        //数据统计与任务单元实体转换
        for (OpsTaskAttrBasicReplica replica : replicaList) {
            // 转成任务
            OpsTaskGenInfo genInfo = this.convert(replica);
            List<OpsTaskFundInfo> fundInfoList = fundMap.get(replica.getId());
            if (!CollectionUtil.isEmpty(fundInfoList)) {
                long count = fundInfoList.stream().filter(r -> r.getCompleteStatus() != null && r.getCompleteStatus().equals("3")).count();
                if (count == fundInfoList.size()) {
                    // 全部完成
                    genInfo.setTaskCompleteStatus(3);
                    genInfo.setTotalDetail(fundInfoList.size());
                    genInfo.setCompleteDetail(fundInfoList.size());
                } else {
                    // 进行中
                    genInfo.setTaskCompleteStatus(1);
                    genInfo.setTotalDetail(fundInfoList.size());
                    genInfo.setCompleteDetail(count);
                }
            } else {
                genInfo.setTaskCompleteStatus(3);
                genInfo.setTotalDetail(0);
                genInfo.setCompleteDetail(0);
            }
            result.add(genInfo);
        }

        // 转成树状

        Map<Long, List<OpsTaskGenInfo>> groupMap = result.stream().collect(Collectors.groupingBy(OpsTaskGenInfo::getParentId));

        List<OpsTaskGenInfo> leafNodeList = result.stream().filter(e -> result.stream().noneMatch(r -> r.getParentId().equals(e.getId()))).collect(Collectors.toList());
        for (OpsTaskGenInfo leafNode : leafNodeList) {
            if (leafNode.getCompleteDetail() != leafNode.getTotalDetail()) {
                leafNode.setTaskCompleteStatus(1);
            }
        }

        List<OpsTaskGenInfo> opsTaskGenInfoList = format(leafNodeList, result, true);
        List<OpsTaskGenInfo> parentList = opsTaskGenInfoList.stream().filter(r -> r.getParentId() == 0L).collect(Collectors.toList());

        for (OpsTaskGenInfo genInfo : parentList) {
            this.recursion(1, genInfo, groupMap);
        }
        return parentList;
    }

    /**
     * 自底而上递归修改任务状态
     */
    private List<OpsTaskGenInfo> format(List<OpsTaskGenInfo> list, List<OpsTaskGenInfo> result, Boolean isLeaf) {
        if (!isLeaf) {
            for (OpsTaskGenInfo l : list) {
                if (result.stream().anyMatch(r -> r.getParentId().equals(l.getId()) && r.getTaskCompleteStatus().equals(1))) {
                    l.setTaskCompleteStatus(1);
                } else {
                    l.setTaskCompleteStatus(3);
                }
            }
        }
        List<OpsTaskGenInfo> newList = new ArrayList<>(list);

        List<OpsTaskGenInfo> parentList = result.stream().filter(e -> list.stream().anyMatch(l -> l.getParentId().equals(e.getId()))).collect(Collectors.toList());
        if (!parentList.isEmpty()) newList.addAll(format(parentList, result, false));

        return newList;
    }

    /**
     * 递归
     */
    private void recursion(int level, OpsTaskGenInfo genInfo, Map<Long, List<OpsTaskGenInfo>> groupMap) {
        if (level > 10) {
            return;
        }
        Long id = genInfo.getId();
        List<OpsTaskGenInfo> childList = groupMap.get(id);
        if (CollectionUtil.isEmpty(childList)) {
            return;
        }
        for (OpsTaskGenInfo childInfo : childList) {
            this.recursion(level + 1, childInfo, groupMap);
        }
        childList.sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
        genInfo.setChildren(childList);
    }

    /**
     * 类型转换
     */
    private OpsTaskGenInfo convert(OpsTaskAttrBasicReplica replica) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(replica, target);
        //id置空
        target.setId(Long.valueOf(replica.getId()));
        target.setParentId(Long.valueOf(replica.getParentId()));
        //任务状态：1正常
        target.setTaskProcessStatus(1);
        // 任务完成状态：1进行中
        target.setTaskCompleteStatus(1);
        //任务来源: 3任务模板
        target.setTaskRef(3);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //重置任务开始和结束时间
        target.setTaskEndTime(null);
        target.setTaskStartTime(null);
        target.setTaskGenTime(DateUtil.today());
        target.setCreateTime(null);
        target.setUpdateTime(null);
        target.setTaskRefId(replica.getId());
        //任务默认为单节点任务
        return target;
    }

    @Override
    public List<OpsTaskFundInfo> getTaskFundList(String date, List<String> deptIds) {
        int defSign = 0;
        if (SecureUtil.isAdmin(null)) {
            defSign = 1;
        }
        return baseMapper.getTaskFundList(date, deptIds, defSign);
    }


    @Override
    public List<OpsTaskFundInfo> getTaskFundListByLeader(String date, List<String> deptIds) {
        return baseMapper.getTaskFundListByLeader(date, deptIds, 1);
    }

    @Override
    public List<OpsTaskFundInfo> getFundByDate(String date, String taskReplicaId) {
        return baseMapper.getFundByDate(date, taskReplicaId);
    }


    @Override
    public List<OpsTaskFundInfo> queryMergeForDashBoard(String date, List<String> ids){
        return  baseMapper.queryMergeForDashboard(date,ids);
    }

    @Override
    public List<OpsTaskFundInfo> getTaskFundListForMonitor(String date) {
        return baseMapper.queryFundInfoForMonitor(date);
    }

    @Override
    public List<OpsTaskFundInfo> getTaskFundListForMonitorWarn(String date) {
        return baseMapper.queryFundInfoForMonitorWran(date);
    }
}
