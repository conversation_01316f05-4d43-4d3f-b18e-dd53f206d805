package sdata.ops.system.service.impl;

import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.mo.ColumnMeta;
import sdata.ops.base.system.model.mo.TableMetaData;
import sdata.ops.common.core.util.DatabaseType;
import sdata.ops.system.handler.DatabaseDetectService;
import sdata.ops.system.service.DataMetaService;

import java.sql.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class MyBatisDatabaseMetaServiceImpl implements DataMetaService {

    private final SqlSessionFactory sqlSessionFactory;
    private final DatabaseDetectService detectService;

    @Override
    public List<String> getTableList() throws SQLException {
        DatabaseType dbType = detectService.detectDatabaseType();
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Connection connection = session.getConnection();
            return getTableListByType(dbType, connection);
        }
    }

    @Override
    public TableMetaData getTableMetaData(String tableName) throws SQLException {
        DatabaseType dbType = detectService.detectDatabaseType();
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Connection connection = session.getConnection();
            TableMetaData metaData = new TableMetaData();

            // 获取表结构
            metaData.setColumns(getColumns(connection, tableName));

            // 获取前100行数据
            metaData.setRows(getTableData(connection, dbType, tableName));

            return metaData;
        }
    }

    private List<ColumnMeta> getColumns(Connection connection, String tableName) throws SQLException {
        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet columns = metaData.getColumns(null, null, tableName, null);
        List<ColumnMeta> columnMetas = new ArrayList<>();

        while (columns.next()) {
            ColumnMeta column = new ColumnMeta();
            column.setName(columns.getString("COLUMN_NAME"));
            column.setType(columns.getString("TYPE_NAME"));
            column.setSize(columns.getInt("COLUMN_SIZE"));
            column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
            columnMetas.add(column);
        }
        return columnMetas;
    }

    private List<Map<String, Object>> getTableData(Connection connection, DatabaseType dbType, String tableName) throws SQLException {
        String query = "";
        switch (dbType) {
            case MYSQL:
                query = "SELECT * FROM " + tableName + " LIMIT 100";
                break;
            case ORACLE:
            case DM:
                query = "SELECT * FROM " + tableName + " WHERE ROWNUM <= 100";
                break;
            default:
                throw new SQLException("Unsupported database type: " + dbType);
        }

        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery(query);

        List<Map<String, Object>> rows = new ArrayList<>();
        ResultSetMetaData rsmd = resultSet.getMetaData();
        int columnCount = rsmd.getColumnCount();

        while (resultSet.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                row.put(rsmd.getColumnName(i), convertDatabaseValue(resultSet.getObject(i)));
            }
            rows.add(row);
        }
        return rows;
    }

    private List<String> getTableListByType(DatabaseType dbType, Connection connection) throws SQLException {
        DatabaseMetaData metaData = connection.getMetaData();
        String catalog = connection.getCatalog(); // 获取当前数据库名

        switch (dbType) {
            case MYSQL:
                return extractTableNames(metaData.getTables(catalog, null, null, new String[]{"TABLE"}));
            case ORACLE:
            case DM:
                return extractTableNames(metaData.getTables(null, metaData.getUserName(), "%", new String[]{"TABLE"}));
            default:
                throw new SQLException("Unsupported database type: " + dbType);
        }
    }

    private Object convertDatabaseValue(Object rawValue) {
        if (rawValue == null) {
            return null;
        }

        // 特殊处理日期时间类型
        if (rawValue instanceof java.sql.Timestamp) {
            return ((java.sql.Timestamp) rawValue).toLocalDateTime();
        }
        if (rawValue instanceof java.sql.Date) {
            return ((java.sql.Date) rawValue).toLocalDate();
        }
        if (rawValue instanceof java.sql.Time) {
            return ((java.sql.Time) rawValue).toLocalTime();
        }

        // 默认情况直接返回原值
        return rawValue;
    }

    private List<String> extractTableNames(ResultSet rs) throws SQLException {
        List<String> tables = new ArrayList<>();
        while (rs.next()) {
            tables.add(rs.getString("TABLE_NAME"));
        }
        return tables;
    }
}
