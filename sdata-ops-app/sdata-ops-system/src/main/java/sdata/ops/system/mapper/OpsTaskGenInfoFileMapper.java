package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import sdata.ops.base.system.model.entity.OpsTaskGenInfoFile;

import java.util.List;

@Mapper
public interface OpsTaskGenInfoFileMapper extends BaseMapper<OpsTaskGenInfoFile> {
    List<OpsTaskGenInfoFile> selectListByInfoId(@Param("id") Long id);
}
