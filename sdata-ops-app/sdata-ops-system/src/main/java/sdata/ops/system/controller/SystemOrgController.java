package sdata.ops.system.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.OpsSysUserOrg;
import sdata.ops.base.system.model.entity.SystemUser;
import sdata.ops.base.system.model.vo.OrgUserVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.api.UserConstants;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.system.service.OpsSysOrgService;
import sdata.ops.system.service.OpsSysUserOrgService;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/systemOrg")
@RequiredArgsConstructor
public class SystemOrgController {

    private final OpsSysOrgService deptService;

    private final OpsSysUserOrgService opsSysUserOrgService;

    /**
     * 获取部门列表
     */
    // @RequiresPermissions("system:dept:list")
    @ControllerAuditLog(value = "获取部门列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list")
    public R<Object> list(OpsSysOrg dept) {
        List<OpsSysOrg> depts = deptService.selectDeptList(dept);
        return R.data(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    // @RequiresPermissions("system:dept:list")
    @ControllerAuditLog(value = "查询部门列表（排除节点）", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/list/exclude")
    public R<Object> excludeChild(@RequestParam(value = "orgId", required = false) Long orgId) {
        List<OpsSysOrg> depts = deptService.selectDeptList(new OpsSysOrg());
        depts.removeIf(d -> d.getId().intValue() == orgId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), orgId + ""));
        return R.data(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    // @RequiresPermissions("system:dept:query")
    @ControllerAuditLog(value = "根据部门编号获取详细信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping(value = "/getById")
    public R<Object> getInfo(@RequestParam("id") Long id) {
        deptService.checkDeptDataScope(id);
        return R.data(deptService.selectDeptById(id));
    }

    /**
     * 新增部门
     */
    //@RequiresPermissions("system:dept:add")
    //@Log(title = "部门管理", businessType = BusinessType.INSERT)
    @ControllerAuditLog(value = "新增部门", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    @PostMapping("/add")
    public R<Object> add(@RequestBody OpsSysOrg dept) {
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail("新增部门'" + dept.getOrgName() + "'失败，部门名称已存在");
        }
        return R.data(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    //@RequiresPermissions("system:dept:edit")
    //@Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @ControllerAuditLog(value = "修改部门", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PostMapping("edit")
    public R<Object> edit(@RequestBody OpsSysOrg dept) {
        Long deptId = dept.getId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail("修改部门'" + dept.getOrgName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            return R.fail("修改部门'" + dept.getOrgName() + "'失败，上级部门不能是自己");
        } else if (Objects.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0) {
            return R.fail("该部门包含未停用的子部门！");
        }
        return R.data(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    //@RequiresPermissions("system:dept:remove")
    //@Log(title = "部门管理", businessType = BusinessType.DELETE)
    @ControllerAuditLog(value = "删除部门", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/delete/{orgId}")
    public R<Object> remove(@PathVariable Long orgId) {
        if (deptService.hasChildByDeptId(orgId)) {
            return R.fail("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(orgId)) {
            return R.fail("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(orgId);
        return R.data(deptService.deleteDeptById(orgId));
    }

    @ControllerAuditLog(value = "获取部门树", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/treeOrg")
    public R<Object> treeOrg(OpsSysOrg org) {
        return R.data(deptService.buildDeptTreeFull(org));
    }

    /**
     * 岗位节点上包含人员信息
     *
     * @param org 查询对象
     * @return treeNode
     */
    @ControllerAuditLog(value = "获取包含用户的部门树", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/treeOrgWithUser")
    public R<Object> treeOrgWithUser(OpsSysOrg org) {
        return R.data(deptService.buildDeptTreeFillUserInfo(org));
    }

    /**
     * 岗位节点上包含人员信息
     *
     * @param
     * @return treeNode
     */
    @ControllerAuditLog(value = "获取仅包含岗位的部门树", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/treeOrgOnlyPost")
    public R<Object> treeOrgOnlyPost() {
        return R.data(deptService.buildDeptTreeFillUserInfo(null));
    }

    /**
     * 批量授权/单独授权用户与机构关系
     *
     * @return str
     */
    @ControllerAuditLog(value = "批量授权用户与机构关系", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    @PostMapping("/authSelectUser")
    public R<String> authOrgUser(@RequestBody OrgUserVO orgUserVO) {
        if (orgUserVO.getUserIds().isEmpty()) {
            return R.fail(MessageConstant.PARAM_FAIL);
        }
        opsSysUserOrgService.saveUpdateRelation(orgUserVO);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 批量授权/单独授权用户与机构关系
     *
     * @return str
     */
    @ControllerAuditLog(value = "取消授权用户与机构关系", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    @PostMapping("/authSelectUserCancel")
    public R<String> authSelectUserCancel(@RequestBody OrgUserVO orgUserVO) {
        if (orgUserVO.getUserIds().isEmpty()) {
            return R.fail(MessageConstant.PARAM_FAIL);
        }
        LambdaQueryWrapper<OpsSysUserOrg> delete = new LambdaQueryWrapper<>();
        delete.eq(OpsSysUserOrg::getOrgId, orgUserVO.getOrgId());
        delete.in(OpsSysUserOrg::getUserId, orgUserVO.getUserIds());
        opsSysUserOrgService.remove(delete);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 通过组织id查询当前已经授权的用户
     */
    // @RequiresPermissions("system:role:list")
    @ControllerAuditLog(value = "查询已授权用户", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/allocatedList")
    public R<Object> allocatedList(@RequestParam("orgId") String orgId) {
        List<SystemUser> list = opsSysUserOrgService.selectAllocatedList(orgId);
        for (SystemUser user : list) {
            user.setPassword(null);
        }
        return R.data(list);
    }

    /**
     * 通过组织id查询当前已经授权的用户
     */
    // @RequiresPermissions("system:role:list")
    @ControllerAuditLog(value = "查询未授权用户", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/unallocatedList")
    public R<Object> unallocatedList(@RequestParam("orgId") String orgId,
                                     @RequestParam(value = "name", required = false) String name,
                                     @RequestParam(value = "phoneNum", required = false) String phoneNum) {
        List<SystemUser> list = opsSysUserOrgService.selectUnAllocatedList(orgId, name, phoneNum);
        for (SystemUser user : list) {
            user.setPassword(null);
        }
        return R.data(list);
    }

    @ControllerAuditLog(value = "根据权限获取机构", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/getOrgByPerm")
    public R<Object> getOrgByPerm() {
        return R.data(deptService.getOrgByPerm());
    }

    @ControllerAuditLog(value = "查询机构下的模板", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/templateInOrg")
    public R<Object> templateInOrg(String orgId) {
        return R.data(deptService.templateInOrg(orgId));
    }

    @ControllerAuditLog(value = "查询机构下的用户", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping("/userInOrg")
    public R<Object> userInOrg(String orgId) {
        return R.data(deptService.userInOrg(orgId));
    }


    @GetMapping("/operationOrgInfo")
    public R<Object> operationDeptInfo(){
        return R.data(deptService.operationOrgInfo());
    }


}
