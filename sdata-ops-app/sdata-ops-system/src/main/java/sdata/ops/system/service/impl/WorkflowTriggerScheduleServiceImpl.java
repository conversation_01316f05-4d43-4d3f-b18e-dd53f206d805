package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasic;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.base.system.model.enums.TriggerScheduleFrequency;
import sdata.ops.common.api.R;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import sdata.ops.system.service.OpsTaskAttrBasicService;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;
import sdata.ops.system.service.OpsTaskGenInfoService;
import sdata.ops.system.service.WorkflowTriggerScheduleService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流触发器调度服务实现
 * 6-22点每半小时执行一次，轮询所有配置了工作流绑定的任务单元
 * 根据调度频率和当前时间决定是否实际调用工作流
 * 
 * <AUTHOR>
 * @since 2024-09-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowTriggerScheduleServiceImpl implements WorkflowTriggerScheduleService {

    private final OpsTaskAttrBasicService taskAttrBasicService;
    private final OpsTaskAttrBasicReplicaService taskAttrBasicReplicaService;
    private final IndicatorInfoFeign indicatorInfoFeign;
    private final OpsTaskGenInfoService taskGenInfoService;

    @Override
    public String executeWorkflowTriggers() {
        log.info("工作流触发器主调度任务开始执行");
        
        try {
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();
            int currentHour = currentTime.getHour();
            int currentMinute = currentTime.getMinute();
            
            log.info("当前时间: {}:{}", currentHour, currentMinute);
            
            // 查询所有配置了工作流绑定且已上线的任务单元（主表）
            LambdaQueryWrapper<OpsTaskAttrBasic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(OpsTaskAttrBasic::getWorkflowId)
                       .ne(OpsTaskAttrBasic::getWorkflowId, "")
                       .isNotNull(OpsTaskAttrBasic::getTriggerScheduleFrequency)
                       .eq(OpsTaskAttrBasic::getTaskStatus, 1); // 只检查已上线的任务单元

            List<OpsTaskAttrBasic> taskUnits = taskAttrBasicService.list(queryWrapper);
            log.info("找到 {} 个配置了工作流绑定且已上线的任务单元（主表）", taskUnits.size());

            // 查询所有配置了工作流绑定且已上线的任务单元（副本表）
            LambdaQueryWrapper<OpsTaskAttrBasicReplica> replicaQueryWrapper = new LambdaQueryWrapper<>();
            replicaQueryWrapper.isNotNull(OpsTaskAttrBasicReplica::getWorkflowId)
                              .ne(OpsTaskAttrBasicReplica::getWorkflowId, "")
                              .isNotNull(OpsTaskAttrBasicReplica::getTriggerScheduleFrequency)
                              .eq(OpsTaskAttrBasicReplica::getTaskStatus, 1); // 只检查已上线的任务单元

            List<OpsTaskAttrBasicReplica> replicaTaskUnits = taskAttrBasicReplicaService.list(replicaQueryWrapper);
            log.info("找到 {} 个配置了工作流绑定且已上线的任务单元（副本表）", replicaTaskUnits.size());

            if (taskUnits.isEmpty() && replicaTaskUnits.isEmpty()) {
                String message = "没有找到配置了工作流绑定且已上线的任务单元，跳过执行";
                log.info(message);
                return message;
            }
            
            int executedCount = 0;
            int skippedCount = 0;

            // 处理主表任务单元
            for (OpsTaskAttrBasic taskUnit : taskUnits) {
                int[] counts = processTaskUnit(taskUnit, currentTime, currentHour, currentMinute);
                executedCount += counts[0];
                skippedCount += counts[1];
            }

            // 处理副本表任务单元
            for (OpsTaskAttrBasicReplica replicaTaskUnit : replicaTaskUnits) {
                int[] counts = processReplicaTaskUnit(replicaTaskUnit, currentTime, currentHour, currentMinute);
                executedCount += counts[0];
                skippedCount += counts[1];
            }

            String message = String.format("工作流触发器主调度任务执行完成，共处理 %d 个任务单元，执行 %d 个，跳过 %d 个",
                    taskUnits.size() + replicaTaskUnits.size(), executedCount, skippedCount);
            log.info(message);
            return message;
        } catch (Exception e) {
            String errorMessage = "工作流触发器主调度任务执行异常: " + e.getMessage();
            log.error(errorMessage, e);
            return errorMessage;
        }
    }

    /**
     * 处理主表任务单元
     *
     * @param taskUnit 任务单元
     * @param currentTime 当前时间
     * @param currentHour 当前小时
     * @param currentMinute 当前分钟
     * @return [执行数量, 跳过数量]
     */
    private int[] processTaskUnit(OpsTaskAttrBasic taskUnit, LocalDateTime currentTime, int currentHour, int currentMinute) {
        try {
            if (shouldExecuteWorkflow(taskUnit.getTriggerScheduleFrequency(), currentHour, currentMinute)) {
                log.info("执行任务单元 {} 的工作流触发器，工作流ID: {}",
                        taskUnit.getTaskName(), taskUnit.getWorkflowId());

                // 调用工作流
                Map<String, Object> variables = new HashMap<>();
                variables.put("taskUnitId", taskUnit.getId());
                variables.put("taskUnitName", taskUnit.getTaskName());
                variables.put("taskUnitType", "basic"); // 标识来源表
                variables.put("scheduleTime", currentTime.toString());

                R<WorkflowTriggerResultVO> result = indicatorInfoFeign.executeWorkflowForTrigger(
                        taskUnit.getWorkflowId(), variables);

                if (result.isSuccess() && result.getData() != null) {
                    WorkflowTriggerResultVO resultVO = result.getData();
                    log.info("工作流执行完成，触发结果: {}, 任务单元: {}",
                            resultVO.getTriggerResult(), taskUnit.getTaskName());

                    if (Boolean.TRUE.equals(resultVO.getTriggerResult())) {
                        log.info("工作流返回true，应该创建任务，任务单元: {}", taskUnit.getTaskName());

                        try {
                            // 创建任务
                            createTaskFromWorkflowTrigger(taskUnit, resultVO);
                            log.info("工作流触发器创建任务成功，任务单元: {}", taskUnit.getTaskName());
                        } catch (Exception e) {
                            log.error("工作流触发器创建任务失败，任务单元: {}, 错误: {}", taskUnit.getTaskName(), e.getMessage(), e);
                        }
                    } else {
                        log.info("工作流返回false，不创建任务，任务单元: {}", taskUnit.getTaskName());
                    }
                } else {
                    log.error("工作流执行失败，任务单元: {}, 错误信息: {}",
                            taskUnit.getTaskName(), result.getMessage());
                }

                return new int[]{1, 0}; // 执行了1个，跳过0个
            } else {
                log.debug("跳过任务单元 {} 的工作流触发器，频率: {}",
                        taskUnit.getTaskName(), taskUnit.getTriggerScheduleFrequency());
                return new int[]{0, 1}; // 执行了0个，跳过1个
            }
        } catch (Exception e) {
            log.error("处理任务单元 {} 时发生异常", taskUnit.getTaskName(), e);
            return new int[]{0, 0}; // 异常情况
        }
    }

    /**
     * 处理副本表任务单元
     *
     * @param replicaTaskUnit 副本任务单元
     * @param currentTime 当前时间
     * @param currentHour 当前小时
     * @param currentMinute 当前分钟
     * @return [执行数量, 跳过数量]
     */
    private int[] processReplicaTaskUnit(OpsTaskAttrBasicReplica replicaTaskUnit, LocalDateTime currentTime, int currentHour, int currentMinute) {
        try {
            if (shouldExecuteWorkflow(replicaTaskUnit.getTriggerScheduleFrequency(), currentHour, currentMinute)) {
                log.info("执行副本任务单元 {} 的工作流触发器，工作流ID: {}",
                        replicaTaskUnit.getTaskName(), replicaTaskUnit.getWorkflowId());

                // 调用工作流
                Map<String, Object> variables = new HashMap<>();
                variables.put("taskUnitId", replicaTaskUnit.getId());
                variables.put("taskUnitName", replicaTaskUnit.getTaskName());
                variables.put("taskUnitType", "replica"); // 标识来源表
                variables.put("scheduleTime", currentTime.toString());

                R<WorkflowTriggerResultVO> result = indicatorInfoFeign.executeWorkflowForTrigger(
                        replicaTaskUnit.getWorkflowId(), variables);

                if (result.isSuccess() && result.getData() != null) {
                    WorkflowTriggerResultVO resultVO = result.getData();
                    log.info("工作流执行完成，触发结果: {}, 副本任务单元: {}",
                            resultVO.getTriggerResult(), replicaTaskUnit.getTaskName());

                    if (Boolean.TRUE.equals(resultVO.getTriggerResult())) {
                        log.info("工作流返回true，应该创建任务，副本任务单元: {}", replicaTaskUnit.getTaskName());

                        try {
                            // 创建任务（副本表暂时使用相同的创建逻辑，后续可以扩展）
                            createTaskFromReplicaWorkflowTrigger(replicaTaskUnit, resultVO);
                            log.info("工作流触发器创建任务成功，副本任务单元: {}", replicaTaskUnit.getTaskName());
                        } catch (Exception e) {
                            log.error("工作流触发器创建任务失败，副本任务单元: {}, 错误: {}", replicaTaskUnit.getTaskName(), e.getMessage(), e);
                        }
                    } else {
                        log.info("工作流返回false，不创建任务，副本任务单元: {}", replicaTaskUnit.getTaskName());
                    }
                } else {
                    log.error("工作流执行失败，副本任务单元: {}, 错误信息: {}",
                            replicaTaskUnit.getTaskName(), result.getMessage());
                }

                return new int[]{1, 0}; // 执行了1个，跳过0个
            } else {
                log.debug("跳过副本任务单元 {} 的工作流触发器，频率: {}",
                        replicaTaskUnit.getTaskName(), replicaTaskUnit.getTriggerScheduleFrequency());
                return new int[]{0, 1}; // 执行了0个，跳过1个
            }
        } catch (Exception e) {
            log.error("处理副本任务单元 {} 时发生异常", replicaTaskUnit.getTaskName(), e);
            return new int[]{0, 0}; // 异常情况
        }
    }
    
    /**
     * 根据调度频率和当前时间判断是否应该执行工作流
     *
     * @param frequency 调度频率
     * @param currentHour 当前小时
     * @param currentMinute 当前分钟
     * @return 是否应该执行
     */
    private boolean shouldExecuteWorkflow(TriggerScheduleFrequency frequency, int currentHour, int currentMinute) {
        if (frequency == null) {
            return false;
        }

        switch (frequency) {
            case THIRTY_MINUTES:
                // 30分钟频率：每次主调度都执行（因为主调度就是30分钟一次）
                return currentMinute == 0 || currentMinute == 30;

            case ONE_HOUR:
                // 1小时频率：只在整点执行（6:00, 7:00, 8:00...）
                return currentMinute == 0;

            case TWO_HOURS:
                // 2小时频率：只在偶数小时的整点执行（6:00, 8:00, 10:00...）
                return currentMinute == 0 && currentHour % 2 == 0;

            default:
                log.warn("未知的调度频率: {}", frequency);
                return false;
        }
    }

    /**
     * 从工作流触发器创建任务
     *
     * @param taskUnit 任务单元
     * @param workflowResult 工作流执行结果
     */
    private void createTaskFromWorkflowTrigger(OpsTaskAttrBasic taskUnit, WorkflowTriggerResultVO workflowResult) {
        // 使用现有的convertGenInfo方法将任务单元转换为任务
        OpsTaskGenInfo taskGenInfo = taskGenInfoService.convertGenInfo(taskUnit);

        // 如果工作流返回了任务信息，则覆盖默认配置
        if (workflowResult.getTaskInfo() != null && !workflowResult.getTaskInfo().isEmpty()) {
            applyWorkflowTaskInfo(taskGenInfo, workflowResult.getTaskInfo());
        }

        // 保存任务
        taskGenInfoService.save(taskGenInfo);

        log.info("工作流触发器创建任务成功，任务ID: {}, 任务名称: {}", taskGenInfo.getId(), taskGenInfo.getTaskName());
    }

    /**
     * 从副本表工作流触发器创建任务
     *
     * @param replicaTaskUnit 副本任务单元
     * @param workflowResult 工作流执行结果
     */
    private void createTaskFromReplicaWorkflowTrigger(OpsTaskAttrBasicReplica replicaTaskUnit, WorkflowTriggerResultVO workflowResult) {
        // 使用现有的 convertGenInfo 方法将副本任务单元转换为任务
        OpsTaskGenInfo taskGenInfo = taskGenInfoService.convertGenInfo(replicaTaskUnit);

        // 如果工作流返回了任务信息，则覆盖默认配置
        if (workflowResult.getTaskInfo() != null && !workflowResult.getTaskInfo().isEmpty()) {
            applyWorkflowTaskInfo(taskGenInfo, workflowResult.getTaskInfo());
        }

        // 保存任务
        taskGenInfoService.save(taskGenInfo);

        log.info("副本表工作流触发器创建任务成功，任务ID: {}, 任务名称: {}", taskGenInfo.getId(), taskGenInfo.getTaskName());
    }

    /**
     * 应用工作流返回的任务信息覆盖默认配置
     *
     * @param taskGenInfo 任务信息
     * @param taskInfo 工作流返回的任务信息
     */
    private void applyWorkflowTaskInfo(OpsTaskGenInfo taskGenInfo, Map<String, Object> taskInfo) {
        log.info("应用工作流返回的任务信息: {}", taskInfo);

        // 任务名称
        if (taskInfo.containsKey("taskName")) {
            String taskName = String.valueOf(taskInfo.get("taskName"));
            if (taskName != null && !taskName.trim().isEmpty()) {
                taskGenInfo.setTaskName(taskName);
                log.info("工作流覆盖任务名称: {}", taskName);
            }
        }

        // 任务描述
        if (taskInfo.containsKey("taskDesc")) {
            String taskDesc = String.valueOf(taskInfo.get("taskDesc"));
            if (taskDesc != null && !taskDesc.trim().isEmpty()) {
                taskGenInfo.setTaskDesc(taskDesc);
                log.info("工作流覆盖任务描述: {}", taskDesc);
            }
        }

        // 任务优先级
        if (taskInfo.containsKey("taskPriority")) {
            String taskPriority = String.valueOf(taskInfo.get("taskPriority"));
            if (taskPriority != null && !taskPriority.trim().isEmpty()) {
                taskGenInfo.setTaskPriority(taskPriority);
                log.info("工作流覆盖任务优先级: {}", taskPriority);
            }
        }

        // 任务等级
        if (taskInfo.containsKey("taskLevel")) {
            String taskLevel = String.valueOf(taskInfo.get("taskLevel"));
            if (taskLevel != null && !taskLevel.trim().isEmpty()) {
                taskGenInfo.setTaskLevel(taskLevel);
                log.info("工作流覆盖任务等级: {}", taskLevel);
            }
        }

        // 任务归属
        if (taskInfo.containsKey("taskOwnerVal")) {
            String taskOwnerVal = String.valueOf(taskInfo.get("taskOwnerVal"));
            if (taskOwnerVal != null && !taskOwnerVal.trim().isEmpty()) {
                taskGenInfo.setTaskOwnerVal(taskOwnerVal);
                log.info("工作流覆盖任务归属: {}", taskOwnerVal);
            }
        }

        // 复核人员
        if (taskInfo.containsKey("taskCheckVal")) {
            String taskCheckVal = String.valueOf(taskInfo.get("taskCheckVal"));
            if (taskCheckVal != null && !taskCheckVal.trim().isEmpty()) {
                taskGenInfo.setTaskCheckVal(taskCheckVal);
                log.info("工作流覆盖复核人员: {}", taskCheckVal);
            }
        }

        // 任务标签
        if (taskInfo.containsKey("taskTags")) {
            String taskTags = String.valueOf(taskInfo.get("taskTags"));
            if (taskTags != null && !taskTags.trim().isEmpty()) {
                taskGenInfo.setTaskTags(taskTags);
                log.info("工作流覆盖任务标签: {}", taskTags);
            }
        }

        log.info("工作流任务信息应用完成");
    }
}
