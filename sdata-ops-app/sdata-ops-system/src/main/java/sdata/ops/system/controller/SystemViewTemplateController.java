package sdata.ops.system.controller;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.entity.OpsViewTemplate;
import sdata.ops.base.system.model.vo.OpsViewTemplateVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsViewGroupRelationService;
import sdata.ops.system.service.OpsViewTemplateService;
import sdata.ops.system.service.SystemUserService;

import java.util.HashMap;
import java.util.Map;

@RequestMapping("/view-template")
@RestController
@RequiredArgsConstructor
public class SystemViewTemplateController {


    private final OpsViewTemplateService opsViewTemplateService;


    private final SystemUserService userService;

    private final OpsViewGroupRelationService relationService;
    @PostMapping("/save")
    public R<Object> saveView(@RequestBody JSONObject view) {
        OpsViewTemplate viewEntity = JSONUtil.toBean(view, OpsViewTemplate.class);
        opsViewTemplateService.saveOrUpdate(viewEntity);
        return R.data(new OpsViewTemplateVO().convert(viewEntity,userService.findNameIdMapping(),null));
    }

    @GetMapping("/get")
    public R<Object> getView(@RequestParam(value = "id") String id) {
        return R.data(new OpsViewTemplateVO().convert(opsViewTemplateService.getById(id),userService.findNameIdMapping(),null));
    }

    @GetMapping("/delete")
    public R<Object> deleteView(@RequestParam("id") String id) {
        opsViewTemplateService.removeById(id);
        return R.data(MessageConstant.OPERATOR_SUCCESS);
    }

    @GetMapping("/page")
    public R<Object> pageView(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                              String name,
                              @RequestParam(value = "groupId", required = false) Integer groupId) {
        IPage<OpsViewTemplate> pageEntity = new Page<>(pageNo, pageSize);
        IPage<OpsViewTemplate> res = opsViewTemplateService.pageCustom(pageEntity, groupId, name);
        return R.data(customPage(res));
    }

    private Map<String,Object> customPage(IPage<OpsViewTemplate> res) {
        Map<String,Object> finalRes = new HashMap<>();
        finalRes.put("total", res.getTotal());
        finalRes.put("records", res.convert(i -> new OpsViewTemplateVO().convert(i,userService.findNameIdMapping(),relationService.templateIdGroupMapper())).getRecords());
        finalRes.put("pageNo", res.getCurrent());
        finalRes.put("pageSize", res.getSize());
        finalRes.put("isLastPage", res.getPages() == 0 || res.getPages() == res.getCurrent());
        return finalRes;
    }

    @PostMapping("/saveRelation")
    public R<Object> saveRelation(@RequestBody OpsViewGroupRelation ls) {
        opsViewTemplateService.saveRelation(ls);
        return R.data(MessageConstant.OPERATOR_SUCCESS);
    }
}
