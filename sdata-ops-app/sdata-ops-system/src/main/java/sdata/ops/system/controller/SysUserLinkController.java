package sdata.ops.system.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.UserLinkDTO;
import sdata.ops.base.system.model.entity.UserLink;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.UserLinkService;

import java.util.List;

@RestController
@RequestMapping("/sys/links")
@RequiredArgsConstructor
public class SysUserLinkController {

    private final UserLinkService userLinkService;

    @GetMapping
    public R<List<UserLink>> list() {
        Long userId = Long.valueOf(SecureUtil.currentUserId());
        return R.data(userLinkService.listByUserId(userId));
    }

    @PostMapping("add")
    public R<Void> add(@RequestBody UserLinkDTO dto) {
        userLinkService.addLink(dto);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    @PostMapping("/modify/{id}")
    public R<Void> update(@PathVariable Long id, @RequestBody UserLinkDTO dto) {
        userLinkService.updateLink(id, dto);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @GetMapping("/deleted/{id}")
    public R<Void> delete(@PathVariable Long id) {
        userLinkService.deleteLink(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}
