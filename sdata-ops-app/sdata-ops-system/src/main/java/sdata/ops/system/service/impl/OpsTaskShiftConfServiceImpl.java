package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.*;
import sdata.ops.system.mapper.OpsTaskShiftConfMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskShiftConfServiceImpl extends ServiceImpl<OpsTaskShiftConfMapper, OpsTaskShiftConf>
        implements OpsTaskShiftConfService {


    private final SystemUserService userService;

    private final OpsSysUserOrgService userOrgService;

    private final OpsTaskTemplateService taskTemplateService;

    private final OpsTaskAttrBasicReplicaService replicaService;

    private final OpsShiftConfListService opsShiftConfListService;

    @Override
    public List<SystemUser> findUserArrByLeaderId(String userId) {
        if (SecureUtil.isAdmin(null)) {
            return userService.list();
        }
        return userService.findUserByLeaderId(userId);
    }

    @Override
    public List<OpsTaskShiftConf> listByUserType() {
        if (SecureUtil.isAdmin(null)) {
            return list();
        }
        List<SystemUser> systemUsers = findUserArrByLeaderId(SecureUtil.currentUserId());
        if (systemUsers.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> ids = systemUsers.stream().map(SystemUser::getId).collect(Collectors.toList());
        LambdaQueryWrapper<OpsTaskShiftConf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskShiftConf::getFromId, ids);
        return list(queryWrapper);
    }

    /**
     * 任务转交
     *
     * @param conf 转交人员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationShiftConf(OpsTaskShiftConf conf) {
        long count = count(Wrappers.lambdaQuery(OpsTaskShiftConf.class).eq(OpsTaskShiftConf::getFromId, conf.getFromId()));
        if (count > 0) {
            throw new RuntimeException("用户已有交接配置,不允许重复操作!");
        }
        String fromId = conf.getFromId();
        String toId = conf.getToId();
        String toUser = conf.getToUser();
        String shiftId = String.valueOf(IdWorker.getId());
        log.error("开始查询");
        //先查询需要转交人员的模板中任务单元信息，上线的模板的任务单元包括经办与复核
        List<String> templateIds = taskTemplateService.list(Wrappers.lambdaQuery(OpsTaskTemplate.class).eq(OpsTaskTemplate::getTemplateStatus, 1)).stream().map(OpsTaskTemplate::getId).collect(Collectors.toList());
        List<OpsTaskAttrBasicReplica> arrayByTask = replicaService.queryReplicaListByTemplateIdAndUserIdWithOwner(templateIds, fromId);
        if (arrayByTask == null || arrayByTask.isEmpty()) {
            throw new RuntimeException("该用户没有任务配置信息!");
        }
        //自动新增接收人不存在的岗位信息
        List<String> diff = autoInsertOrgInfo(fromId, toId);

        //然后调整为交接人员信息
        List<OpsShiftConfList> arr = new ArrayList<>();
        if(!diff.isEmpty()){
            for (String s : diff) {
                OpsShiftConfList item = new OpsShiftConfList();
                item.setTaskType(2);
                item.setTaskId(s);
                item.setUserId(fromId);
                item.setShiftId(shiftId);
                arr.add(item);
            }
        }
        for (OpsTaskAttrBasicReplica replica : arrayByTask) {
            OpsShiftConfList item = new OpsShiftConfList();
            if (replica.getTaskOwnerId().equals(fromId)) {
                replica.setTaskOwnerVal(toUser);
                replica.setTaskOwnerId(toId);
                item.setTaskType(0);
                item.setTaskId(replica.getId());
                item.setUserId(fromId);
                item.setShiftId(shiftId);
                arr.add(item);
                continue;
            }
            if (replica.getTaskCheckId().equals(fromId)) {
                replica.setTaskCheckVal(toUser);
                replica.setTaskCheckId(toId);
                item.setTaskType(1);
                item.setTaskId(replica.getId());
                item.setUserId(fromId);
                item.setShiftId(shiftId);
                arr.add(item);
            }
        }
        log.error("数据更新完成");

        //提交
        replicaService.updateBatchById(arrayByTask);
        log.error("数据批量更新完成");

        //新增转交记录
        OpsTaskShiftConf insert = new OpsTaskShiftConf();
        insert.setId(Long.parseLong(shiftId));
        insert.setCreateTime(new Date());
        insert.setFromId(fromId);
        insert.setFromUser(conf.getFromUser());
        insert.setToId(toId);
        insert.setToUser(toUser);
        insert.setExpireDay(null);
        save(insert);
        opsShiftConfListService.saveBatch(arr);
        log.error("数据新增配置记录完成");
    }

    private List<String> autoInsertOrgInfo(String fromId, String toId) {
        List<String> diff = userOrgService.diffInfoBy(fromId, toId);
        if (diff == null || diff.isEmpty()) {
            return new ArrayList<>();
        }
        List<OpsSysUserOrg> arr = new ArrayList<>();
        for (String s : diff) {
            OpsSysUserOrg org = new OpsSysUserOrg();
            org.setOrgId(Long.valueOf(s));
            org.setUserId(Long.valueOf(toId));
            arr.add(org);
        }
        userOrgService.saveBatch(arr);
        return diff;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetConf(String id) {
        OpsTaskShiftConf conf = getById(id);
        //查询配置单元表
        List<OpsShiftConfList> list = opsShiftConfListService.list(Wrappers.lambdaQuery(OpsShiftConfList.class)
                .eq(OpsShiftConfList::getShiftId, conf.getId()));
        //更新任务单元表
        Map<String, OpsShiftConfList> mapping = list.stream().collect(Collectors.toMap(OpsShiftConfList::getTaskId, i -> i, (n1, n2) -> n1));
        //查询任务单元表
        List<String> ids = new ArrayList<>(mapping.keySet());
        List<OpsTaskAttrBasicReplica> replicas = replicaService.listByIds(ids);
        //重置信息
        List<OpsSysUserOrg> orgDel = new ArrayList<>();
        for (OpsTaskAttrBasicReplica replica : replicas) {
            if (mapping.containsKey(replica.getId())) {
                if (mapping.get(replica.getId()).getTaskType() == 0) {
                    replica.setTaskOwnerId(conf.getFromId());
                    replica.setTaskOwnerVal(conf.getFromUser());
                    continue;
                }
                if (mapping.get(replica.getId()).getTaskType() == 1) {
                    replica.setTaskCheckId(conf.getFromId());
                    replica.setTaskCheckVal(conf.getFromUser());
                    continue;
                }
                if (mapping.get(replica.getId()).getTaskType() == 2) {
                    OpsSysUserOrg sk = new OpsSysUserOrg();
                    sk.setUserId(Long.valueOf(conf.getToId()));
                    sk.setOrgId(Long.valueOf(mapping.get(replica.getId()).getTaskId()));
                    orgDel.add(sk);
                }
            }
        }
        //更新数据
        replicaService.updateBatchById(replicas);
        //清理垃圾数据
        opsShiftConfListService.remove(Wrappers.lambdaQuery(OpsShiftConfList.class).eq(OpsShiftConfList::getShiftId, id));
        //删除记录
        removeById(id);
        //删除org数据
        removeOrg(orgDel);
    }

    private void removeOrg(List<OpsSysUserOrg> orgDel) {
        if(orgDel.isEmpty()){
            return;
        }
        for (OpsSysUserOrg org : orgDel) {
            LambdaQueryWrapper<OpsSysUserOrg> del=new LambdaQueryWrapper<>();
            del.eq(OpsSysUserOrg::getOrgId,org.getOrgId());
            del.eq(OpsSysUserOrg::getUserId,org.getUserId());
            userOrgService.remove(del);
        }
    }
}




