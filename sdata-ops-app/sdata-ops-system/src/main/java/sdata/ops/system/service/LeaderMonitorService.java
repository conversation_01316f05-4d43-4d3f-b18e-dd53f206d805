package sdata.ops.system.service;

public interface LeaderMonitorService {
    Object warnList(String date);

    /**
     * 超时列表v1
     *
     * @param date 业务日期
     * @return
     */
    Object warnListV1(String date);

    Object dailyList(String date);

    /**
     * 日常任务列表v1
     *
     * @param date 业务日期
     * @return
     */
    Object dailyListV1(String date);

    Object tempList(String date);
}
