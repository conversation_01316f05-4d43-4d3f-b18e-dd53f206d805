package sdata.ops.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsTaskJobRelation;

/**
* <AUTHOR>
* @description 针对表【ops_task_job_relation】的数据库操作Mapper
* @createDate 2024-07-05 15:39:29
* @Entity generator.domain.OpsTaskJobRelation
*/
@Mapper
public interface OpsTaskJobRelationMapper extends BaseMapper<OpsTaskJobRelation> {

    @Delete("delete from ops_task_job_relation  where task_id in (" +
            "   select task_replica_id from ops_task_template_relation where template_id =#{id})")
    void deleteTaskReplicaIdsByTemplateId(String id);
}




