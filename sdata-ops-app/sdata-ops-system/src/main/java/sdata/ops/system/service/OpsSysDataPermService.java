package sdata.ops.system.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import sdata.ops.base.system.model.entity.OpsSysDataPerm;
import sdata.ops.base.system.model.vo.OpsPermSaveVO;

/**
* <AUTHOR>
* @description 针对表【ops_sys_data_perm(数据授权信息表)】的数据库操作Service
* @createDate 2024-07-02 16:27:27
*/
public interface OpsSysDataPermService extends IService<OpsSysDataPerm> {

    void saveOrUpdateAllConf(OpsPermSaveVO perm);

    void deletedOperation(String id);

    JSONObject getDataPermById(String id, String userId);

    OpsPermSaveVO queryDetail(String id);
}
