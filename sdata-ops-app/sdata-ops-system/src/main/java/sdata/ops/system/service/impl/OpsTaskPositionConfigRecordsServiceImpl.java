package sdata.ops.system.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.ConditionTaskDTO;
import sdata.ops.base.system.model.dto.PositionConfigDTO;
import sdata.ops.base.system.model.dto.PositionMultiStepDTO;
import sdata.ops.base.system.model.dto.PositionStepDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.OrgUserVO;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.mapper.OpsTaskPositionConfigRecordsMapper;
import sdata.ops.system.service.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OpsTaskPositionConfigRecordsServiceImpl extends ServiceImpl<OpsTaskPositionConfigRecordsMapper, OpsTaskPositionConfigRecords>
        implements OpsTaskPositionConfigRecordsService {


    private final OpsTaskGenInfoService opsTaskGenInfoService;


    private final OpsTaskTemplateService taskTemplateService;

    private final OpsTaskAttrBasicReplicaService basicReplica;

    private final OpsSysUserOrgService userOrgService;

    private final OpsShiftConfListService opsShiftConfListService;

    private final OpsTaskAttrBasicSlaveService slaveService;

    private final SystemUserService userService;


    @Override
    public List<OpsTaskPositionConfigRecords> confList() {
        LambdaQueryWrapper<OpsTaskPositionConfigRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskPositionConfigRecords::getState, 1);
        queryWrapper.eq(OpsTaskPositionConfigRecords::getDeleted, 0);
        if (!SecureUtil.isAdmin(null)) {
            //leader查询部门下所有岗位进行调整
            ConditionTaskDTO filter = opsTaskGenInfoService.taskSpecialAuthFilter(null);
            if (filter.getPostIds().isEmpty()) {
                return new ArrayList<>();
            }
            queryWrapper.in(OpsTaskPositionConfigRecords::getOrgId, filter.getPostIds());
        }
        Map<String, String> map = taskTemplateService.list(Wrappers.lambdaQuery(OpsTaskTemplate.class).select(OpsTaskTemplate::getTemplateName, OpsTaskTemplate::getId)).stream().
                collect(Collectors.toMap(OpsTaskTemplate::getId, OpsTaskTemplate::getTemplateName));
        return list(queryWrapper).stream().peek(i -> i.setTemplateName(map.get(i.getTemplateId()))).collect(Collectors.toList());
    }

    @Override
    public boolean checkIsExisit(PositionConfigDTO configDTO) {
        LambdaQueryWrapper<OpsTaskPositionConfigRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskPositionConfigRecords::getTemplateId, configDTO.getTemplateId());
        queryWrapper.eq(OpsTaskPositionConfigRecords::getDeleted, 0);
        queryWrapper.eq(OpsTaskPositionConfigRecords::getState, 1);
        return !list(queryWrapper).isEmpty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confAdd(PositionConfigDTO configDTO) {
        //过滤
        //先进行转派排序
        configDTO.getConfigs().sort(Comparator.comparing(PositionMultiStepDTO::getSort));
        //预生成本次操作id
        Long recordId = IdWorker.getId();
        OpsTaskPositionConfigRecords records = new OpsTaskPositionConfigRecords();
        records.setId(String.valueOf(recordId));
        records.setReplaced(configDTO.getReplaced());
        records.setCreateTime(new Date());
        records.setOrgId(configDTO.getOrgId());
        records.setOrgName(configDTO.getOrgName());
        records.setTemplateId(configDTO.getTemplateId());
        records.setPreviewStr(configDTO.getPreviewStr());
        Integer replaced = configDTO.getReplaced();
        //获取人员id-name
        Map<String, String> idName = userService.findNameIdMapping();
        //备份模板
        List<OpsTaskAttrBasicReplica> basicUnits = basicReplica.queryListByTemplateId(configDTO.getTemplateId());
        List<OpsTaskAttrBasicSlave> slaves = basicUnits.stream().map(i -> new OpsTaskAttrBasicSlave().convert(i, recordId)).collect(Collectors.toList());
        //人员-替换(分经办和复核)
        List<PositionStepDTO> permProcess = new ArrayList<>();
        List<OpsTaskAttrBasicReplica> hits = new ArrayList<>();
        //当天已经生成的任务是否需要同样交接(如果需要)
        List<OpsTaskGenInfo> currentTask = new ArrayList<>();
        Map<String, OpsTaskGenInfo> mappingGen = new HashMap<>();
        if (configDTO.getCurrentTaskIsTransfer() == 1) {
            List<OpsTaskGenInfo> taskInfo = opsTaskGenInfoService.listByTemplateIdAndNowDate(configDTO.getTemplateId(), DateUtil.format(new Date(), "yyyy-MM-dd"));
            mappingGen = taskInfo.stream().collect(Collectors.toMap(OpsTaskGenInfo::getTaskRefId, i -> i));
        }
        for (PositionMultiStepDTO stepOut : configDTO.getConfigs()) {
            for (PositionStepDTO step : stepOut.getSteps()) {
                if(StringUtils.isBlank(step.getProxyId())){
                    continue;
                }
                //如果是新增权限,且替岗和被替岗人权限不同情况下
                if (replaced == 1 && !Objects.equals(step.getFromOrgId(), step.getProxyOrgId())) {
                    permProcess.add(step);
                } else {
                    permProcess.add(step);
                }
                Iterator<OpsTaskAttrBasicReplica> iteratorRe = basicUnits.iterator();
                while (iteratorRe.hasNext()) {
                    OpsTaskAttrBasicReplica replica = iteratorRe.next();
                    if (step.getType() == 1 && replica.getTaskOwnerId().equals(step.getFromId()) && stepOut.getTaskIds().contains(replica.getId())) {
                        OpsTaskAttrBasicReplica res = new OpsTaskAttrBasicReplica();
                        BeanUtil.copyProperties(replica, res);
                        res.setTaskOwnerVal(idName.get(step.getProxyId()));
                        res.setTaskOwnerId(step.getProxyId());
                        hits.add(res);
                        taskInfoModify(currentTask, mappingGen, res, step.getType());
                        iteratorRe.remove();
                        continue;
                    }
                    if (step.getType() == 2 && replica.getTaskCheckId().equals(step.getFromId()) && stepOut.getTaskIds().contains(replica.getId())) {
                        OpsTaskAttrBasicReplica res = new OpsTaskAttrBasicReplica();
                        BeanUtil.copyProperties(replica, res);
                        res.setTaskOwnerVal(idName.get(step.getProxyId()));
                        res.setTaskOwnerId(step.getProxyId());
                        hits.add(res);
                        iteratorRe.remove();
                        taskInfoModify(currentTask, mappingGen, res, step.getType());
                    }
                }
            }
        }
        basicUnits.addAll(hits);

        //处理权限 replaced =1 情况是新增权限  =2 情况是先删除自身权限 再去新增权限
        List<OpsShiftConfList> store = new ArrayList<>();
        if (replaced == 1) {
            Map<String, List<String>> cap = new HashMap<>();
            for (PositionStepDTO process : permProcess) {
                OpsShiftConfList item = new OpsShiftConfList();
                item.setShiftId(String.valueOf(recordId));
                item.setUserId(process.getProxyId());
                item.setTaskId(process.getFromOrgId());
                store.add(item);
                if (!cap.containsKey(process.getFromOrgId())) {
                    if (cap.get(process.getFromOrgId()) == null) {
                        List<String> s = new ArrayList<>();
                        s.add(process.getProxyId());
                        cap.put(process.getFromOrgId(), s);
                    }
                } else {
                    cap.get(process.getFromOrgId()).add(process.getProxyId());
                }
            }
            //岗位新增-如果替岗人岗位与被替岗人岗位权限范围相等，则不会重复新增
            cap.forEach((k, v) -> {
                OrgUserVO orgUserVO = new OrgUserVO();
                orgUserVO.setUserIds(v);
                orgUserVO.setOrgId(k);
                userOrgService.saveUpdateRelation(orgUserVO);
            });
        }
        if (replaced == 2) {
            //删除自身权限- 新增配置权限- 删除权限需要留存(重置需要恢复原有岗位权限设置)
            List<String> userIds = new ArrayList<>();
            Map<String, List<String>> cap = new HashMap<>();
            for (PositionStepDTO process : permProcess) {
                userIds.add(process.getProxyId());
                if (!cap.containsKey(process.getFromOrgId())) {
                    if (cap.get(process.getFromOrgId()) == null) {
                        List<String> s = new ArrayList<>();
                        s.add(process.getProxyId());
                        cap.put(process.getFromOrgId(), s);
                    }
                } else {
                    cap.get(process.getFromOrgId()).add(process.getProxyId());
                }
            }

            List<OpsSysUserOrg> as = userOrgService.list(Wrappers.lambdaQuery(OpsSysUserOrg.class).in(OpsSysUserOrg::getUserId, userIds));
            for (OpsSysUserOrg a : as) {
                OpsShiftConfList item = new OpsShiftConfList();
                item.setShiftId(String.valueOf(recordId));
                item.setUserId(String.valueOf(a.getUserId()));
                item.setTaskId(String.valueOf(a.getOrgId()));
                store.add(item);
            }
            //删除再新增
            userOrgService.remove(Wrappers.lambdaQuery(OpsSysUserOrg.class).in(OpsSysUserOrg::getUserId, userIds));
            //岗位新增-如果替岗人岗位与被替岗人岗位权限范围相等，则不会重复新增
            cap.forEach((k, v) -> {
                OrgUserVO orgUserVO = new OrgUserVO();
                orgUserVO.setUserIds(v);
                orgUserVO.setOrgId(k);
                userOrgService.saveUpdateRelation(orgUserVO);
            });

        }

        //存储操作记录
        //存储模板内容副本
        //存储岗位权限变化记录
        //更新模板任务表
        save(records);
        slaveService.saveBatch(slaves);
        opsShiftConfListService.saveBatch(store);
        basicReplica.updateBatchById(basicUnits);
        if (!currentTask.isEmpty())
            opsTaskGenInfoService.updateBatchById(currentTask);
    }

    //任务更改
    private void taskInfoModify(List<OpsTaskGenInfo> currentTask, Map<String, OpsTaskGenInfo> mappingGen, OpsTaskAttrBasicReplica res, int type) {
        if(mappingGen.isEmpty()){
            return;
        }
        if (mappingGen.containsKey(res.getId()) && mappingGen.get(res.getId()).getTaskCompleteStatus() != 3) {
            OpsTaskGenInfo info = mappingGen.get(res.getId());
            if (type == 1) {
                info.setTaskOwnerVal(res.getTaskOwnerVal());
                info.setTaskOwnerId(res.getTaskOwnerId());
            }
            if (type == 2) {
                info.setTaskCheckId(res.getTaskCheckId());
                info.setTaskCheckVal(res.getTaskCheckVal());
            }
            currentTask.add(info);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confOverwrite(String id) {
        //更改内容
        LambdaUpdateWrapper<OpsTaskPositionConfigRecords> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsTaskPositionConfigRecords::getDeleted, 1);
        updateWrapper.set(OpsTaskPositionConfigRecords::getState, 3);
        updateWrapper.eq(OpsTaskPositionConfigRecords::getId, id);
        update(updateWrapper);
        //删除副本表
        slaveService.remove(Wrappers.lambdaQuery(OpsTaskAttrBasicSlave.class).eq(OpsTaskAttrBasicSlave::getTransferId, id));
        //删除权限表配置
        opsShiftConfListService.remove(Wrappers.lambdaQuery(OpsShiftConfList.class).eq(OpsShiftConfList::getShiftId, id));
        //权限替换还是新增-覆盖不用考虑
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reset(String id, Integer permReset) {
        //查询副本
        List<OpsTaskAttrBasicSlave> slaves = slaveService.list(Wrappers.lambdaQuery(OpsTaskAttrBasicSlave.class).eq(OpsTaskAttrBasicSlave::getTransferId, id));
        List<OpsTaskAttrBasicReplica> replicas = slaves.stream().map(i -> new OpsTaskAttrBasicSlave().convert(i)).collect(Collectors.toList());
        //人员权限复原
        OpsTaskPositionConfigRecords records = getById(id);
        //权限需要重置
        if (permReset == 1) {
            List<OpsShiftConfList> list = opsShiftConfListService.
                    list(Wrappers.lambdaQuery(OpsShiftConfList.class).eq(OpsShiftConfList::getShiftId, id));
            if (records.getReplaced() == 1) {
                //（1为临时扩展，所以需要复原人员岗位权限）
                List<OpsSysUserOrg> orgDel = new ArrayList<>();
                for (OpsShiftConfList item : list) {
                    OpsSysUserOrg sk = new OpsSysUserOrg();
                    sk.setUserId(Long.valueOf(item.getUserId()));
                    sk.setOrgId(Long.valueOf(item.getTaskId()));
                    orgDel.add(sk);
                }
                if (!orgDel.isEmpty()) {
                    for (OpsSysUserOrg org : orgDel) {
                        LambdaQueryWrapper<OpsSysUserOrg> del = new LambdaQueryWrapper<>();
                        del.eq(OpsSysUserOrg::getOrgId, org.getOrgId());
                        del.eq(OpsSysUserOrg::getUserId, org.getUserId());
                        userOrgService.remove(del);
                    }
                }
            }
            if (records.getReplaced() == 2) {
                //2为重置，所以要删除当前并恢复之前
                //先删除当前岗位信息
                List<String> delIds = new ArrayList<>();
                List<OpsSysUserOrg> orgs = new ArrayList<>();
                for (OpsShiftConfList item : list) {
                    delIds.add(item.getUserId());
                    OpsSysUserOrg sk = new OpsSysUserOrg();
                    sk.setUserId(Long.valueOf(item.getUserId()));
                    sk.setOrgId(Long.valueOf(item.getTaskId()));
                    orgs.add(sk);
                }
                userOrgService.remove(Wrappers.lambdaQuery(OpsSysUserOrg.class).in(OpsSysUserOrg::getUserId, delIds));
                userOrgService.saveBatch(orgs);
            }
        }
        //先删除当前模板关系表与配置表 内容，真实删除，防止id主键冲突
        taskTemplateService.deleteTemplateForTransfer(records.getTemplateId(), replicas);
        //更改记录内容变为无效内容
        LambdaUpdateWrapper<OpsTaskPositionConfigRecords> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsTaskPositionConfigRecords::getDeleted, 1);
        updateWrapper.set(OpsTaskPositionConfigRecords::getState, 2);
        updateWrapper.eq(OpsTaskPositionConfigRecords::getId, id);
        update(updateWrapper);
        //删除副本内容，防止id冲突
        slaveService.remove(Wrappers.lambdaQuery(OpsTaskAttrBasicSlave.class).eq(OpsTaskAttrBasicSlave::getTransferId, id));
    }

}
