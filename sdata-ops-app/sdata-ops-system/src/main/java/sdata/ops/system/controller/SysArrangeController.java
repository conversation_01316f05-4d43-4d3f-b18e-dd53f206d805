package sdata.ops.system.controller;


import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.OpsSysArrangeDTO;
import sdata.ops.base.system.model.entity.OpsSysArrange;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsSysArrangeService;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:37
 */
@RestController
@RequestMapping("/sysArrange")
@RequiredArgsConstructor
public class SysArrangeController {


    private final OpsSysArrangeService opsSysArrangeService;

    /**
     * 获取排班列表
     */
    @GetMapping("/list")
    public R<Object> list(
            @RequestParam(value = "id") String id,
            @RequestParam(value = "startDate") String startDate,
            @RequestParam(value = "endDate") String endDate
    ) {
        return R.data(opsSysArrangeService.list(id, startDate, endDate));
    }

    /**
     * 进行排班
     */
    @PostMapping("/save")
    public R<Object> save(@RequestBody OpsSysArrange opsSysArrange) {
        //如果该岗位的该用户的该班次在该天有排班记录则跳过
        Integer count = opsSysArrangeService.findByOrgShiftUserDate(opsSysArrange);
        if (count > 0) {
            return R.data("该岗位的该用户的该班次在该天已有排班记录");
        }
        opsSysArrangeService.save(opsSysArrange);
        return R.success("操作成功");
    }

    /**
     * 取消排班
     */
    @GetMapping("del")
    public R<Object> del(@RequestParam("id") String id) {
        opsSysArrangeService.removeById(id);
        return R.success("操作成功");
    }

    /**
     * 班次覆盖
     */
    @PostMapping("/cover")
    @Transactional
    public R<Object> cover(@RequestBody OpsSysArrangeDTO opsSysArrange) {
        opsSysArrangeService.cover(opsSysArrange);
        return R.success("操作成功");
    }

    /**
     * 批量排班
     */
    @PostMapping("/batchScheduling")
    public R<Object> batchScheduling(@RequestBody OpsSysArrangeDTO opsSysArrange) {
        opsSysArrangeService.batchScheduling(opsSysArrange);
        return R.success("操作成功");
    }

}
