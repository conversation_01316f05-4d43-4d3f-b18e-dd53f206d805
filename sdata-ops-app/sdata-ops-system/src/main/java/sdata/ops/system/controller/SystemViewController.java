package sdata.ops.system.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.entity.OpsView;
import sdata.ops.base.system.model.entity.OpsViewGroupRelation;
import sdata.ops.base.system.model.vo.OpsViewVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.system.service.OpsViewService;

import java.util.List;

@RequestMapping("/view")
@RequiredArgsConstructor
@RestController
public class SystemViewController {


    private final OpsViewService opsViewService;

    @PostMapping("/save")
    public R<Object> saveView(@RequestBody JSONObject view) {
        OpsView viewEntity = JSONUtil.toBean(view, OpsView.class);
        opsViewService.saveView(viewEntity);
        return R.data(new OpsViewVO().convert(viewEntity, 0));
    }

    @GetMapping("/get")
    public R<Object> getView(@RequestParam(value = "id") String id) {
        return R.data(opsViewService.getAllViewInfo(id));
    }

    @GetMapping("/delete")
    public R<Object> deleteView(@RequestParam("id") String id) {
        opsViewService.deleteView(id);
        return R.data(MessageConstant.OPERATOR_SUCCESS);
    }



}
