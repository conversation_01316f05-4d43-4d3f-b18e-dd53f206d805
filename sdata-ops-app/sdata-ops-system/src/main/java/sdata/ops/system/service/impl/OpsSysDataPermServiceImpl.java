package sdata.ops.system.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.entity.OpsSysDataPerm;
import sdata.ops.base.system.model.entity.OpsSysDataPermAuthSubject;
import sdata.ops.base.system.model.entity.OpsSysDataPermDimensionValue;
import sdata.ops.base.system.model.vo.OpsPermSaveVO;
import sdata.ops.base.system.model.vo.OpsSysDataPermDimensionValueVO;
import sdata.ops.common.api.R;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import sdata.ops.system.mapper.OpsSysDataAuthMapper;
import sdata.ops.system.service.OpsSysDataPermAuthSubjectService;
import sdata.ops.system.service.OpsSysDataPermDimensionValueService;
import sdata.ops.system.service.OpsSysDataPermService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_sys_data_perm(数据授权信息表)】的数据库操作Service实现
 * @createDate 2024-07-02 16:27:27
 */
@Service
@RequiredArgsConstructor
public class OpsSysDataPermServiceImpl extends ServiceImpl<OpsSysDataAuthMapper, OpsSysDataPerm> implements OpsSysDataPermService {

    private final OpsSysDataPermDimensionValueService sysDataPermDimensionValueService;

    private final OpsSysDataPermAuthSubjectService subjectService;

    @Lazy
    @Resource
    private IndicatorInfoFeign indicatorInfoFeign;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateAllConf(OpsPermSaveVO perm) {
        //更新权限主表内容
        OpsSysDataPerm permMain = perm.getPerm();
        saveOrUpdate(permMain);
        //删除>新增权限授权对象信息
        updatePermAuthSubject(perm.getSubjectList(), permMain.getId());
        //删除>新增权限授权配置信息
        updatePermConfig(perm.getPermConfig(), permMain.getId(), permMain.getPermType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletedOperation(String id) {
        //查看权限是否被指标引用
        R<Object> ref = indicatorInfoFeign.getMetricRelationForPerm(id);
        if (!ref.isSuccess() || (long) ref.getData() > 0) {
            throw new RuntimeException("该权限被指标引用，请先解除引用关系");
        }
        removeById(Long.valueOf(id));
        sysDataPermDimensionValueService.remove(new QueryWrapper<OpsSysDataPermDimensionValue>().eq("perm_id", id));
        subjectService.remove(new QueryWrapper<OpsSysDataPermAuthSubject>().eq("perm_id", id));
    }

    @Override
    public JSONObject getDataPermById(String id, String userId) {
        long count = subjectService.count(new LambdaQueryWrapper<>(OpsSysDataPermAuthSubject.class).eq(OpsSysDataPermAuthSubject::getPermId, id).
                eq(OpsSysDataPermAuthSubject::getSubjectId, userId));
        if (count > 0) {
            OpsSysDataPermDimensionValueVO permConfig = OpsSysDataPermDimensionValueVO.covertVO(sysDataPermDimensionValueService.getOne(Wrappers.lambdaQuery(OpsSysDataPermDimensionValue.class)
                    .eq(OpsSysDataPermDimensionValue::getPermId, id)));
            return new JSONObject().
                    set("permId", id).
                    set("permType", permConfig.getValueType())
                    .set("permConf", permConfig.getValueConf());
        }
        return null;
    }

    @Override
    public OpsPermSaveVO queryDetail(String id) {
        OpsPermSaveVO vo = new OpsPermSaveVO();
        vo.setPerm(getById(id));
        vo.setSubjectList(subjectService.list(new QueryWrapper<OpsSysDataPermAuthSubject>().eq("perm_id", id)));
        vo.setPermConfig(OpsSysDataPermDimensionValueVO.covertVO(sysDataPermDimensionValueService.getOne(new QueryWrapper<OpsSysDataPermDimensionValue>().eq("perm_id", id))));
        return vo;
    }

    private void updatePermConfig(OpsSysDataPermDimensionValueVO permConfig, String id, String permType) {
        if (permConfig != null) {
            sysDataPermDimensionValueService.remove(new QueryWrapper<OpsSysDataPermDimensionValue>().eq("perm_id", id));
            OpsSysDataPermDimensionValue entity = permConfig.covertEntity();
            entity.setPermId(Long.valueOf(id));
            entity.setValueType(permType.equals("rule") ? 2 : 1);
            sysDataPermDimensionValueService.save(entity);
        }
    }

    private void updatePermAuthSubject(List<OpsSysDataPermAuthSubject> subjectList, String id) {
        subjectService.remove(new QueryWrapper<OpsSysDataPermAuthSubject>().eq("perm_id", id));
        subjectList.forEach(subject -> subject.setPermId((id)));
        subjectService.saveBatch(subjectList);
    }
}




