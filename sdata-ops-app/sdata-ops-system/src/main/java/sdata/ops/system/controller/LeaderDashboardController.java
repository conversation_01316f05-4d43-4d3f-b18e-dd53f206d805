package sdata.ops.system.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.system.model.dto.ConditionTaskDTO;
import sdata.ops.base.system.model.entity.OpsDashboardLayout;
import sdata.ops.base.system.model.entity.OpsSysOrg;
import sdata.ops.base.system.model.entity.OpsTaskFundInfo;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.base.system.model.vo.ComponentVO;
import sdata.ops.base.system.model.vo.DashboardVO;
import sdata.ops.base.system.model.vo.UserStatisticsDetailVO;
import sdata.ops.base.system.model.vo.UserStatisticsVO;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.service.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/system/leaderDash")
@RequiredArgsConstructor
public class LeaderDashboardController {


    private final OpsTaskGenInfoService opsTaskGenInfoService;

    private final OpsTaskFundInfoService opsTaskFundInfoService;

    private final OpsTaskAttrBasicReplicaService opsTaskAttrBasicReplicaService;

    private final OpsSysOrgService opsSysOrgService;

    private final SystemUserService userService;



    /**
     * 获取与用户相关的任务列，分页形式
     *
     * @return IPage
     */
    @GetMapping("/list")
    public R<Object> taskList(@RequestParam(value = "orgId", required = false) String orgId,
                              @RequestParam(value = "date", required = false) String dateTime) {
        //附加权限查询生成
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(orgId);
        //如果条件附加为null则组织权限未配置
        if (Objects.isNull(condition)) {
            return R.data(new ArrayList<>());
        }
        //第一次查询都为根节点，即pid=0 的
        //主日常任务查询，每个交易日的
        //特殊任务 - 延期的
        //add  延期的会把完成时间调整为顺延工作日的最大值，比如当天是2024-07-21 17:00 可以延期3天 则是 2024-07-24 17:00 查询此类数据
        //特殊任务 - 周期轮转的
        //add 周期任务举例，也是每日要生成的，但是开始干这个任务可以是T 日，也可以是结束日之前 比如结束日是T+5 ，那么开始日期就可以是T+5以内任何一个日期

        String genTime = !StringUtils.hasText(dateTime) ? DateUtil.format(new Date(), "yyyy-MM-dd") : dateTime;
        Date now = !StringUtils.hasText(dateTime) ? new Date() : DateUtil.parse(dateTime + " " + DateUtil.format(new Date(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        List<OpsTaskGenInfo> resc = opsTaskGenInfoService.multiTaskListForDashboardByLeader(now, genTime, condition);

        //排序
        resc.sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
        //查询该节点内所有有子集节点任务清单，并树状组织,赋值给当前分页数据
        Map<Long, String> p_child = new HashMap<>();
        for (OpsTaskGenInfo info : resc) {
            if (StringUtils.hasText(info.getTaskChildIds())) {
                p_child.put(info.getId(), info.getTaskChildIds());
            }
        }
        if (!p_child.isEmpty()) {
            //查询当前任务中有子任务的清单项目，并重新赋值
            Map<Long, List<OpsTaskGenInfo>> child = opsTaskGenInfoService.findChildByLeader(p_child, condition);
            for (OpsTaskGenInfo record : resc) {
                if (child.containsKey(record.getId())) {
                    record.setChildren(child.get(record.getId()));
                }
            }
        }

        // 查询模板关联产品的任务
        List<OpsTaskGenInfo> opsTaskInfoList = opsTaskFundInfoService.getOpsTaskInfoListByLeader(orgId, genTime);

        resc.addAll(opsTaskInfoList);

        return R.data(resc);
    }


    /**
     * 代办接口
     * 查询范围临时代办
     *
     * @return list
     */
    @GetMapping("/todoList")
    public R<Object> todoList(@RequestParam(value = "date", required = false) String dateTime
    ,@RequestParam(value = "orgId")String orgId) {
        ConditionTaskDTO condition = new ConditionTaskDTO();
        condition.setType(4);
        condition.setPostIds(new ArrayList<>());
        //主日常任务查询，每个交易日的
        List<OpsTaskGenInfo> res = opsTaskGenInfoService.dashboardListTask(todoWrapper(dateTime), condition);
        return R.data(res);
    }


    @GetMapping("/warnList")
    public R<Object> warnList(@RequestParam(value = "date", required = false) String dateTime,
                              @RequestParam(value = "orgId") String orgId) {
        //附加权限查询生成
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(orgId);

        //如果条件附加为null则组织权限未配置
        if (Objects.isNull(condition)) {
            return R.data(new ArrayList<>());
        }
        //第一次查询都为根节点，即pid=0 的
        //主日常任务查询，每个交易日的
        List<OpsTaskGenInfo> res = opsTaskGenInfoService.dashboardListTask(warnWrapper(dateTime), condition);
        //排除自动延时的
        res = res.stream().filter(this::defferCondition).collect(Collectors.toList());
        //排序
        return R.data(sortObjectsByPriority(res, "1"));
    }

    @GetMapping("/tempDetail")
    public R<Object> tempTaskDetail(@RequestParam("id") String id) {
        OpsTaskGenInfo pInfo = opsTaskGenInfoService.getById(id);
        if (StringUtils.hasText(pInfo.getTaskChildIds())) {
            ConditionTaskDTO conditionTaskDTO = new ConditionTaskDTO();
            conditionTaskDTO.setType(4);
            Map<Long, List<OpsTaskGenInfo>> childInfos = opsTaskGenInfoService.findChildByLeader(Map.of(pInfo.getId(), pInfo.getTaskChildIds()), conditionTaskDTO);
            pInfo.setChildren(childInfos.get(pInfo.getId()));
        }
        return R.data(pInfo);
    }

    public boolean defferCondition(OpsTaskGenInfo i) {
        //如果不是延时任务
        if (i.getTaskDeferredType() != null && i.getTaskDeferredType().equals("0")) {
            return true;
        }
        //如果是延时任务且是当天内还未延时的数据
        if (i.getTaskDeferredType() != null && i.getTaskDeferredType().equals("1")) {
            //判定延时任务是否为当天且还未延时
            //延时阈值为0，则延时无效
            if (i.getTaskDeferredCount() == 0) {
                return true;
            }
            //如果延时阈值大于0 ，且当天任务还 未被自动任务调整结束时间
            return i.getTaskDeferredCount() <= 0 || !DateUtil.format(i.getTaskEndTime(), "yyyy-MM-dd").equals(i.getTaskGenTime());
        }
        return true;
    }

    /**
     * 工作台 统计信息，代办数据 日常任务 加预警数据
     *
     * @return count
     */
    @GetMapping("/taskStatistics")
    public R<Object> dataCount(@RequestParam(value = "date", required = false) String dateTime) {
        UserStatisticsVO res = new UserStatisticsVO();
        //附加权限查询生成
        ConditionTaskDTO condition = new ConditionTaskDTO();
        condition.setType(4);
        condition.setPostIds(new ArrayList<>());
        //再查询临时的
        List<OpsTaskGenInfo> tempList = opsTaskGenInfoService.dashboardTempListTaskLeaf(todoWrapperLeaf(dateTime), condition);

        //最后查日常的
        String genTime = !StringUtils.hasText(dateTime) ? DateUtil.format(new Date(), "yyyy-MM-dd") : dateTime;
        Date now = !StringUtils.hasText(dateTime) ? new Date() : DateUtil.parse(dateTime + " " + DateUtil.format(new Date(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        List<OpsTaskGenInfo> dailyList = opsTaskGenInfoService.multiTaskListForDashboardLeafByLeader(now, genTime, condition);
        // 查询明细(产品部明细-产品组合查询)
        List<OpsTaskFundInfo> opsTaskFundInfoList = opsTaskFundInfoService.getTaskFundListByLeader(now.toString(), condition.getPostIds());
        opsTaskFundInfoList = opsTaskFundInfoList.stream().filter(e -> e.getCompleteStatus() != null && (e.getCompleteStatus().equals("1") || e.getCompleteStatus().equals("3"))).collect(Collectors.toList());
        //已完成任务id
        List<Long> overIds = new ArrayList<>();
        //临时待办排除-
        tempList = tempList.stream().filter(temp -> !(!temp.getTaskCompleteStatus().equals(3) && !DateUtil.format(temp.getTaskEndTime(), "yyyy-MM-dd").equals(genTime))).collect(Collectors.toList());

        if (!tempList.isEmpty()) {
            for (OpsTaskGenInfo info : tempList) {
                if (info.getTaskCompleteStatus() == 5) {
                    overIds.add(info.getId());
                }
            }
        }
        if (!dailyList.isEmpty()) {
            for (OpsTaskGenInfo info : dailyList) {
                if ((info.getTaskCompleteStatus() == 3 || info.getTaskCompleteStatus() == 5)) {
                    overIds.add(info.getId());
                }
            }
        }
        //追加二级领导下任务的复核条数(就是权限类型不等于1 查询直接sql过滤)，同样条件过滤
        List<OpsTaskGenInfo> checkOpsTaskGenInfoList = opsTaskGenInfoService.checkTaskListForLeader(genTime, condition);
        //计算分母
        Long dc = (long) (tempList.size() + dailyList.size() + opsTaskFundInfoList.size() + checkOpsTaskGenInfoList.size());
        if (dc == 0) {
            res.setComplete("0").setUnComplete("0").setRate("0%");
            return R.data(res);
        }
        //完成的
        Long over = (long) overIds.size() + opsTaskFundInfoList.stream().filter(e -> e.getCompleteStatus().equals("3")).count() + tempList.stream().filter(temp -> temp.getTaskCompleteStatus().equals(3)).count() + checkOpsTaskGenInfoList.size();
        //未完成的   当天未开始的算入未完成
        Long unOver = dc - overIds.size() -
                opsTaskFundInfoList.stream().filter(e -> e.getCompleteStatus().equals("3")).count() -
                tempList.stream().filter(temp -> temp.getTaskCompleteStatus().equals(3)).count() -
                checkOpsTaskGenInfoList.size();
        //rate
        BigDecimal rate = NumberUtil.div(over, dc).multiply(new BigDecimal(100));
        res.setComplete(over + "").setUnComplete(unOver + "").setRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
        return R.data(res);
    }

    private Wrapper<OpsTaskGenInfo> warnWrapper(String dateTime) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskCompleteStatus, OpsTaskGenInfo::getTaskEndTime);
        queryWrapper.eq(OpsTaskGenInfo::getParentId, 0);
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, 3).ne(OpsTaskGenInfo::getTaskCompleteStatus, 5);
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority, 1);
        //如果有时间参数就是参数值，没时间参数则是当前时间
        queryWrapper.lt(OpsTaskGenInfo::getTaskEndTime, StringUtils.hasText(dateTime) ? DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.parse(dateTime, "yyyy-MM-dd"), -1)) : new Date());
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        return queryWrapper;
    }

    private Wrapper<OpsTaskGenInfo> todoWrapper(String dateTime) {
        //第一次查询都为根节点，即pid=0 的
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getParentId, 0);
        queryWrapper.eq(OpsTaskGenInfo::getTaskType, "temp");
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority,1);
        queryWrapper.last(" and date_format(task_start_time,'%Y-%m-%d') <= '" + dateTime + "' and date_format(task_end_time,'%Y-%m-%d') >='" + dateTime + "' order by task_end_time asc");
        return queryWrapper;
    }

    private Wrapper<OpsTaskGenInfo> warnWrapperLeaf(String dateTime) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId, OpsTaskGenInfo::getTaskName, OpsTaskGenInfo::getTaskCompleteStatus, OpsTaskGenInfo::getTaskEndTime);
        queryWrapper.isNull(OpsTaskGenInfo::getTaskChildIds);
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, 3).ne(OpsTaskGenInfo::getTaskCompleteStatus, 5);
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        //如果有时间参数就是参数值，没时间参数则是当前时间
        queryWrapper.lt(OpsTaskGenInfo::getTaskEndTime, StringUtils.hasText(dateTime) ? DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.parse(dateTime, "yyyy-MM-dd"), -1)) : new Date());
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        return queryWrapper;
    }

    private Wrapper<OpsTaskGenInfo> todoWrapperLeaf(String dateTime) {
        //第一次查询都为根节点，即pid=0 的
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(OpsTaskGenInfo::getTaskChildIds);
        queryWrapper.last(" and date_format(task_start_time,'%Y-%m-%d') <= '" + dateTime + "' and date_format(task_end_time,'%Y-%m-%d') >='" + dateTime + "' order by create_time desc");
        queryWrapper.eq(OpsTaskGenInfo::getTaskType, "temp");
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority,1);
        return queryWrapper;
    }

    /**
     * 统计指标详情列表专用
     *
     * @param dateTime
     * @return
     */
    private Wrapper<OpsTaskGenInfo> todoWrapperLeafDetail(String dateTime) {
        //第一次查询都为根节点，即pid=0 的
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        //queryWrapper.isNull(OpsTaskGenInfo::getTaskChildIds);
        queryWrapper.last(" and date_format(task_start_time,'%Y-%m-%d') <= '" + dateTime + "' and date_format(task_end_time,'%Y-%m-%d') >='" + dateTime + "' order by create_time desc");
        queryWrapper.eq(OpsTaskGenInfo::getTaskType, "temp");
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority,1);
        return queryWrapper;
    }

    private List<OpsTaskGenInfo> sortObjectsByPriority(List<OpsTaskGenInfo> res, String taskPriority) {
        List<String> ks = Arrays.asList("1,", "2", "3");
        //如果有值
        if (StringUtils.hasText(taskPriority)) {
            ks = List.of(taskPriority);
        }
        List<String> finalKs = ks;
        Comparator<OpsTaskGenInfo> comparator = (o1, o2) -> {
            int index1 = finalKs.indexOf(o1.getTaskPriority());
            int index2 = finalKs.indexOf(o2.getTaskPriority());
            return Integer.compare(index2, index1);
        };
        return res.stream().sorted(comparator).collect(Collectors.toList());
    }

    /**
     * 统计指标详情接口
     *
     * @param dateTime yyyy-MM-dd
     * @return
     */
    @GetMapping("/taskStatisticsDetail")
    public R<Object> dataDetail(@RequestParam(value = "date", required = false) String dateTime) {
        try {
            UserStatisticsDetailVO res = new UserStatisticsDetailVO();
            //附加权限查询生成
            ConditionTaskDTO condition = new ConditionTaskDTO();
            condition.setType(4);
            condition.setPostIds(new ArrayList<>());


            String genTime = !StringUtils.hasText(dateTime) ? DateUtil.format(new Date(), "yyyy-MM-dd") : dateTime;
            Date now = !StringUtils.hasText(dateTime) ? new Date() : DateUtil.parse(dateTime + " " + DateUtil.format(new Date(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");

            //临时类型数据列表
            List<OpsTaskGenInfo> tempList = opsTaskGenInfoService.dashboardListTaskDetail(todoWrapperLeafDetail(dateTime), condition).stream()
                    .filter(temp -> !(!temp.getTaskCompleteStatus().equals(3)
                            && !DateUtil.format(temp.getTaskEndTime(), "yyyy-MM-dd").equals(genTime))
                    ).collect(Collectors.toList());
            //日常类型数据列表
            List<OpsTaskGenInfo> dailyList = opsTaskGenInfoService.multiTaskListForDashboardLeafDetailByLeader(now, genTime, condition);
            //组合明细列表
            List<OpsTaskFundInfo> opsTaskFundInfoList = opsTaskFundInfoService.getTaskFundListByLeader(now.toString(), condition.getPostIds())
                    .stream().filter(e -> e.getCompleteStatus() != null && (e.getCompleteStatus().equals("1") || e.getCompleteStatus().equals("3"))).collect(Collectors.toList());
            //复核数据
            List<OpsTaskGenInfo> checkOpsTaskGenInfoList = opsTaskGenInfoService.checkTaskListForLeader(genTime, condition);
            //通过叶节点复核数据查询所有的父节点数据

            List<OpsTaskGenInfo> checkOpsTaskGenInfoList_p = opsTaskGenInfoService.getParentListByChildIds(checkOpsTaskGenInfoList.stream().map(OpsTaskGenInfo::getId).distinct().collect(Collectors.toList()));

            Map<String, String> idToNameMap = userService.findNameIdMapping();

            if (CollUtil.isNotEmpty(checkOpsTaskGenInfoList_p)) {
                checkOpsTaskGenInfoList = checkOpsTaskGenInfoList_p;
            }
            checkOpsTaskGenInfoList.forEach(gen -> {
                gen.setOperationDisplayType(2);
                if (StringUtils.hasText(gen.getOperationCompleteId()) && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                    String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                    if (StringUtils.hasText(tmpOperationName)) {
                        gen.setOperationCompleteId(tmpOperationName);
                    }
                } else {
                    gen.setOperationCompleteId(gen.getTaskOwnerVal());
                }
                if (StringUtils.hasText(gen.getOperationCheckId()) && idToNameMap.containsKey(gen.getOperationCheckId())) {
                    String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                    if (StringUtils.hasText(tmpCheckName)) {
                        gen.setOperationCheckId(tmpCheckName);
                    }
                } else {
                    gen.setOperationCheckId(gen.getTaskCheckVal());
                }
            });

            //临时数据-完成-最后一层子数据
            List<OpsTaskGenInfo> tempListComplete = tempList.stream().filter(i -> (!StringUtils.hasText(i.getTaskChildIds()) && (i.getTaskCompleteStatus() == 3 || i.getTaskCompleteStatus() == 5))).collect(Collectors.toList());


            //临时数据-未完成-最后一层子数据
            List<OpsTaskGenInfo> tempListUnComplete = tempList.stream().filter(i -> (!StringUtils.hasText(i.getTaskChildIds()) && i.getTaskCompleteStatus() != 3 && i.getTaskCompleteStatus() != 5)).collect(Collectors.toList());
            if (condition.getType() == 1) {
                tempListComplete = tempListComplete.stream()
                        .filter(i -> (
                                StringUtils.hasText(i.getTaskOwnerId())
                                        && (("2".equals(i.getTaskOwnerType()) && SecureUtil.currentUserId().equals(i.getTaskOwnerId()))
                                        || ("1".equals(i.getTaskOwnerType()) && condition.getPostIds().contains(i.getTaskOwnerId())))))
                        .collect(Collectors.toList());
                System.out.println("tempListComplete_1: " + tempListComplete.size());
                tempListUnComplete = tempListUnComplete.stream()
                        .filter(i -> (
                                StringUtils.hasText(i.getTaskOwnerId())
                                        && (("2".equals(i.getTaskOwnerType()) && SecureUtil.currentUserId().equals(i.getTaskOwnerId()))
                                        || ("1".equals(i.getTaskOwnerType()) && condition.getPostIds().contains(i.getTaskOwnerId())))))
                        .collect(Collectors.toList());
            }
            List<String> tempCIds = tempListComplete.stream().map(i -> String.valueOf(i.getId())).collect(Collectors.toList());
            List<String> tempUnCIds = tempListUnComplete.stream().map(i -> String.valueOf(i.getId())).collect(Collectors.toList());
            List<OpsTaskGenInfo> tempComplete_p = Lists.newArrayList(), tempUnComplete_p = Lists.newArrayList();
            tempList.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds()))
                    .collect(Collectors.toMap(
                            i -> Arrays.stream(i.getTaskChildIds().split(",")).collect(Collectors.toSet()),
                            i -> i, (n1, n2) -> n1))
                    .entrySet()
                    .forEach(entry -> {
                        Set<String> set = entry.getKey();
                        if (tempCIds.stream().anyMatch(set::contains)) {
                            tempComplete_p.add(entry.getValue());
                        }
                        if (tempUnCIds.stream().anyMatch(set::contains)) {
                            tempUnComplete_p.add(entry.getValue());
                        }
                    });
            tempListComplete.addAll(tempComplete_p);
            tempListUnComplete.addAll(tempUnComplete_p);

            //日常类型数据-完成-最后一层子数据
            List<OpsTaskGenInfo> dailyListComplete = dailyList.stream().filter(i -> (!StringUtils.hasText(i.getTaskChildIds()) && (i.getTaskCompleteStatus() == 3 || i.getTaskCompleteStatus() == 5))).collect(Collectors.toList());
            //日常类型数据-未完成-最后一层子数据
            List<OpsTaskGenInfo> dailyListUnComplete = dailyList.stream().filter(i -> (!StringUtils.hasText(i.getTaskChildIds()) && i.getTaskCompleteStatus() != 3 && i.getTaskCompleteStatus() != 5)).collect(Collectors.toList());
            if (condition.getType() == 1) {
                dailyListComplete = dailyListComplete.stream()
                        .filter(i -> (
                                StringUtils.hasText(i.getTaskOwnerId())
                                        && (("2".equals(i.getTaskOwnerType()) && SecureUtil.currentUserId().equals(i.getTaskOwnerId()))
                                        || ("1".equals(i.getTaskOwnerType()) && condition.getPostIds().contains(i.getTaskOwnerId())))))
                        .collect(Collectors.toList());
                dailyListUnComplete = dailyListUnComplete.stream()
                        .filter(i -> (
                                StringUtils.hasText(i.getTaskOwnerId())
                                        && (("2".equals(i.getTaskOwnerType()) && SecureUtil.currentUserId().equals(i.getTaskOwnerId()))
                                        || ("1".equals(i.getTaskOwnerType()) && condition.getPostIds().contains(i.getTaskOwnerId())))))
                        .collect(Collectors.toList());
            }

            List<String> dailyCIds = dailyListComplete.stream().map(i -> String.valueOf(i.getId())).distinct().collect(Collectors.toList());
            List<String> dailyUnCIds = dailyListUnComplete.stream().map(i -> String.valueOf(i.getId())).distinct().collect(Collectors.toList());
            List<OpsTaskGenInfo> dailyComplete_p = Lists.newArrayList(), dailyUnComplete_p = Lists.newArrayList();
            dailyList.stream().filter(i -> StringUtils.hasText(i.getTaskChildIds()))
                    .collect(Collectors.toMap(
                            i -> Arrays.stream(i.getTaskChildIds().split(",")).collect(Collectors.toSet()),
                            i -> i, (n1, n2) -> n1))
                    .entrySet()
                    .forEach(entry -> {
                        Set<String> set = entry.getKey();
                        if (dailyCIds.stream().anyMatch(set::contains)) {
                            dailyComplete_p.add(entry.getValue());
                        }
                        if (dailyUnCIds.stream().anyMatch(set::contains)) {
                            dailyUnComplete_p.add(entry.getValue());
                        }
                    });
            dailyListComplete.addAll(dailyComplete_p);
            dailyListUnComplete.addAll(dailyUnComplete_p);

            //组合明细列表-完成
            List<OpsTaskGenInfo> fund2GenListComplete = fund2GenLogic(opsTaskFundInfoList.stream().filter(e -> null != e.getCompleteStatus() && "3".equals(e.getCompleteStatus())).collect(Collectors.toList()));
            //组合明细列表-未完成
            List<OpsTaskGenInfo> fund2GenListUnComplete = fund2GenLogic(opsTaskFundInfoList.stream().filter(e -> !"3".equals(e.getCompleteStatus())).collect(Collectors.toList()));

            //合并所有完成数据
            dailyListComplete.addAll(tempListComplete);
            dailyListComplete.addAll(fund2GenListComplete);
            List<Long> dailyCompleteIds = dailyListComplete.stream().map(OpsTaskGenInfo::getId).collect(Collectors.toList());


            //dailyListComplete.addAll(checkOpsTaskGenInfoList);

            //合并所有未完成数据
            dailyListUnComplete.addAll(tempListUnComplete);
            dailyListUnComplete.addAll(fund2GenListUnComplete);

            //将operation_complete_id, operation_check_id,替换为对应的name
            if (CollUtil.isNotEmpty(idToNameMap)) {
                dailyListComplete.stream().forEach(gen -> {
                    if (StringUtils.hasText(gen.getOperationCompleteId())
                            && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                        String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                        if (StringUtils.hasText(tmpOperationName)) {
                            gen.setOperationCompleteId(tmpOperationName);
                        }
                    } else {
                        gen.setOperationCompleteId(gen.getTaskOwnerVal());
                    }
                    if (StringUtils.hasText(gen.getOperationCheckId())
                            && idToNameMap.containsKey(gen.getOperationCheckId())) {
                        String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                        if (StringUtils.hasText(tmpCheckName)) {
                            gen.setOperationCheckId(tmpCheckName);
                        }
                    } else {
                        gen.setOperationCheckId(gen.getTaskCheckVal());
                    }
                });
                dailyListUnComplete.stream().forEach(gen -> {
                    if (StringUtils.hasText(gen.getOperationCompleteId()) && idToNameMap.containsKey(gen.getOperationCompleteId())) {
                        String tmpOperationName = idToNameMap.get(gen.getOperationCompleteId());
                        if (StringUtils.hasText(tmpOperationName)) {
                            gen.setOperationCompleteId(tmpOperationName);
                        }
                    } else {
                        gen.setOperationCompleteId(gen.getTaskOwnerVal());
                    }
                    if (StringUtils.hasText(gen.getOperationCheckId()) && idToNameMap.containsKey(gen.getOperationCheckId())) {
                        String tmpCheckName = idToNameMap.get(gen.getOperationCheckId());
                        if (StringUtils.hasText(tmpCheckName)) {
                            gen.setOperationCheckId(tmpCheckName);
                        }
                    } else {
                        gen.setOperationCheckId(gen.getTaskCheckVal());
                    }
                });
            }

            Instant t4 = Instant.now();
            //转树结构
            res.setComplete(buildTree(deepCopy(dailyListComplete)));
            res.setUnComplete(buildTree(deepCopy(dailyListUnComplete)));
            res.getComplete().addAll(buildTree(deepCopy(checkOpsTaskGenInfoList)));
            Instant t5 = Instant.now();
            Duration duration45 = Duration.between(t4, t5);
            log.info("taskStatisticsDetail_buildTree执行时间:{} 毫秒", duration45.toMillis());
            return R.data(res);
        } catch (Exception e) {
            log.error("taskStatisticsDetail_error", e);
            UserStatisticsDetailVO res = new UserStatisticsDetailVO();
            return R.data(res);
        }
    }

    /**
     * 生成树状结构数据
     *
     * @param list
     * @return
     */
    public List<OpsTaskGenInfo> buildTree(List<OpsTaskGenInfo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Instant start = Instant.now();
        //Map<Long, OpsTaskGenInfo> genMap = list.stream().collect(Collectors.toMap(OpsTaskGenInfo::getId, i -> i));
        Map<Long, OpsTaskGenInfo> genMap = list.stream().collect(Collectors.toMap(i -> (i.getId() + i.getOperationDisplayType()), i -> i));
        List<OpsTaskGenInfo> rootNodes = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (null == genInfo.getParentId() || genInfo.getParentId() == 0) {
                rootNodes.add(genInfo);
            } else {
                OpsTaskGenInfo parent = genMap.get(genInfo.getParentId() + genInfo.getOperationDisplayType());
                if (null != parent) {
                    if (null == parent.getChildren()) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(genInfo);
                } else {
                    rootNodes.add(genInfo);
                }
            }
        }
        sortTreeNodes(rootNodes);
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        log.info("buildTree方法-执行时间:{} 毫秒", duration.toMillis());
        return rootNodes;
    }

    /**
     * 排序处理
     *
     * @param rootNodes
     */
    public static void sortTreeNodes(List<OpsTaskGenInfo> rootNodes) {
        rootNodes.forEach(node -> {
            if (null != node.getChildren() && CollUtil.isNotEmpty(node.getChildren())) {
                node.getChildren()
                        .sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
                sortTreeNodes(node.getChildren());
            }
        });
        rootNodes.sort(Comparator.comparingLong(OpsTaskGenInfo::getId));
    }

    public static <T> List<T> deepCopy(List<T> src) {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            return (List<T>) in.readObject();
        } catch (IOException e) {
            e.printStackTrace();
            return new ArrayList<>();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 组合明细转gen数据逻辑处理
     *
     * @param fundList
     * @return
     */
    public List<OpsTaskGenInfo> fund2GenLogic(List<OpsTaskFundInfo> fundList) {
        if (CollUtil.isEmpty(fundList)) {
            return new ArrayList<>();
        }
        Instant start = Instant.now();
        List<String> taskReplicaIds = fundList.stream().filter(i -> StringUtils.hasText(i.getTaskReplicaId()))
                .map(OpsTaskFundInfo::getTaskReplicaId).distinct().collect(Collectors.toList());
        List<OpsTaskGenInfo> basic2GenList = opsTaskAttrBasicReplicaService.queryParentAndSelfListBySelfIds(taskReplicaIds)
                .stream()
                .map(replica -> {
                    OpsTaskGenInfo gen = new OpsTaskGenInfo();
                    BeanUtil.copyProperties(replica, gen, CopyOptions.create().ignoreNullValue().ignoreError());
                    gen.setImportStatus(1);
                    return gen;
                }).collect(Collectors.toList());
        List<OpsTaskGenInfo> fund2GenList = fundList.stream()
                .map(fund -> {
                    OpsTaskGenInfo gen = new OpsTaskGenInfo();
                    //进行属性赋值
                    gen.setId(Long.parseLong(fund.getId()));
                    gen.setParentId(Long.parseLong(fund.getTaskReplicaId()));
                    gen.setTaskName(fund.getFundName());
                    gen.setTaskCompleteStatus(Integer.parseInt(fund.getCompleteStatus()));
                    if (StringUtils.hasText(fund.getStartDate())) {
                        gen.setTaskStartTime(DateUtil.parse(fund.getStartDate(), DatePattern.NORM_DATE_PATTERN));
                    }
                    if (StringUtils.hasText(fund.getEndDate())) {
                        gen.setTaskEndTime(DateUtil.parse(fund.getEndDate(), DatePattern.NORM_DATE_PATTERN));
                    }
                    return gen;
                }).collect(Collectors.toList());
        basic2GenList.addAll(fund2GenList);
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        log.info("fund2GenLogic方法-执行时间:{} 毫秒", duration.toMillis());
        return buildTree(basic2GenList);
    }

    /**
     * 导出接口
     *
     * @param orgId
     * @param startTime yyyy-MM-dd
     * @param endTime   yyyy-MM-dd
     * @param response
     */
    @GetMapping("/exportExcel")
    public void exportExcel(String orgId, @RequestParam("startDate") String startTime, @RequestParam("endDate") String endTime, HttpServletResponse response) {
        if (!StringUtils.hasText(startTime)) {
            startTime = DateUtil.format(new Date(), "yyyy-MM-dd");
        }
        if (!StringUtils.hasText(endTime)) {
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd");
        }
        LocalDate startDate = LocalDate.parse(startTime), endDate = LocalDate.parse(endTime), nowDate = LocalDate.now();
        if (startDate.isAfter(nowDate)) {
            startDate = nowDate;
        }
        if (endDate.isAfter(nowDate)) {
            endDate = nowDate;
        }

        if (startDate.isAfter(endDate)) {
            return;
        }
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        if (daysBetween > 30) {
            return;
        }
        //时间范围列表
        List<String> dateList = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.toString());
            currentDate = currentDate.plusDays(1);
        }
        Map<Long, String> orgMap = opsSysOrgService.queryOrgByType3().stream().collect(Collectors.toMap(OpsSysOrg::getId, OpsSysOrg::getOrgName, (n2, n1) -> n1));
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(orgId);
        if (Objects.isNull(condition)) {
            return;
        }
        Workbook workbook = new XSSFWorkbook();
        for (String dateTime : dateList) {
            String genTime = dateTime;
            Date now = DateUtil.parse(dateTime + " " + DateUtil.format(new Date(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            if (LocalDate.parse(dateTime).isBefore(nowDate)) {
                now = DateUtil.parse(dateTime + " " + "23:59:59", DatePattern.NORM_DATETIME_PATTERN);
            }
            List<OpsTaskGenInfo> list = opsTaskGenInfoService.multiTaskListForDashboard(now, genTime, condition);
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            //排序
            list.sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
            //查询该节点内所有有子集节点任务清单，并树状组织,赋值给当前分页数据
            Map<Long, String> p_child = new HashMap<>();
            for (OpsTaskGenInfo info : list) {
                if (StringUtils.hasText(info.getTaskChildIds())) {
                    p_child.put(info.getId(), info.getTaskChildIds());
                }
            }

            List<OpsTaskGenInfo> destList = Lists.newArrayList();
            if (!p_child.isEmpty()) {
                //查询当前任务中有子任务的清单项目，并重新赋值
                Map<Long, List<OpsTaskGenInfo>> child = opsTaskGenInfoService.findChild(p_child, condition);
                for (OpsTaskGenInfo info : list) {
                    destList.add(info);
                    if (child.keySet().contains(info.getId())) {
                        destList.addAll(child.get(info.getId()));
                    }
                }
            }

            // 1. 找出所有 parentId = 0 的根节点
            List<OpsTaskGenInfo> rootNodes = destList.stream().filter(gen -> gen.getParentId() == 0).collect(Collectors.toList());
            // 2. 为根节点设置 task_no
            for (int i = 0; i < rootNodes.size(); i++) {
                rootNodes.get(i).setTaskNo(String.valueOf(i + 1));
            }
            // 3. 为非根节点设置 task_no
            for (OpsTaskGenInfo gen : destList) {
                if (gen.getParentId() != 0) {
                    // 查找父节点
                    OpsTaskGenInfo parent = destList.stream()
                            .filter(r -> r.getId().equals(gen.getParentId()))
                            .findFirst()
                            .orElse(null);

                    if (parent != null) {
                        // 查找父节点下的所有子节点
                        List<OpsTaskGenInfo> siblings = destList.stream()
                                .filter(r -> r.getParentId().equals(parent.getId()))
                                .collect(Collectors.toList());

                        // 查找当前节点在兄弟节点中的位置
                        int index = siblings.indexOf(gen);
                        gen.setTaskNo(parent.getTaskNo() + "-" + (index + 1));
                    }
                }
            }
            Sheet sheet = workbook.createSheet(dateTime);
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "岗位名称", "业务名称", "开始时间", "截止时间", "经办人", "复核人", "操作结果备注", "计数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            // 创建一个单元格样式
            CellStyle style = workbook.createCellStyle(), style1 = workbook.createCellStyle(),
                    dateStyle = workbook.createCellStyle(), dateStyle1 = workbook.createCellStyle(),
                    numStyle = workbook.createCellStyle(), numStyle1 = workbook.createCellStyle();
            numStyle.setDataFormat(workbook.createDataFormat().getFormat("0"));
            numStyle1.setDataFormat(workbook.createDataFormat().getFormat("0"));
            dateStyle.setDataFormat(workbook.createDataFormat().getFormat("yyyy-MM-dd"));
            dateStyle1.setDataFormat(workbook.createDataFormat().getFormat("yyyy-MM-dd"));
            // 填充数据
            for (int i = 0; i < destList.size(); i++) {
                OpsTaskGenInfo gen = destList.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(gen.getTaskNo());
                row.createCell(1).setCellValue(orgMap.get(Long.parseLong(gen.getOwnerOrgId())));
                row.createCell(2).setCellValue(gen.getTaskName());
                row.createCell(3).setCellValue(gen.getTaskStartTime());
                row.createCell(4).setCellValue(gen.getTaskEndTime());
                row.createCell(5).setCellValue(gen.getTaskOwnerVal());
                row.createCell(6).setCellValue(gen.getTaskCheckVal());
                row.createCell(7).setCellValue(gen.getTaskDesc());
                row.createCell(8).setCellValue(gen.getWorkAmount());
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    if (j == 3 || j == 4) {
                        if (gen.getTaskNo().contains("-")) {
                            row.getCell(j).setCellStyle(dateStyle);
                        } else {
                            row.getCell(j).setCellStyle(dateStyle1);
                        }
                    }
                    if (j == 8) {
                        if (gen.getTaskNo().contains("-")) {
                            row.getCell(j).setCellStyle(numStyle);
                        } else {
                            row.getCell(j).setCellStyle(numStyle1);
                        }
                    }
                    if (j != 3 && j != 4 && j != 8) {
                        if (gen.getTaskNo().contains("-")) {
                            row.getCell(j).setCellStyle(style);
                        } else {
                            row.getCell(j).setCellStyle(style1);
                        }
                    }
                }
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    if (gen.getTaskNo().contains("-")) {
                        row.getCell(j).getCellStyle().setFillForegroundColor(IndexedColors.WHITE.getIndex());
                        row.getCell(j).getCellStyle().setFillPattern(FillPatternType.NO_FILL);
                    } else {
                        row.getCell(j).getCellStyle().setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                        row.getCell(j).getCellStyle().setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    }
                }
            }
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
        }
        String fileName = "logExport";
        if (startDate.isEqual(endDate)) {
            fileName = fileName + startTime;
        } else {
            fileName = fileName + startTime + "To" + endTime;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
