package sdata.ops.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsTaskJobRelation;
import sdata.ops.system.mapper.OpsTaskJobRelationMapper;
import sdata.ops.system.service.OpsTaskJobRelationService;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_job_relation】的数据库操作Service实现
 * @createDate 2024-07-05 15:39:29
 */
@Service
public class OpsTaskJobRelationServiceImpl extends ServiceImpl<OpsTaskJobRelationMapper, OpsTaskJobRelation>
        implements OpsTaskJobRelationService {

    @Override
    public void deleteDailyTaskForTemplateId(String id) {
        baseMapper.deleteTaskReplicaIdsByTemplateId(id);
    }
}




