package sdata.ops.system.util;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ContentDisposition;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Excel相关处理
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExcelUtil {

    /**
     * 导出excel
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param response  响应体
     */
    @SneakyThrows
    public static <T> void exportExcel(List<T> list, String sheetName, HttpServletResponse response) {
        resetResponse(sheetName, response);
        ServletOutputStream os = response.getOutputStream();
        exportExcel(list, sheetName, os);
    }

    /**
     * 导出excel
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param os        输出流
     */
    public static <T> void exportExcel(List<T> list, String sheetName, OutputStream os) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Class<?> clazz = list.get(0).getClass();
        ExcelWriterSheetBuilder builder = EasyExcelFactory.write(os, clazz)
                .autoCloseStream(false)
                // 自动适配
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
//                 大数值自动转换 防止失真
//                .registerConverter(new ExcelBigNumberConvert())
                .sheet(sheetName);
        builder.doWrite(list);
    }

    /**
     * 重置响应体
     */
    private static void resetResponse(String sheetName, HttpServletResponse response) {
        ContentDisposition cd = ContentDisposition.attachment().filename(sheetName, StandardCharsets.UTF_8).build();
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,Download-Filename");
        response.setHeader("Content-Disposition", cd.toString());
        response.setHeader("Download-Filename", URLEncoder.encode(sheetName, StandardCharsets.UTF_8));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
    }
}
