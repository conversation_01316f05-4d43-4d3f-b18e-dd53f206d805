package sdata.ops.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import sdata.ops.base.system.model.entity.OpsTaskDynamicLog;
import sdata.ops.system.mapper.OpsTaskDynamicLogMapper;
import sdata.ops.system.service.OpsTaskDynamicLogService;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class OpsTaskDynamicLogServiceImpl extends ServiceImpl<OpsTaskDynamicLogMapper, OpsTaskDynamicLog>
        implements OpsTaskDynamicLogService {

    @Override
    public boolean checkTaskIsRecord(Long taskId) {
        LambdaQueryWrapper<OpsTaskDynamicLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpsTaskDynamicLog::getTaskId, taskId);
        wrapper.eq(OpsTaskDynamicLog::getTriggerDate, DateUtil.format(new Date(), "yyyy-MM-dd"));
        return !list(wrapper).isEmpty();
    }
    @Override
    public void insertSuccessTaskRecord(Long taskId) {
        OpsTaskDynamicLog dynamicLog=new OpsTaskDynamicLog();
        dynamicLog.setTaskId(taskId);
        dynamicLog.setCreateTime(new Date());
        dynamicLog.setTriggerDate(DateUtil.format(new Date(),"yyyy-MM-dd"));
    }
}




