package sdata.ops.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import sdata.ops.base.system.model.entity.OpsSysRoleMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ops_sys_role_menu(角色和菜单关联表)】的数据库操作Mapper
* @createDate 2024-06-03 19:48:02
* @Entity sdata.ops.base.system.api.entity.OpsSysRoleMenu
*/
@Mapper
public interface OpsSysRoleMenuMapper extends BaseMapper<OpsSysRoleMenu> {


    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
     int checkMenuExistRole(Long menuId);

    /**
     * 通过角色ID删除角色和菜单关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
     int deleteRoleMenuByRoleId(Long roleId);

    /**
     * 批量删除角色菜单关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
     int deleteRoleMenu(Long[] ids);

    /**
     * 批量新增角色菜单信息
     *
     * @param roleMenuList 角色菜单列表
     * @return 结果
     */
     int batchRoleMenu(List<OpsSysRoleMenu> roleMenuList);
}




