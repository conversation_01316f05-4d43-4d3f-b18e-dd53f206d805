package sdata.ops.common.config.convert;

import cn.hutool.json.JSONNull;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class JsonNullSerializer extends JsonSerializer<Object> {

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 处理 Hutool的JSONNull 或其他框架的null表示
        if (isJsonNull(value)) {
            gen.writeNull(); // 将JSONNull显式转为null
        } else {
            // 默认序列化逻辑
            serializers.defaultSerializeValue(value, gen);
        }
    }

    private boolean isJsonNull(Object value) {
        // 识别常见JSON库的null表示对象
        return value == null || value instanceof JSONNull;                                         // Jackson
    }
}
