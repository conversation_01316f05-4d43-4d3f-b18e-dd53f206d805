package sdata.ops.common.config.mp;

import cn.dev33.satoken.spring.SpringMVCUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

@Component
public class MybatisPlusAutoFill implements MetaObjectHandler {


    //设置数据新增时候的，字段自动赋值规则
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        this.strictInsertFill(metaObject, "createTime", Date.class, now);
        this.strictInsertFill(metaObject, "updateTime", Date.class, now);
        if(SpringMVCUtil.isWeb()) {
            String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";
            this.strictInsertFill(metaObject, "createBy", String.class, userId);
            this.strictUpdateFill(metaObject, "updateBy", String.class, userId);
        }
    }

    //设置数据修改update时候的，字段自动赋值规则
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        if(SpringMVCUtil.isWeb()) {
            String userId = StpUtil.isLogin() ? StpUtil.getExtra("userId").toString() : "-1";
            this.strictUpdateFill(metaObject, "updateBy", String.class, userId);
        }
    }

}
