package sdata.ops.common.config.http;

public enum BodyType {
    JSON("application/json"),
    FORM("application/x-www-form-urlencoded"),
    XML("application/xml"),
    TEXT("text/plain"),
    FORM_DATA("multipart/form-data"),
    OCTET_STREAM("application/octet-stream");

    private final String contentType;

    BodyType(String contentType) {
        this.contentType = contentType;
    }

    public String getContentType() {
        return contentType;
    }

    // 根据枚举类型获取对应的Content-Type
    public static String getContentTypeByType(String type) {
        for (BodyType enumType : BodyType.values()) {
            if (enumType.name().equals(type)) {
                return enumType.getContentType();
            }
        }
        return null;
    }
}
