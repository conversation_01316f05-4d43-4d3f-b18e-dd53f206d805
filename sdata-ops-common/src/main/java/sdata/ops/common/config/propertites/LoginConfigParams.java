package sdata.ops.common.config.propertites;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import sdata.ops.common.api.R;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.List;
import java.util.Map;
import java.util.Random;

@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "ops.login.config")
public class LoginConfigParams {

    /**
     * 登录模式 SYSTEM LDAP CAS(当前仅支持ticket) TOKEN
     */
    private String loginModel;

    /**
     * CAS模式下获取响应的票据url关键词
     * 例1: 访问cas认证通过后,cas系统重定向并追加ticket|票据模式关键词为ticket
     */
    private String casKeyword = "ticket";

    /**
     * cas服务认证接口或地址
     */
    private String casServer;

    /**
     * 本系统对接校验ticket成功后重定向到前端访问地址
     */
    private String casCallback;

    /**
     * 系统重定向到cas服务，cas认证关键词，例如有 redirect ，callback等
     */
    private String casRedirectKeyword;

    /**
     * cas校验密钥
     */
    private String casSecretKey;

    /**
     * cas校验处理类，ticket校验每个平台不一样，只能定制化
     */
    private String casHandler;
    /**
     * 域控服务器地址
     * 示例: ldap://ip/域名+389
     *      ldaps://ip/域名+636
     */
    private String ldapUrl;

    /***
     * 域控登录用户domain配置
     * 例: 域控用户登录名为 zhangsan dc=qq dc=cn
     * 最终域控验证登录为自动拼接的 <EMAIL>
     */
    private List<String> ldapDc;

    /***
     * 系统是否开启登录拦截
     */
    private Boolean auth = true;

    /**
     * token解析处理类bean名称
     */
    private String tokenHandler;

    /***
     * 第三方登录认证的情景下，是否系统主动同步用户，不同步则认证通过后使用unique值新增用户
     */
    private Boolean userAsync = false;

    // -------------------------- 工具方法

    /**
     * URL编码
     *
     * @param url see note
     * @return see note
     */
    public static String encodeUrl(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    private final static ObjectMapper objectMapper = new ObjectMapper();

    public static void writeJsonResponse(HttpServletResponse response, Object data) {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        if(data instanceof R){
            R<Object> r= (R<Object>) data;
            if(r.getCode()==401){
               response.setStatus(401);
            }
        }
        try {
            String json = objectMapper.writeValueAsString(data);
            response.getWriter().write(json);
        } catch (IOException e) {
            // 处理异常
            log.error("登录接口响应异常", e);
        }
    }

}
