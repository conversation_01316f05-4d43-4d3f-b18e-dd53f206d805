package sdata.ops.common.config.pool;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@EnableScheduling
public class ThreadConfig {

    // 与 org.flowable.spring.boot.FlowableJobConfiguration.taskExecutor 冲突
    @Bean("sdataopsTaskExecutor")
    public TaskExecutor sdataopsTaskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setMaxPoolSize(10);
        taskExecutor.setQueueCapacity(1024);
        taskExecutor.setKeepAliveSeconds(300);
        taskExecutor.setThreadNamePrefix("ops_common_threadPool");
        return taskExecutor;
    }

}
