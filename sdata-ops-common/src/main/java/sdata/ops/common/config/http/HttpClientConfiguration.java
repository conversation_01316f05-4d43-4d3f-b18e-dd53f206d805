package sdata.ops.common.config.http;

import com.dtflys.forest.Forest;
import com.dtflys.forest.backend.HttpBackend;
import com.dtflys.forest.config.ForestConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/***
 * http组件配置
 *
 */
@Component
public class HttpClientConfiguration implements ApplicationRunner {
    private ForestConfiguration forestConfiguration;

    @Autowired
    public void setForestConfiguration(ForestConfiguration forestConfiguration) {
        this.forestConfiguration = forestConfiguration;
    }

    @Override
    public void run(ApplicationArguments args) {
        forestConfiguration.setBackendName("okhttp3");
        forestConfiguration.setMaxConnections(200);
        forestConfiguration.setMaxAsyncThreadSize(32);
        forestConfiguration.setMaxAsyncQueueSize(16);
        forestConfiguration.setMaxRetryCount(0);
    }

}
