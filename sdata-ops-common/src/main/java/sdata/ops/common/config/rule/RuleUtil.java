package sdata.ops.common.config.rule;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.extern.slf4j.Slf4j;
import sdata.ops.common.exception.RuleException;

import java.util.Arrays;

/***
 * 使用 alibaba 开源组件ql——express规则引擎
 */
@Slf4j
public class RuleUtil {
    private static final ExpressRunner runner = new ExpressRunner(true, false);

    static {
        try {
            // 别名
            runner.addOperatorWithAlias("contains", "like", null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean execute(String express, DefaultContext<String, Object> context) {
        try {
            return (boolean) runner.execute(express, context, null, false, false);
        } catch (Exception e) {
            throw new RuleException("规则引擎执行错误,检查表达式与参数是否匹配,或者执行结果类型不匹配", e);
        }
    }

    /**
     * 检查语法
     *
     * @param express 表达式
     * @return 如果表达式语法正确，返回null，否则返回错误信息
     */
    public static String lexCheck(String express) {
        try {
            runner.parseInstructionSet(express);
            return null;
        } catch (Exception e) {
            log.warn("语法解析错误", e);
            return e.getMessage();
        }
    }

//    public static void main(String[] args) {
//        Arrays.asList(
//                "age>16",
//                "age<20",
//                "age=16",
//                "age==16",
//                "age>=16",
//                "age<=16",
//                "age!=16",
//                "age contains 16",
//                "age like 16",
//                "age in 16",
//                "age not in 16",
//                "age>16&&age<20",
//                "age>16||age<20"
//        ).forEach(s -> System.out.println(s + " => " + lexCheck(s)));
//    }
}
