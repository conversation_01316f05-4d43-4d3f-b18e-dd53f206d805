package sdata.ops.common.config.feign;

import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

/***
 * feign 拦截器
 * feign发起http请求前，可以设置通用参数，比如token或者cookie其他身份令牌等
 */
@Component
public class FeignAuthInterceptor implements RequestInterceptor {


    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header("user-token", StpUtil.getTokenSession().getToken());
    }
}
