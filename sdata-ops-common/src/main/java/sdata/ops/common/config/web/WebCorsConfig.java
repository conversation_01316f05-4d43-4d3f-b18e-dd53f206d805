package sdata.ops.common.config.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebCorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        //设置允许跨域访问的路径
        registry.addMapping("/**")
                //设置允许跨域访问的源
                .allowedOriginPatterns("*")
                //允许跨域请求的方法
                .allowedMethods("*")
                //允许头部设置
                .allowedHeaders("*")
                //是否发送 cookie
                .allowCredentials(true);
    }

}
