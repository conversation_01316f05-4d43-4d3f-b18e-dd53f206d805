package sdata.ops.common.config.http;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dtflys.forest.Forest;
import com.dtflys.forest.auth.BasicAuth;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import org.springframework.http.HttpMethod;
import sdata.ops.common.core.model.HttpRequestModel;

import java.util.Map;

/***
 * api工具类
 */
public class HttpHandler {


    //自定义响应
    public static <T> T sendHttp(HttpRequestModel requestModel, Class<T> c) {
        //构建请求
        ForestRequest<Object> request = constructRequest(requestModel);
        return (T) request.execute(c);
    }


    //响应为map
    public static Map<String, Object> sendHttpCallMap(HttpRequestModel requestModel) {
        //构建请求
        ForestRequest<Object> request = constructRequest(requestModel);
        return request.executeAsMap();
    }

    //响应为json
    public static JSONObject sendHttpCallJson(HttpRequestModel requestModel) {
        ForestRequest<JSONObject> request = constructRequest(requestModel);
        ForestResponse response = request.executeAsResponse();
        if (response.isSuccess()) {
            JSONObject finalRes = new JSONObject();
            String res = response.readAsString();
            if (JSONUtil.isTypeJSONArray(res)) {
                finalRes.set("api", JSONUtil.parseArray(res));
                return finalRes;
            }
            finalRes.set("api", JSONUtil.parseObj(response.getResult()));
            return finalRes;
        }
        return null;
    }


    /**
     * 请求构建
     *
     * @param requestModel 请求体内容
     */
    private static <T> ForestRequest<T> constructRequest(HttpRequestModel requestModel) {
        ForestRequest forestRequest = requestModel.getMethod().equalsIgnoreCase(HttpMethod.GET.name()) ? Forest.get(requestModel.getUrl())
                : Forest.post(requestModel.getUrl());
        //header不为空则追加
        if (!requestModel.getHeader().isEmpty()) {
            forestRequest.addHeader(requestModel.getHeader());
        }
        //url追加参数不为空
        if (!requestModel.getQuery().isEmpty()) {
            forestRequest.addQuery(requestModel.getQuery());
        }
        //body追加参数不为空
        if (!requestModel.getBody().isEmpty()) {
            JSONObject bodyFull = requestModel.getBody();
            String type = bodyFull.getStr("type");
            String contentType = BodyType.getContentTypeByType(type);
            if ("JSON".equalsIgnoreCase(contentType)) {
                forestRequest.contentTypeJson().addBody(bodyFull.getJSONObject("data"));
            }
            if ("FORM_DATA".equalsIgnoreCase(contentType)) {
                forestRequest.contentTypeMultipartFormData().addBody(bodyFull.getJSONObject("data"));
            }
            if ("FORM".equalsIgnoreCase(contentType)) {
                forestRequest.contentFormUrlEncoded().addBody(bodyFull.getJSONObject("data"));
            }

        }
        //auth目前仅支持basic
        if (!requestModel.getAuthentication().isEmpty()) {
            String username = requestModel.getAuthentication().getStr("user");
            String password = requestModel.getAuthentication().getStr("pass");
            forestRequest.setAuthenticator(new BasicAuth(username, password));
        }
        return forestRequest;
    }

}
