package sdata.ops.common.core.util;

import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.*;

/**
 * 季度日期工具类，用于根据年份和季度生成季度的开始和结束时间。
 */
public class QuarterDateUtils {

    /**
     * 获取指定年份和季度的开始和结束时间。
     *
     * @param year    年份，例如 2021
     * @param quarter 季度，1-4
     * @return 包含 "start" 和 "end" 键的 Map，对应季度的开始和结束时间
     * @throws IllegalArgumentException 如果季度不在 1-4 之间
     */
    public static Tuple2<Date, Date> getQuarterStartEnd(int year, int quarter) {
        // 验证季度是否合法
        if (quarter < 1 || quarter > 4) {
            throw new IllegalArgumentException("季度必须在 1 到 4 之间");
        }

        // 创建 Calendar 对象以操作日期
        Calendar calendar = Calendar.getInstance();

        // 计算季度的开始月份
        int startMonth = (quarter - 1) * 3;
        calendar.set(year, startMonth, 1, 0, 0, 0); // 设置开始日期为该季度第一天的 00:00:00

        // 获取季度的开始时间
        Date startTime = calendar.getTime();

        // 计算季度的结束月份
        int endMonth = quarter * 3 - 1;
        calendar.set(year, endMonth, 1); // 设置结束日期为该月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)); // 获取该月的最后一天
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        // 获取季度的结束时间
        Date endTime = calendar.getTime();

        return Tuples.of(startTime, endTime);
    }


}