package sdata.ops.common.core.annotation;


import sdata.ops.common.enums.LogType;
import sdata.ops.common.enums.OperateType;

import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ControllerAuditLog {
    /**
     * 模块名称
     */
    String moduleName() default "";
    /**
     * 操作类型
     */
    OperateType operateType() default OperateType.OTHER;
    /**
     * 日志类型
     */
    LogType logType() default LogType.SYSTEM;
    /**
     * 操作内容
     */
    String value() default "";
}
