package sdata.ops.common.core.util;

public class CronGeneric {
    public static String generateCronExpression(int hours) {
        if (hours <= 0) {
            throw new IllegalArgumentException("Interval must be greater than 0");
        }

        // 秒（始终设置为 0）

        return "0 " +

                // 分（始终设置为 0）
                "0 " +

                // 小时（使用生成的小时列表）
                generateHours(hours) + " " +

                // 日期（每天）
                "* " +

                // 月份（每月）
                "* " +

                // 星期（每周的任何一天）
                "?";
    }

    private static String generateHours(int interval) {
        if (interval <= 0) {
            throw new IllegalArgumentException("Interval must be greater than 0");
        }

        StringBuilder hours = new StringBuilder();
        for (int hour = 6; hour <= 22; hour += interval) {
            if (hours.length() > 0) {
                hours.append(",");
            }
            hours.append(hour);
        }
        return hours.toString();
    }
}
