package sdata.ops.common.core.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2024/2/28 10:09
 */
public class MathUtil {

    /**
     * 任务完成进度，默认为100
     *
     * @param total    总数
     * @param complete 完成数
     * @return 除数结果
     */
    public static long divide(long complete, long total) {
        if (total == 0) {
            return 100;
        }
        return new BigDecimal(complete).divide(new BigDecimal(total), 2, RoundingMode.DOWN).multiply(new BigDecimal(100)).longValue();
    }

}
