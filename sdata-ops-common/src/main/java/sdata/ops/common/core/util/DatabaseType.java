package sdata.ops.common.core.util;

public enum DatabaseType {
    MYSQL("MySQL"),
    ORACLE("Oracle"),
    DM("DM"),
    UNKNOWN("Unknown");

    private final String name;

    DatabaseType(String name) {
        this.name = name;
    }

    public static DatabaseType fromProductName(String productName) {
        if (productName == null) {
            return UNKNOWN;
        }
        productName = productName.toLowerCase();
        if (productName.contains("mysql")) {
            return MYSQL;
        } else if (productName.contains("oracle")) {
            return ORACLE;
        } else if (productName.contains("dm")) {
            return DM;
        }
        return UNKNOWN;
    }
}
