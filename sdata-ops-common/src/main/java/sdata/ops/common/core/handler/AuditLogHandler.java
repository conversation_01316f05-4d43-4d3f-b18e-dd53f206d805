package sdata.ops.common.core.handler;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import sdata.ops.common.core.mapper.AuditLogMapper;
import sdata.ops.common.core.model.AuditLog;

@Component
public class AuditLogHandler extends ServiceImpl<AuditLogMapper, AuditLog> {

    /**
     * 日志入库
     */
    @Async("sdataopsTaskExecutor")
    public void log(AuditLog log) {
        super.save(log);
    }
}
