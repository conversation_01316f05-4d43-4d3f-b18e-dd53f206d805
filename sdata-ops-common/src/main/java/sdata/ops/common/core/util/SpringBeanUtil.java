package sdata.ops.common.core.util;

import lombok.Getter;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component("springBeanUtil")
public class SpringBeanUtil implements ApplicationContextAware {
    /**
     * spring上下文
     * -- GETTER --
     *  获取Spring程序上下文
     *
     * @return applicationContext

     */
    @Getter
    private static ApplicationContext applicationContext = null;

    public static<T> T getAopProxy(T invoker) {
        return (T) AopContext.currentProxy();
    }

    /**
     * Spring容器启动后，会把 applicationContext 给自动注入进来，然后把 applicationContext 赋值到静态变量中，方便后续拿到容器对象
     *
     * @see ApplicationContextAware#setApplicationContext(ApplicationContext)
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringBeanUtil.applicationContext = applicationContext;
    }

    /**
     * 根据BeanId获取对象
     *
     * @param beanId Bean标识
     * @return Bean对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanId) {
        return (T) applicationContext.getBean(beanId);
    }

    /**
     * 根据对象类型获取对象
     *
     * @param requiredType 对象类型
     * @return Bean对象
     */
    public static <T> T getBean(Class<T> requiredType) {
        return applicationContext.getBean(requiredType);
    }
}
