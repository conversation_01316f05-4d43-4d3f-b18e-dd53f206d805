package sdata.ops.common.core.model;

import cn.hutool.json.JSONObject;
import lombok.Data;

@Data
public class HttpRequestModel {

    //模型id
    private String id;
    //api名称
    private String name;

    //请求方式
    private String method;

    //数据位置
    private String jsonPath;

    //请求地址
    private String url;
    //请求参数query的
    private JSONObject query=new JSONObject();
    //请求参数body的
    private JSONObject body=new JSONObject();
    //验证
    private JSONObject authentication=new JSONObject();
    //header
    private JSONObject header=new JSONObject();
}
