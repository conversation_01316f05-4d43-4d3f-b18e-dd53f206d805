package sdata.ops.common.core.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("ops_audit_log")
@ExcelIgnoreUnannotated
public class AuditLog implements Serializable {
    @ExcelProperty(value = "id")
    private String id;
    @ExcelProperty(value = "操作时间")
    private Date operatorTime;
    @ExcelProperty(value = "模块名称")
    private String moduleName;
    @ExcelProperty(value = "操作名称")
    private String operatorName;
//    @ExcelProperty(value = "操作编码")
    private String operatorCode;
//    @ExcelProperty(value = "用户id")
    private String operatorId;
    @ExcelProperty(value = "操作人员")
    private String nickname;
    @ExcelProperty(value = "操作ip")
    private String operatorIp;
    @ExcelProperty(value = "操作地点")
    private String clientLocation;
    @ExcelProperty(value = "请求url")
    private String url;
//    @ExcelProperty(value = "日志编码")
    private String logCode;
//    @ExcelProperty(value = "日志名称")
    private String logName;
    @ExcelProperty(value = "日志状态")
    private String status;
    @ExcelProperty(value = "请求体")
    private String operatorContent;
    @ExcelProperty(value = "响应体")
    private String operatorResult;
//    @ExcelProperty(value = "追踪id")
    private String traceId;
}
