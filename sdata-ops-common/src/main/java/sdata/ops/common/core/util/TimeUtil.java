package sdata.ops.common.core.util;

import cn.hutool.core.date.DateUtil;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class TimeUtil {

    TimeUtil(){}


    public static String convertToStandardFormat(String dateStr) {
        List<String> formats = Arrays.asList(
                "yyyy-MM-dd", "yyyy/MM/dd", "dd-MM-yyyy", "dd/MM/yyyy",
                "MM-dd-yyyy", "MM/dd/yyyy", "yyyy.MM.dd", "dd.MM.yyyy",
                "MM.dd.yyyy", "yyyy-M-d", "yyyy/M/d", "d-M-yyyy", "d/M/yyyy",
                "M-d-yyyy", "M/d/yyyy", "yyyy.M.d", "d.M.yyyy", "M.d.yyyy"
                // 你可以根据需要添加更多格式
        );

        for (String format : formats) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                LocalDate date = LocalDate.parse(dateStr, formatter);
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (DateTimeParseException e) {
                // 如果当前格式不匹配，则尝试下一个格式
            }
        }

        throw new IllegalArgumentException("无法解析的日期格式: " + dateStr);
    }


    /**
     * 校验时间字符串
     * @param dateStr time string
     * @return  boolean
     */
    public static Boolean validate_yyyyMMdd(String dateStr){
        if(!StringUtils.hasText(dateStr)){
            return false;
        }
        try {
            DateUtil.parse(dateStr,"yyyyMMdd HH:mm");
        }catch (Exception e){
            return false;
        }
        return true;
    }

    /**
     * 校验时间字符串  yyyy-MM-dd HH:mm
     * @param dateStr time string
     * @return  boolean
     */
    public static Boolean validate_yyyy_MM_dd(String dateStr){
        if(!StringUtils.hasText(dateStr)){
            return false;
        }
        try {
            DateUtil.parse(dateStr,"yyyy-MM-dd HH:mm");
        }catch (Exception e){
            return false;
        }
        return true;
    }

}
