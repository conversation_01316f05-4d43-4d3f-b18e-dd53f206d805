package sdata.ops.common.core.aspect;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.net.NetUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.handler.AuditLogHandler;
import sdata.ops.common.core.model.AuditLog;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.common.filter.TraceIdInterceptor;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Optional;
import java.util.function.Predicate;

@Aspect
@Component
//@ConditionalOnProperty(value = "sdata.auditLog.enabled", havingValue = "true", matchIfMissing = true)
public class AuditLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(AuditLogAspect.class);
    /**
     * 判断ip地址是否为ipv4
     */
    private static final Predicate<String> IS_IP_V4 = PatternPool.IPV4.asMatchPredicate();

    private AuditLogHandler auditLogHandler;

    @Autowired
    public void setAuditLogHandler(AuditLogHandler auditLogHandler) {
        this.auditLogHandler = auditLogHandler;
    }

    @Pointcut("@annotation(sdata.ops.common.core.annotation.ControllerAuditLog)")
    public void pointcut() {
    }

    @AfterReturning(pointcut = "pointcut()", returning = "result")
    public void afterReturn(JoinPoint joinPoint, Object result) {
        try {
            ControllerAuditLog tag = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(ControllerAuditLog.class);
            var infoOpt = extraReq(joinPoint, tag, result);
            boolean recordFlag = false;
            if ("SECURITY".equals(tag.logType().name())) {
                //如果当前日志是安全管理员操作日志，并启用记录安全管理日志
                recordFlag = true;
            }

            if ("SYSTEM".equals(tag.logType().name())) {
                //如果当前日志是系统日志
                recordFlag = true;
            }

            if (recordFlag && infoOpt.isPresent()) {
                auditLogHandler.log(infoOpt.get());
            }
            if (infoOpt.isPresent()) {
                var info = infoOpt.get();
                logger.info("client ip {} operate module {} success, record {},operateType {},logType {},result {}",
                        info.getOperatorIp(), tag.moduleName(), recordFlag, tag.operateType().DESC, tag.logType().name(), info.getOperatorResult());
            }
        } catch (Exception e) {
            // 防止记录日志影响业务
            logger.error("日志记录异常", e);
        }
    }

    @AfterThrowing(pointcut = "pointcut()", throwing = "ex")
    public void afterThrow(JoinPoint joinPoint, Throwable ex) {
        try {
            ControllerAuditLog tag = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(ControllerAuditLog.class);
            var infoOpt = extraReq(joinPoint, tag, ex);
            if (infoOpt.isPresent()) {
                var info = infoOpt.get();
                auditLogHandler.log(info);
                logger.info("client ip {} operate module {} exception,operateType {},logType {},result {}", info.getOperatorIp(),
                        tag.moduleName(), tag.operateType().DESC, tag.logType().name(), info.getOperatorResult());
            }
        } catch (Exception e) {
            // 防止记录日志影响业务
            logger.error("日志记录异常", e);
        }
    }

    /**
     * 解析请求
     *
     * @param joinPoint     切面
     * @param tag           注解
     * @param resultOrThrow 返回结果或异常对象
     * @return 请求信息
     */
    private Optional<AuditLog> extraReq(JoinPoint joinPoint, ControllerAuditLog tag, Object resultOrThrow) {
        RequestAttributes attrs = RequestContextHolder.currentRequestAttributes();
        if (!(attrs instanceof ServletRequestAttributes)) {
            // 非web请求
            return Optional.empty();
        }

        HttpServletRequest req = ((ServletRequestAttributes) attrs).getRequest();
        // 内容限制长度
        int limit = 5000;

        String userId = StpUtil.getLoginIdAsString();
        String nickname = String.valueOf(StpUtil.getExtra("name"));
        String clientIp = ServletUtil.getClientIP(req);
        String ipLocation;
        if ((IS_IP_V4.test(clientIp) && NetUtil.isInnerIP(clientIp)) || "0:0:0:0:0:0:0:1".equals(clientIp)) {
            ipLocation = "内网";
        } else {
            ipLocation = "公网";
        }
        String url = limitString(req.getMethod() + " " + req.getRequestURI() + "?" + req.getQueryString(), limit);
        String traceId = MDC.get(TraceIdInterceptor.TRACE_ID);
        String reqBody = limitString(getBody(joinPoint), limit);
        String result;
        String status;
        if (resultOrThrow instanceof Throwable) {
            result = ExceptionUtil.stacktraceToString((Throwable) resultOrThrow, limit);
            status = "异常";
        } else if (resultOrThrow instanceof String) {
            result = limitString(((String) resultOrThrow), limit);
            status = "成功";
        } else {
            result = limitString(JsonUtils.toJsonString(resultOrThrow), limit);
            status = "成功";
        }

        AuditLog entity = new AuditLog()
                .setId(IdWorker.getIdStr())
                .setOperatorTime(new Date())
                .setModuleName(tag.moduleName())
                .setOperatorName(tag.operateType().DESC)
                .setOperatorCode(tag.operateType().name())
                .setOperatorId(userId)
                .setNickname(nickname)
                .setOperatorIp(clientIp)
                .setClientLocation(ipLocation)
                .setUrl(url)
                .setLogCode(tag.logType().name())
                .setLogName(tag.logType().DESC)
                .setStatus(status)
                .setOperatorContent(reqBody)
                .setOperatorResult(result)
                .setTraceId(traceId);
        return Optional.of(entity);
    }

    private String getBody(JoinPoint joinPoint) {
        // 1. 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();

        // 2. 获取方法参数和参数注解
        Object[] args = joinPoint.getArgs();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        // 3. 遍历参数注解，查找标记了@RequestBody的参数
        for (int i = 0; i < parameterAnnotations.length; i++) {
            Annotation[] annotations = parameterAnnotations[i];
            for (Annotation annotation : annotations) {
                if (annotation instanceof RequestBody) {
                    // 返回找到的第一个标记了@RequestBody的参数
                    Object arg = args[i];
                    if (arg instanceof String) {
                        return ((String) arg);
                    } else {
                        return JsonUtils.toJsonString(arg);
                    }
                }
            }
        }

        // 未找到标记了@RequestBody的参数
        return "";
    }

    /**
     * 限制长度
     *
     * @param str   字符串
     * @param limit 限制长度
     * @return 限制了长度的字符串
     */
    private static String limitString(String str, int limit) {
        if (str == null) {
            return "";
        }
        if (str.length() > limit) {
            return str.substring(0, limit) + "...";
        }
        return str;
    }
}
