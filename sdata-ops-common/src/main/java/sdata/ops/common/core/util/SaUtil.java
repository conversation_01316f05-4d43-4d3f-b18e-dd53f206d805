package sdata.ops.common.core.util;

import cn.dev33.satoken.stp.StpUtil;

public class SaUtil {

    public static String username() {
        Object username = StpUtil.getExtra("username");
        return username == null ? "" : username.toString();
    }

    public static String nickname() {
        Object username = StpUtil.getExtra("name");
        return username == null ? "" : username.toString();
    }

    public static String userId() {
        return StpUtil.getLoginIdAsString();
    }
}
