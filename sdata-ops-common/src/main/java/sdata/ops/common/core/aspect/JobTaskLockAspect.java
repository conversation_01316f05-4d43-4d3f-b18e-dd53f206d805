package sdata.ops.common.core.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.common.core.annotation.JobTaskDistributedLock;

import java.lang.reflect.Method;

@Aspect
@Component
public class JobTaskLockAspect {


    private JdbcTemplate jdbcTemplate;

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private static final Logger logger = LoggerFactory.getLogger(JobTaskLockAspect.class);

    @Around("@annotation(sdata.ops.common.core.annotation.JobTaskDistributedLock)")
    @Transactional(rollbackFor = Exception.class)
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        JobTaskDistributedLock annotation = method.getAnnotation(JobTaskDistributedLock.class);
        String lockKey = annotation.lockId();
        try {
            // 尝试插入锁记录
            jdbcTemplate.update("insert into warn_job (id, warn_id, job_id) values (?, ?, ?)", lockKey, "131", 1);
            // 成功插入记录，获取到锁
            try {
                return joinPoint.proceed();
            } finally {
                // 立即释放锁
                jdbcTemplate.update("delete from warn_job where id=?", lockKey);
            }
        }catch (Exception e) {
            // 插入失败，主键冲突，未获取到锁
            logger.error("create_task_每日任务创建执行出错", e);
            throw new RuntimeException("");
        }
    }
}
