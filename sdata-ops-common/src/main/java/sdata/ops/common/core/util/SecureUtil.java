package sdata.ops.common.core.util;

import cn.dev33.satoken.stp.StpUtil;

import java.util.ArrayList;
import java.util.List;

public class SecureUtil {

    private SecureUtil() {
    }


    public static String currentUserName() {
        return StpUtil.getExtra("username").toString();
    }

    public static String currentUserId() {
        return StpUtil.getExtra("userId").toString();
    }

    public static String safeUserId() {
        try {
            return StpUtil.getExtra("userId").toString();
        } catch (Exception e) {
            return "-1";
        }
    }

    public static boolean isAdmin(String userId) {
        if (StpUtil.getExtra("username").toString().equals("admin")) {
            return true;
        }
        return StpUtil.getExtra("role") != null && List.of(StpUtil.getExtra("role").toString().split(",")).contains("1");
    }

}
