package sdata.ops.common.core.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.IOException;
import java.util.Map;

public class JsonUtils {

    private static  final ObjectMapper mapper = new ObjectMapper();

    static {
        // json里有多余字段的时候，忽略掉
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    public static Map<String, Object> toMap(String obj) {
        try {
            return mapper.readValue(obj, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
    public static JsonNode toJsonObj(String obj) {
        try {
            return mapper.readTree(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static Tuple2<Boolean,String> isValid(String json) {
        if(!StringUtils.hasText(json)){
            throw  new RuntimeException("json字符串为空:异常中断");
        }
        try {
            JSONUtil.parseObj(json);
            return  Tuples.of(true,"obj");
        }catch (JSONException e){
            try{
                JSONUtil.parseArray(json);
                return Tuples.of(true,"arr");
            }catch (JSONException el){
                return Tuples.of(true,"fail");
            }
        }
    }

    public static String toJsonString(Object object) {
        if (ObjectUtil.isNull(object)) {
            return null;
        }
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String text, Class<T> clazz) {
        if (!StringUtils.hasLength(text)) {
            return null;
        }
        try {
            return mapper.readValue(text, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
