package sdata.ops.common.core.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.AES;

import java.util.regex.Pattern;

public class AesUtils {

    private static final AES aes = new AES("ECB", "PKCS7Padding", "SDATASINGULARITY".getBytes());

    /**
     * 字符串加密
     *
     * @param str
     * @return
     */
    public static String encryptBase64(String str) {
        if (StrUtil.isEmptyIfStr(str)) {
            return str;
        }
        // 如果是加密数据，不进行二次加密
        if (checkBase64(str)) {
            return str;
        }
        // 返回加密数据
        return aes.encryptBase64(str);
    }

    /**
     * 字符串解密
     *
     * @param str
     * @return
     */
    public static String decryptStr(String str) {
        if (StrUtil.isEmptyIfStr(str)) {
            return str;
        }
        // 如果不是是加密数据，不处理
        if (!checkBase64(str)) {
            return str;
        }
        // 返回解密数据
        return aes.decryptStr(str);
    }

    /**
     * 判断字符串是否为加密
     *
     * @return
     */
    public static boolean checkBase64(String str) {
        String base64Rule = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Rule, str);
    }

//    public static void main(String[] args) {
//        // 解密数据
//        String decryptStr = "vmDCstEch8QWdGsxUcEz6Iaxm+DOAhhgE2NHrPSEcDeah9Dg2Otv9lPi4KWchdNG";
//        System.out.println(aes.encryptBase64(decryptStr));
//    }
}
