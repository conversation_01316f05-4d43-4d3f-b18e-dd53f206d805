package sdata.ops.common.core.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import sdata.ops.common.api.R;
import sdata.ops.common.api.ResultCode;

import java.sql.SQLTimeoutException;
import java.util.stream.Collectors;

import static javax.servlet.http.HttpServletResponse.SC_BAD_REQUEST;


@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 接口层的校验注解抛异常，在这里格式化消息
     *
     * @param e 绑定的异常
     * @return 返回格式化后的消息
     */
    @ExceptionHandler({BindException.class})
    public R<String> bindException(BindException e) {
        String msg = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(err -> err.getDefaultMessage() + ":" + err.getField())
                .collect(Collectors.joining(", "));
            return R.fail(SC_BAD_REQUEST, msg);
    }

    /**
     * 全局捕捉异常，不展示错误信息
     *
     * @param e 异常
     * @return 异常信息
     */
    @ExceptionHandler(value = Exception.class)
    public R<String> exceptionHandler(final Exception e) {
        log.error(e.getMessage(), e);
        Throwable cause = e.getCause();
        if (e.getClass() == MaxUploadSizeExceededException.class) {
            return R.data(ResultCode.FAILURE.getCode(), e.toString(), "excel大小超过限制");
        }
        if ((cause instanceof SQLTimeoutException)) {
            return R.data(ResultCode.FAILURE.getCode(), e.toString(), "您的数据查询SQL语句执行超过了10秒，请优化SQL语句后再进行重试");
        }
        String message = e.getMessage();
        if (message == null) {
            message = e.getCause().getMessage();
        }
        return R.fail(message);
    }
}
