package sdata.ops.common.filter;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import sdata.ops.common.api.R;
import sdata.ops.common.config.propertites.LoginConfigParams;
import sdata.ops.common.core.handler.TokenHandler;
import sdata.ops.common.core.model.TokenResult;
import sdata.ops.common.core.util.SpringBeanUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 登录权限拦截器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class LoginInterceptor implements HandlerInterceptor {

    private final LoginConfigParams loginConfig;


    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        //鉴权开关，默认开启
        if (loginConfig.getAuth()) {
            String userToken = request.getHeader("user-token");
            //系统自身token与LDAP登录类型校验,过滤器无需做特殊配置,前端重新调用登录接口
            if (List.of("SYSTEM", "LDAP", "CAS").contains(loginConfig.getLoginModel())) {
                if (!StpUtil.isLogin()) {
                    LoginConfigParams.writeJsonResponse(response, R.fail(401, "用户登录已失效"));
                    return false;
                }
            }
            //token类型登录配置，过滤器需要特殊处理，系统登录已不在使用且需要判token代表用户是否存在，是否切换身份
            if (loginConfig.getLoginModel().equals("TOKEN")) {
                //如果未登录,重定向到系统登录接口，解析token进行静默登录
                if (!StpUtil.isLogin()) {
                    LoginConfigParams.writeJsonResponse(response, R.fail(401, "用户登录已失效"));
                    return false;
                } else {
                    TokenHandler tokenHandler = SpringBeanUtil.getBean(loginConfig.getTokenHandler());
                    TokenResult result = tokenHandler.verifyToken(userToken);
                    //token校验不通过
                    if (!result.getState()) {
                        LoginConfigParams.writeJsonResponse(response, R.fail("token校验失败"));
                        return false;
                    }
                    //从用户端token校验用户与当前登录用户id不符合则认定父端应用已经切换身份
                    if (!result.getAccount().equals(StpUtil.getExtra("userId"))) {
                        LoginConfigParams.writeJsonResponse(response, R.fail(401, "token登录身份已切换"));
                        return false;
                    }
                }
            }
        }
        return true;
    }
}
