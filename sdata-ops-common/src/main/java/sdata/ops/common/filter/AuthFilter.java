package sdata.ops.common.filter;

import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AuthFilter implements WebMvcConfigurer {


    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }

    private LoginInterceptor loginInterceptor;

    @Autowired
    public void setSaTokenInterceptor(LoginInterceptor loginInterceptor) {
        this.loginInterceptor = loginInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加traceId拦截器
        registry.addInterceptor(new TraceIdInterceptor()).addPathPatterns("/**");

        registry.addInterceptor(loginInterceptor).order(0).
                addPathPatterns("/**").
                excludePathPatterns("/loginController/**", "/error","/standControl/test","/actuator/**","/exp/exportExcel");
    }


}
