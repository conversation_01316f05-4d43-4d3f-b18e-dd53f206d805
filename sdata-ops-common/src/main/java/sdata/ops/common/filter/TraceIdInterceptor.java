package sdata.ops.common.filter;

import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

public class TraceIdInterceptor implements HandlerInterceptor {
    public static final String TRACE_ID = "Trace-Id";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = request.getHeader(TRACE_ID);
        if (!(traceId == null || traceId.isEmpty())) {
            // 从请求头里取traceId
            MDC.put(TRACE_ID, traceId);
        } else {
            // 往日志MDC里放trace_id
            MDC.put(TRACE_ID, UUID.randomUUID().toString().replace("-", ""));
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        response.setHeader(TRACE_ID, MDC.get(TRACE_ID));
        // 清除traceId
        MDC.remove(TRACE_ID);
    }
}
