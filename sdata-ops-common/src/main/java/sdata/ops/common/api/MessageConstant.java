package sdata.ops.common.api;

public interface MessageConstant {
    String ADD_SUCCESS = "新增成功";

    String UPDATE_SUCCESS = "修改成功";

    String DELETE_SUCCESS = "删除成功";

    String PARAM_FAIL = "参数错误";

    String ROLE_EXISTS = "角色标识已存在";

    String MANAGER_UPDATE_FAIL = "管理员角色为系统角色，无法进行操作";

    String DEFAULT_UPDATE_FAIL = "默认角色为系统角色，无法进行操作";

    String LOGIN_FAIL = "用户名或密码错误";

    String CAS_LOGIN_FAIL = "CAS认证失败";

    String LOGIN_SUCCESS = "登录成功";

    String ACCOUNT_NOT_EXISTS = "用户不存在";

    String ACCOUNT_DELETE = "用户已注销";

    String ACCOUNT_LOCK = "用户已冻结";

    String ACCOUNT_EXISTS = "用户已存在";

    String PASSWORD_VALIDATE_ERR = "密码必须包含至少一个数字、一个字母、一个特殊字符（@#$%^&+=_），不能包含空格，并且长度在8到16位之间!";

    String ROLE_USER_BIND = "角色已经分配用户，无法删除";

    String MENU_EXISTS_CHILD = "菜单存在下级节点,删除失败";

    String PARAM_MISS = "参数错误";

    String DATASOURCE_MISS = "未查询到数据源";

    String SQL_MISS = "未查询到SQL语句";

    String JS_MISS = "未查询到JS脚本";

    String JAVA_MISS = "未查询到Java源码";

    String PYTHON_MISS = "未查询到python源码";

    String SOURCE_CONN_ERROR = "数据源连接失败";

    String CONN_SUCCESS = "连接成功";

    String CONN_FAIL = "连接失败";

    String SAVE_SUCCESS = "保存成功";

    String OPERATOR_SUCCESS = "操作成功";

    String OPERATOR_FAIL = "操作失败";

    String COPY_SUCCESS = "复制成功";

    String DELETE_FAIL_SOURCE = "删除失败,数据源已关联指标";

    String DELETE_FAIL_GROUP = "删除失败,存在子分组";

    String DELETE_FAIL_INDICATOR = "删除失败,该分组已关联指标";

    String REDUCTION_SUCCESS = "还原成功";

    String SQL_PARSE_ERROR = "SQL解析异常:";

    String DATA_NOT_EXISTS = "数据不存在";
}
