package sdata.ops.common.api;

/**
 * 任务调度通用常量
 *
 * <AUTHOR>
 */
public class ScheduleConstants {
    public static final String TASK_CLASS_NAME = "TASK_CLASS_NAME";

    /**
     * 执行目标key
     */
    public static final String TASK_PROPERTIES = "TASK_PROPERTIES";

    /**
     * 默认
     */
    public static final String MISFIRE_DEFAULT = "0";

    /**
     * 立即触发执行
     */
    public static final String MISFIRE_IGNORE_MISFIRES = "1";

    /**
     * 触发一次执行
     */
    public static final String MISFIRE_FIRE_AND_PROCEED = "2";

    /**
     * 不触发立即执行
     */
    public static final String MISFIRE_DO_NOTHING = "3";

    public static final  String GROUP_DEF="group_dynamic";
    /**
     * 监控中心的任务组
     */
    public static final String GROUP_WARN_CENTER = "WARN_CENTER";

    public static final String SCH_TYPE_DYNAMIC="2";
    public static final String EXECUTION_ID = "execution_id";
    /**
     * 监控中心-监控单元的同步任务
     */
    public static final String SCH_TYPE_WARN_SYNC = "warnSync";
    /**
     * 监控中心-监控单元的告警任务
     */
    public static final String SCH_TYPE_WARN_ALERT = "warnAlert";

    public enum Status {
        /**
         * 正常
         */
        NORMAL("0"),
        /**
         * 暂停
         */
        PAUSE("1");

        private String value;

        private Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
