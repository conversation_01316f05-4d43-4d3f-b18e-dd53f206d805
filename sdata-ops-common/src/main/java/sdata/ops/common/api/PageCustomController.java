package sdata.ops.common.api;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

public class PageCustomController {

    public static  <T, F> Map<String, Object> customPage(IPage<T> res, Function<T, F> converter) {
        Map<String,Object> finalRes = new HashMap<>(7);
        finalRes.put("total", res.getTotal());
        finalRes.put("records", res.convert(converter).getRecords());
        finalRes.put("pageNo", res.getCurrent());
        finalRes.put("pageSize", res.getSize());
        finalRes.put("isLastPage", res.getPages() == 0 || res.getPages() == res.getCurrent());
        return finalRes;
    }
}
