package sdata.ops.common.api;

public interface CommonConstant {
    /**
     * 数据库名
     */
    String DB_NAME = "sdata_ops";

    /**
     * 菜单树根节点
     */
    String MENU_TREE_ROOT_ID = "-1";

    /**
     * 删除标识 0、未删除
     */
    Integer DEL_FLAG_0 = 0;

    /**
     * 删除标识 1、已删除
     */
    Integer DEL_FLAG_1 = 1;

    String JS_METHOD_NAME = "execute";

    String INDICATOR_PREFIX = "indicator:";

    /**
     * 指令监控结果和结果集
     */

    String MONITOR_PREFIX_RES = "monitor:result";

    /**
     * 监控时间节点结果和结果集
     */
    String NODE_PREFIX_RES = "node:result";

    /**
     * 风险排查结果和结果集
     */
    String RISK_PREFIX = "risk:";

    String RISK_PREFIX_RES = "risk:result";

    /**
     * Websocket推送类型
     */
    String MONITOR_TYPE = "indicatorMonitor";

    String MONITOR_GROUP = "indicatorMonitor:group";

    String MONITOR_NODE = "indicatorMonitor:node";

    String RISK_TYPE = "indicatorRisk";

    /**
     * 是否为节假日：0/1
     */
    String HOLIDAY_Y = "1";
    String HOLIDAY_N = "0";

    /**
     * 是否为半日市日：0/1
     */
    String HALF_DAY_Y = "1";
    String HALF_DAY_N = "0";

    /**
     * 是否为休市日：0/1
     */
    String STOP_TRADE_DAY_Y = "1";
    String STOP_TRADE_DAY_N = "0";

    /**
     * 是否为交易日：Y\N
     */
    String TRADE_Y = "Y";
    String TRADE_N = "N";

    /**
     * 交易市场标识（1：A股，2：港股，3：银行间）
     */
    String MARKET_A = "1";

    String MARKET_H = "2";

    String MARKET_IB = "3";


}
