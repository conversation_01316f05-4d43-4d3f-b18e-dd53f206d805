server:
  port: 8088
  servlet:
    context-path: /ops

micro-service:
  local-switch-url: http://localhost:8080/ops #openfeign避开负载均衡配置，不为空指定url等同于httpclient效果
  local-method: true #是否开启openfeign代理bean的替换
spring:
  # mysql
  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *************************************************************************************************************************************************************
#    username: root
#    password: maxkey
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: sData#888
    type: com.zaxxer.hikari.HikariDataSource # JDBC 连接池类型：HikariCP
    hikari:
      connection-timeout: 30000     # 等待连接池分配链接的最大时长（毫秒），超过这个时长还没有可用的连接则发生 SQLException，默认：30 秒
      minimum-idle: 12              # 最小连接数
      maximum-pool-size: 24         # 最大连接数
      auto-commit: true             # 自动提交
      idle-timeout: 600000          # 连接超时的最大时长（毫秒），超时则被释放（retired），默认：10 分钟
      pool-name: DataSourceHikariCP # 连接池名称
      max-lifetime: 1800000         # 连接的生命时长（毫秒），超时而且没被使用则被释放（retired），默认： 30 分钟
  # redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 2
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: sData888
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 4
        # 连接池中的最小空闲连接
        min-idle: 1
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          threadPool:
            threadCount: 5
          scheduler:
            instanceName: taskScheduler
          jobStore:
           tablePrefix: QRTZ_
    jdbc:
      initialize-schema: never
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
#feign客户端配置为okhttp框架
feign:
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true
#quartz配置
#日志配置
logging:
  config: classpath:logback.xml
  level:
    root: info
#文件配置
#登录认证框架
sa-token:
  jwt-secret-key: sightwise#8%$
  active-timeout: 86400
  is-read-cookie: false
  token-name: user-token
#系统登录配置  SYSTEM|系统用户认证  CAS|sso认证  TOKEN|系统内嵌  LDAP|域控认证
ops:
  login:
    config:
      login-model: SYSTEM
      ldap-dc:
      ldap-url:
      auth: true
file:
  upload-dir: /usr/local/sdata/ops/file/
mybatis-plus:
  configuration:
    default-fetch-size: 200
#    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 控制台打印SQL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 控制台打印SQL
#  global-config:
#    db-config:
#      logic-delete-field: delFlag # 全局逻辑删除字段名(deleted为实体类属性名称)
#      logic-delete-value: 2 # 逻辑已删除值。可选，默认值为 1
#      logic-not-delete-value: 0 # 逻辑未删除值。可选，默认值为 0
third:
  oa:
   url: http://172.21.238.43
management:
  endpoints:
    web:
      exposure:
        include: "monitor"
