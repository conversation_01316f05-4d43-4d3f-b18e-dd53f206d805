package sdata.ops.controller;


import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import com.sun.management.OperatingSystemMXBean;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.endpoint.web.annotation.RestControllerEndpoint;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import sdata.ops.common.api.R;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.core.util.RedisUtil;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.config.DynamicControllerInvoker;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.lang.management.ManagementFactory;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@Component
@RestControllerEndpoint(id = "monitor")
@RequiredArgsConstructor
public class StandController {


    private final DynamicControllerInvoker dynamicControllerInvoker;

    private final OperatingSystemMXBean osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();


    private final DataSource dataSource;

    private final RedisUtil redisUtil;



    @Value("${third.mail.url:null}")
    private String mailAddress;

    @Value("${third.oa.url:null}")
    private String oaAddress;

    @ControllerAuditLog(value = "测试进程内调用control方法", operateType = OperateType.EXECUTE, moduleName = ModuleName.SYSTEM)
    @GetMapping("/invoker")
    public R<Object> testInvoker(String path) throws Exception {
        return R.data(dynamicControllerInvoker.invokeControllerMethod(path, null));
    }


    @ControllerAuditLog(value = "服务器状态", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    @GetMapping
    public R<Object> monitor() throws Exception {
        JSONObject res=new JSONObject();
        Map<String,Object> info=new HashMap<>();
        info.put("CpuUsage", NumberUtil.round(osBean.getSystemCpuLoad() * 100, 2) + "%");
        long totalMemory = osBean.getTotalPhysicalMemorySize();
        long freeMemory = osBean.getFreePhysicalMemorySize();
        info.put("MemoryUsage", NumberUtil.round((double) (totalMemory - freeMemory) / totalMemory * 100, 2) + "%");
        File root = new File("/");
        long totalSpace = root.getTotalSpace();
        long usableSpace = root.getUsableSpace();
        info.put("DiskUsage", NumberUtil.round((double) (totalSpace - usableSpace) / totalSpace * 100, 2) + "%");
        try {
            redisUtil.set("monitor", "c");
            res.set("bes-CacheServer", "up");
        } catch (RuntimeException e) {
            res.set("bes-CacheServer", "down");
        }
        Map<String,Object> connect=new HashMap<>();

        try {
            dataSource.getConnection().isValid(1000);
            connect.put("DMConnectStatus", "up");
        } catch (SQLException e) {
            connect.put("DMConnectStatus", "down");
        }
        connect.put("OaConnectStatus",testThirdPartyPort(oaAddress)?"up":"down");
        connect.put("MailConnectStatus",testEmailPort(mailAddress)?"up":"down");
        res.set("bes-AppServer","up");
        res.set("bes-WebServer",isBws()?"up":"down");
        res.set("connect",connect);
        res.set("instance",info);
        return R.data(res);
    }

    public static boolean isBws() throws Exception {
        try {
            URL url = new URL("http://localhost:8090");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(2000);
            connection.setReadTimeout(2000);
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
//                // 读取响应内容
//                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
//                String line;
//                StringBuilder response = new StringBuilder();
//                while ((line = reader.readLine()) != null) {
//                    response.append(line);
//                }
//                reader.close();
//                // 检查响应内容中是否包含Vue页面的特征字符串
//                // 例如，检查是否有"<div id='app'>"这样的DOM元素
                return true;
            } else {
                return false;
            }
        }catch (Exception e){
            return false;
        }
    }

    public static boolean testEmailPort(String host) {
        return testPort(host, 143);
    }
    public static boolean testThirdPartyPort(String host) {
        return testPort(host, 80);
    }
    private static boolean testPort(String host, int port) {
        if(!StringUtils.hasText(host)){
            return false;
        }
        host=host.replace("http://","").replace("https://","");
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), (5000));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
