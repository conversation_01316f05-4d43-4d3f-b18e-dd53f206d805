package sdata.ops.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/exp")
@RequiredArgsConstructor
public class ExportController {


    private final OpsTaskAttrBasicReplicaService replica;

    @GetMapping("/exportExcel")
    public  void httpExport(@RequestParam("templateId") String templateId, HttpServletResponse response) {
        List<OpsTaskAttrBasicReplica> replicaList = replica.list(Wrappers.lambdaQuery(OpsTaskAttrBasicReplica.class).last("where id in( SELECT\n" +
                "        b.task_replica_id\n" +
                "        FROM\n" +
                "        ops_task_template_relation b\n" +
                "        WHERE\n" +
                "        b.template_id = "+templateId+")"));
        // 1. 找出所有 parentId = 0 的根节点
        List<OpsTaskAttrBasicReplica> rootNodes = replicaList.stream()
                .filter(replica -> replica.getParentId().equals("0"))
                .collect(Collectors.toList());
        // 2. 为根节点设置 task_no
        for (int i = 0; i < rootNodes.size(); i++) {
            rootNodes.get(i).setTaskNo(String.valueOf(i + 1));
        }

        // 3. 为非根节点设置 task_no
        for (OpsTaskAttrBasicReplica replica : replicaList) {
            if (!replica.getParentId().equals("0")) {
                // 查找父节点
                OpsTaskAttrBasicReplica parent = replicaList.stream()
                        .filter(r -> r.getId().equals(replica.getParentId()))
                        .findFirst()
                        .orElse(null);

                if (parent != null) {
                    // 查找父节点下的所有子节点
                    List<OpsTaskAttrBasicReplica> siblings = replicaList.stream()
                            .filter(r -> r.getParentId().equals(parent.getId()))
                            .collect(Collectors.toList());

                    // 查找当前节点在兄弟节点中的位置
                    int index = siblings.indexOf(replica);
                    replica.setTaskNo(parent.getTaskNo() + "-" + (index + 1));
                }
            }
        }
        // 输出结果
        // 创建一个新的工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "内容", "经办", "复核", "开始时间", "截止时间", "说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < replicaList.size(); i++) {
            OpsTaskAttrBasicReplica replica = replicaList.get(i);
            Row row = sheet.createRow(i + 1);

            row.createCell(0).setCellValue(replica.getTaskNo());
            row.createCell(1).setCellValue(replica.getTaskName());
            row.createCell(2).setCellValue(replica.getTaskOwnerVal());
            row.createCell(3).setCellValue(replica.getTaskCheckVal());
            row.createCell(4).setCellValue(replica.getTaskStartTime());
            row.createCell(5).setCellValue(replica.getTaskEndTime());
            row.createCell(6).setCellValue(replica.getTaskDesc());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
       response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=template.xlsx");

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
