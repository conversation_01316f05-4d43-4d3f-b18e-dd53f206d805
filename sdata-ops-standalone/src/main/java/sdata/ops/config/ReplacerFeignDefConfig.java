package sdata.ops.config;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import sdata.ops.common.config.feign.FeignLocal;

import java.util.HashMap;
import java.util.Map;

/**
 * 由于单体项目合并了多个子项目，子项目中可能存在openfeign调用
 * 需要http调用导致增加耗时，所以配置此类对openfeign接口类生成的bean进行手动检查
 * 并替换为接口的实现类的bean进行方法调用，由此不在进行http访问，减少网络i/o
 * 注意* 如果要实现openfeign中http调用改为本地接口调用，必须要对接口类实现对应的@component或者@Service类才能被检查到bean并且进行替换DI。
 * 如果没有对应实现类的bean声明则还是openfeign默认生成的bean进行http调用
 */
@Component
@Slf4j
public class ReplacerFeignDefConfig implements BeanPostProcessor, ApplicationContextAware {

    private ApplicationContext applicationContext;


    @Value("${micro-service.local-method}")
    private boolean local = false;

    /***
     * 使用bean生成周期中后置处理器，判断openfeign生成的bean进行动态拦截替换为对应接口实现类的bean
     * @param bean the new bean instance
     * @param beanName the name of the bean
     */
    @Override
    public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        if (!local) {
            return bean;
        }
        //bean的自身class对象
        Class<?> iclass = bean.getClass();
        //bean所有实现的接口类
        Class<?>[] arrClazz = iclass.getInterfaces();
        for (Class<?> clazz : arrClazz) {
            //1.如果实现类中有feignClient注解则进入检查范围
            if (clazz.isAnnotationPresent(FeignClient.class)) {
                System.out.println(beanName + "=== " + bean.toString());
                log.info("检查到feign注解接口实现类::{}", iclass.getSimpleName());
                //2.如果bean本身类对象包含了component注解或者service注解则判定为feign接口类的实现类不做拦截，进入applicationContext中
                if ((iclass.isAnnotationPresent(Component.class) || iclass.isAnnotationPresent(Service.class)) &&
                        !bean.toString().contains("HardCodedTarget")) {
                    log.info("{} bean为本地实现类直接return", iclass.getSimpleName());
                    return bean;
                }
                //3.如果不包含则通过类的类型检查是否有对应的bean的实现即2步骤所检查到的bean
                //理论上spring声明的bean生成是比feign自定义beanFactory生成要早的,如果晚了也不影响，getBean方法已经完美处理了
                Class<?> feignInterface = bean.getClass().getInterfaces()[0];
                // 查找对应的实现类
                Object target = findCustomImplementation(feignInterface);
                if (target != null) {
                    return target;
                }
                log.info("未检查到{}有实现类,默认return openfeign生成的bean", iclass.getSimpleName());
                return bean;
            }
        }
        return bean;

    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 动态查找自定义实现类
     */
    private Object findCustomImplementation(Class<?> feignClientInterface) {
        // 查找所有带有 @CustomFeignImplementation 注解的 Bean
        Map<String, Object> customFeignImplementations = applicationContext.getBeansWithAnnotation(FeignLocal.class);
        for (Object implementation : customFeignImplementations.values()) {
            // 获取注解，并检查注解中指定的接口类型
            // 获取原始目标类
            Class<?> targetClass = AopUtils.getTargetClass(implementation);
            FeignLocal annotation = targetClass.getAnnotation(FeignLocal.class);
            if (annotation != null && annotation.value().equals(feignClientInterface)) {
                return implementation;
            }
        }
        return null;
    }
}
