package sdata.ops.config;


import lombok.RequiredArgsConstructor;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.condition.PathPatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Arrays;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class DynamicControllerInvoker {

    private final RequestMappingHandlerMapping requestMappingHandlerMapping;

    private final ApplicationContext applicationContext;

    public Object invokeControllerMethod(String requestPath, Object... args) throws Exception {
        HandlerMethod handlerMethod = getHandlerMethod(requestPath);
        if (handlerMethod != null) {
            Object controller = applicationContext.getBean(handlerMethod.getBeanType());
            return handlerMethod.getMethod().invoke(controller, args);
        } else {
            throw new IllegalArgumentException("Handler method not found for request path: " + requestPath);
        }
    }

    private HandlerMethod getHandlerMethod(String requestPath) {
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            RequestMappingInfo mappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            System.out.println(Arrays.toString(handlerMethod.getMethod().getParameters()));
            if (requestPath.equals(mappingInfo.getActivePatternsCondition().toString().replace("[", "").replace("]", ""))) {
                return handlerMethod;
            }
        }
        return null;
    }

}
