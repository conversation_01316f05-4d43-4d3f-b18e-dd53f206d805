package sdata.ops;


import cn.hutool.extra.spring.EnableSpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import sdata.ops.indicator.service.impl.WarnAlertDingTalkServiceImpl;
import sdata.ops.indicator.service.impl.WarnAlertMailServiceImpl;
import sdata.ops.indicator.service.impl.WarnAlertSmsServiceImpl;
import sdata.ops.indicator.service.impl.WarnAlertWorkWechatServiceImpl;

import java.security.Security;

@SpringBootApplication
@EnableAsync
@EnableSpringUtil
@Import(value = {
        WarnAlertSmsServiceImpl.class,
        WarnAlertMailServiceImpl.class,
        WarnAlertWorkWechatServiceImpl.class,
        WarnAlertDingTalkServiceImpl.class,
})
public class StandApplication {

    public static void main(String[] args) {
        Security.setProperty("jdk.tls.disabledAlgorithms", "");
        System.setProperty("com.sun.jndi.ldap.object.disableEndpointIdentification", "true");
        SpringApplication.run(StandApplication.class, args);
    }

}
