
2025-01-16 15:36:29.929 [main] [o.s.b.t.context.SpringBootTestContextBootstrapper] INFO : Neither @ContextConfiguration nor @ContextHierarchy found for test class [sdata.ops.feign.FeignTest], using SpringBootContextLoader

2025-01-16 15:36:29.935 [main] [o.s.test.context.support.AbstractContextLoader] INFO : Could not detect default resource locations for test class [sdata.ops.feign.FeignTest]: no resource found for suffixes {-context.xml, Context.groovy}.

2025-01-16 15:36:29.936 [main] [o.s.t.c.support.AnnotationConfigContextLoaderUtils] INFO : Could not detect default configuration classes for test class [sdata.ops.feign.FeignTest]: FeignTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.

2025-01-16 15:36:30.022 [main] [o.s.b.t.context.SpringBootTestContextBootstrapper] INFO : Found @SpringBootConfiguration sdata.ops.StandApplication for test class sdata.ops.feign.FeignTest

2025-01-16 15:36:30.075 [main] [o.s.b.t.context.SpringBootTestContextBootstrapper] INFO : Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]

2025-01-16 15:36:30.086 [main] [o.s.b.t.context.SpringBootTestContextBootstrapper] INFO : Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@565b064f, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@26425897, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@73163d48, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@58c34bb3, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@56a4479a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62163b39, org.springframework.test.context.transaction.TransactionalTestExecutionListener@20a8a64e, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@62f4ff3b, org.springframework.test.context.event.EventPublishingTestExecutionListener@1698fc68, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@4504d271, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@207b8649, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@65b3a85a, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@34997338, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@57eda880, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2b5825fa]

2025-01-16 15:36:30.519 [main] [sdata.ops.feign.FeignTest] INFO : Starting FeignTest using Java 17.0.10 on th16 with PID 79124 (started by z in E:\project\sdata-ops-platform\sdata-ops-standalone)

2025-01-16 15:36:30.520 [main] [sdata.ops.feign.FeignTest] INFO : The following 1 profile is active: "local"

2025-01-16 15:36:31.474 [main] [o.s.d.r.config.RepositoryConfigurationDelegate] INFO : Multiple Spring Data modules found, entering strict repository configuration mode

2025-01-16 15:36:31.476 [main] [o.s.d.r.config.RepositoryConfigurationDelegate] INFO : Bootstrapping Spring Data Redis repositories in DEFAULT mode.

2025-01-16 15:36:31.502 [main] [o.s.d.r.config.RepositoryConfigurationDelegate] INFO : Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

2025-01-16 15:36:31.801 [main] [o.springframework.cloud.context.scope.GenericScope] INFO : BeanFactory id=26d137c7-6ed5-3c35-94c0-65ea97ff3488

2025-01-16 15:36:32.228 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$18d60ff7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.249 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.253 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.259 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.272 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.275 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.280 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$866/0x000002a4d44d57e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.283 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.295 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.355 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:32.370 [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] INFO : Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

2025-01-16 15:36:34.424 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::$Proxy146

2025-01-16 15:36:34.431 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::FlowTestFeignLocalHandler

2025-01-16 15:36:34.431 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : FlowTestFeignLocalHandler beanΪ����ʵ����ֱ��return

2025-01-16 15:36:34.785 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::IndicatorInfoFeignHandler

2025-01-16 15:36:34.785 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : IndicatorInfoFeignHandler beanΪ����ʵ����ֱ��return

2025-01-16 15:36:35.393 [main] [org.quartz.impl.StdSchedulerFactory] INFO : Using default implementation for ThreadExecutor

2025-01-16 15:36:35.403 [main] [org.quartz.core.SchedulerSignalerImpl] INFO : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl

2025-01-16 15:36:35.404 [main] [org.quartz.core.QuartzScheduler] INFO : Quartz Scheduler v.2.3.2 created.

2025-01-16 15:36:35.404 [main] [org.quartz.simpl.RAMJobStore] INFO : RAMJobStore initialized.

2025-01-16 15:36:35.405 [main] [org.quartz.core.QuartzScheduler] INFO : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'taskScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 5 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.


2025-01-16 15:36:35.405 [main] [org.quartz.impl.StdSchedulerFactory] INFO : Quartz scheduler 'taskScheduler' initialized from an externally provided properties instance.

2025-01-16 15:36:35.405 [main] [org.quartz.impl.StdSchedulerFactory] INFO : Quartz scheduler version: 2.3.2

2025-01-16 15:36:35.405 [main] [org.quartz.core.QuartzScheduler] INFO : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4cb87009

2025-01-16 15:36:35.873 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::$Proxy221

2025-01-16 15:36:36.209 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::$Proxy243

2025-01-16 15:36:36.289 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : ��鵽feignע��ӿ�ʵ����::$Proxy248

2025-01-16 15:36:36.292 [main] [sdata.ops.config.ReplacerFeignDefConfig] INFO : δ��鵽$Proxy248��ʵ����,Ĭ��return openfeign���ɵ�bean

2025-01-16 15:36:37.405 [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] INFO : Exposing 1 endpoint(s) beneath base path '/actuator'

2025-01-16 15:36:37.480 [main] [o.s.scheduling.quartz.SchedulerFactoryBean] INFO : Starting Quartz Scheduler now

2025-01-16 15:36:37.481 [main] [org.quartz.core.QuartzScheduler] INFO : Scheduler taskScheduler_$_NON_CLUSTERED started.

2025-01-16 15:36:37.517 [main] [sdata.ops.feign.FeignTest] INFO : Started FeignTest in 7.401 seconds (JVM running for 8.395)

2025-01-16 15:36:37.528 [main] [sdata.ops.system.job.DefaultJobHandler] INFO : scheduler-task-init === success ==size ==0

2025-01-16 15:36:37.912 [main] [com.zaxxer.hikari.HikariDataSource] INFO : DataSourceHikariCP - Starting...

2025-01-16 15:36:38.127 [main] [com.zaxxer.hikari.HikariDataSource] INFO : DataSourceHikariCP - Start completed.

2025-01-16 15:36:38.266 [main] [sdata.ops.system.job.TaskJob] INFO : gen-every:ÿ�������嵥���ɿ�ʼ

2025-01-16 15:36:38.280 [main] [sdata.ops.system.job.TaskJob] INFO : gen-every:ÿ�������嵥����::��������Ԫ��������0

2025-01-16 15:36:40.183 [main] [sdata.ops.system.job.TaskJob] INFO : gen-every:ÿ�������嵥����::��������ģ��󶨵�Ԫ��������3300

2025-01-16 15:36:40.185 [main] [sdata.ops.system.job.TaskJob] INFO : gen-every:ÿ�������嵥����:: �ճ�ֱ��չʾ�������� 2223

2025-01-16 15:36:40.185 [main] [sdata.ops.system.job.TaskJob] INFO : ��ѯ�Ƿ���δ����Ч��ת������

2025-01-16 15:36:42.994 [main] [sdata.ops.system.job.TaskJob] INFO : gen-every:ÿ�������嵥����::����

2025-01-16 15:36:43.103 [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] INFO : Scheduler taskScheduler_$_NON_CLUSTERED paused.

2025-01-16 15:36:43.109 [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] INFO : Shutting down Quartz Scheduler

2025-01-16 15:36:43.110 [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] INFO : Scheduler taskScheduler_$_NON_CLUSTERED shutting down.

2025-01-16 15:36:43.110 [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] INFO : Scheduler taskScheduler_$_NON_CLUSTERED paused.

2025-01-16 15:36:43.110 [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] INFO : Scheduler taskScheduler_$_NON_CLUSTERED shutdown complete.

2025-01-16 15:36:43.133 [SpringApplicationShutdownHook] [com.zaxxer.hikari.HikariDataSource] INFO : DataSourceHikariCP - Shutdown initiated...

2025-01-16 15:36:43.136 [SpringApplicationShutdownHook] [com.zaxxer.hikari.HikariDataSource] INFO : DataSourceHikariCP - Shutdown completed.
