<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>sdata.ops.platform</groupId>
    <artifactId>ops</artifactId>
    <version>24.3.1</version>

    <name>sdata-ops-platform</name>
    <url>http://www.sightwise.com</url>
    <description>运营管理系统</description>
    <packaging>pom</packaging>

    <properties>
        <sdata.ops.version>24.3.1</sdata.ops.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <spring.boot.version>2.7.12</spring.boot.version>
        <spring.cloud.version>2021.0.6</spring.cloud.version>
        <alibaba.cloud.version>2021.0.5.0</alibaba.cloud.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.20</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.43</fastjson.version>
        <oshi.version>6.4.13</oshi.version>
        <commons.io.version>2.13.0</commons.io.version>
        <poi.version>5.4.1</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <hutool.version>5.8.25</hutool.version>
        <mysql.version>8.0.30</mysql.version>
        <druid.version>1.2.16</druid.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <lombok.version>1.18.28</lombok.version>
        <warm-flow>1.0.4</warm-flow>
        <ok.http>4.9.1</ok.http>
        <feign.http>11.6</feign.http>
        <graalvm.version>21.3.3.1</graalvm.version>
        <commons.pool.version>2.11.1</commons.pool.version>
        <arths.version>3.6.5</arths.version>
        <express.version>3.3.2</express.version>
        <agents-flex.version>1.3.1</agents-flex.version>
        <flowable.version>6.8.0</flowable.version>
        <jakarta-validation.version>2.0.2</jakarta-validation.version>
        <aviator.version>5.3.1</aviator.version>
        <easyexcel.version>4.0.3</easyexcel.version>

    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
    </dependencies>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta-validation.version}</version>
            </dependency>
            <!-- 谷歌的表达式引擎 -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <!-- flowable -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.7.12</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--springCloud依赖配置-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--springCloudAlibaba依赖配置-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- mybatisPlus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!--mysql驱动-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version> <!-- 这里是MySQL 8的版本号 -->
            </dependency>
            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--网络客户端-->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${ok.http}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign.http}</version>
            </dependency>
            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>


            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- Apache POI for Excel manipulation -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version> <!-- 当前最新版本 -->
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.18.0</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--commons包依赖-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons.pool.version}</version>
            </dependency>
            <!--arthas java编译包-->
            <dependency>
                <groupId>com.taobao.arthas</groupId>
                <artifactId>arthas-memorycompiler</artifactId>
                <version>${arths.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!--graalvm support-->
            <dependency>
                <groupId>org.graalvm.sdk</groupId>
                <artifactId>graal-sdk</artifactId>
                <version>${graalvm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js</artifactId>
                <version>${graalvm.version}</version>
            </dependency>
            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.alibaba/QLExpress -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>${express.version}</version>
            </dependency>

            <dependency>
                <groupId>com.agentsflex</groupId>
                <artifactId>agents-flex-bom</artifactId>
                <version>${agents-flex.version}</version>
            </dependency>


            <!--个体组件-->
            <dependency>
                <groupId>sdata.ops.platform</groupId>
                <artifactId>sdata-ops-flow</artifactId>
                <version>${sdata.ops.version}</version>
            </dependency>
            <dependency>
                <groupId>sdata.ops.platform</groupId>
                <artifactId>sdata-ops-system</artifactId>
                <version>${sdata.ops.version}</version>
            </dependency>
            <dependency>
                <groupId>sdata.ops.platform</groupId>
                <artifactId>sdata-ops-indicator</artifactId>
                <version>${sdata.ops.version}</version>
            </dependency>
            <!--项目通用配置，token，orm框架-->
            <dependency>
                <groupId>sdata.ops.platform</groupId>
                <artifactId>sdata-ops-common</artifactId>
                <version>${sdata.ops.version}</version>
            </dependency>
            <!---单体部署项目-->
            <dependency>
                <groupId>sdata.ops.platform</groupId>
                <artifactId>sdat-ops-standalone</artifactId>
                <version>${sdata.ops.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>sdata-ops-common</module>
        <module>sdata-ops-app</module>
        <module>sdata-ops-base</module>
        <module>sdata-ops-standalone</module>
        <module>sdata-ops-micro</module>
        <module>sdata-ops-api</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>