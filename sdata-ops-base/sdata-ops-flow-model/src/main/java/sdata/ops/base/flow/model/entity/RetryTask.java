package sdata.ops.base.flow.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import sdata.ops.common.api.CommonConstant;

/**
 * 失败自动任务
 *
 * <AUTHOR>
 * @since 2025-09-29
 */
@Data
@TableName(schema = CommonConstant.DB_NAME, value = "ops_wf_retry_task")
public class RetryTask {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 流程实例id
     */
    private String processInstanceId;
    /**
     * 任务流id
     */
    private String taskFlowId;
    /**
     * 执行id
     */
    private String executionId;
}
