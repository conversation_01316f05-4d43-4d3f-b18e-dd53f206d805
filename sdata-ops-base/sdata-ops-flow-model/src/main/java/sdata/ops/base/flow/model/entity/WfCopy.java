package sdata.ops.base.flow.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import sdata.ops.common.api.CommonConstant;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程抄送对象 wf_copy
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@TableName(schema = CommonConstant.DB_NAME, value = "ops_wf_copy")
public class WfCopy implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 抄送主键
     */
    @TableId(value = "copy_id")
    private String copyId;
    /**
     * 抄送标题
     */
    private String title;
    /**
     * 流程主键
     */
    private String processId;
    /**
     * 流程名称
     */
    private String processName;
    /**
     * 流程分类主键
     */
    private String categoryId;
    /**
     * 部署主键
     */
    private String deploymentId;
    /**
     * 流程实例主键
     */
    private String instanceId;
    /**
     * 任务主键
     */
    private String taskId;
    /**
     * 用户主键
     */
    private String userId;
    /**
     * 发起人Id
     */
    private String originatorId;
    /**
     * 发起人名称
     */
    private String originatorName;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic(value = "0", delval = "2")
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
