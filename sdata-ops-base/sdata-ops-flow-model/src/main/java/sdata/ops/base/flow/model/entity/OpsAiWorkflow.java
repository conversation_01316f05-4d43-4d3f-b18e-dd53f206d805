package sdata.ops.base.flow.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * AI工作流表
 *
 * @TableName ops_ai_workflow
 */
@TableName(value = "ops_ai_workflow")
@Data
public class OpsAiWorkflow implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String icon;

    /**
     * 分类（枚举值：数据处理|接口输出|数据同步|内部调用）
     */
    private String category;

    /**
     * 类型：0-FLOW，1-API
     */
    private Integer type;

    /**
     * 工作流设计内容（JSON格式）
     */
    private String content;

    /**
     * 版本号（不做复杂版本控制，每次编辑+1即可）
     */
    private Integer version;

    /**
     * 发布状态：0-未发布，1-已发布
     */
    private Integer published;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}