package sdata.ops.base.flow.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FormConf {

    /**
     * 表单名称
     */
    private String formName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 标题
     */
    private String title;
    /**
     * 表单尺寸
     */
    private String size;
    private String labelAlign;
    private int labelColSpan;
    private int wrapperColSpan;
    /**
     * 禁用表单
     */
    private boolean disabled = false;
    /**
     * 表单项
     */
    private List<Map<String, Object>> fields;
}
