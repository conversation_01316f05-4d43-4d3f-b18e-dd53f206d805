package sdata.ops.base.flow.model.vo;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class OpsAiWorkflowVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String icon;

    /**
     * 分类（枚举值：数据处理|接口输出|数据同步|内部调用）
     */
    private String category;

    /**
     * 类型：0-FLOW，1-API
     */
    private Integer type;

    /**
     * 工作流设计内容（JSON格式）
     */
    private JSONObject content=new JSONObject();

    /**
     * 版本号（不做复杂版本控制，每次编辑+1即可）
     */
    private Integer version;

    /**
     * 发布状态：0-未发布，1-已发布
     */
    private Integer published;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    public OpsAiWorkflow convertToEntity() {
        OpsAiWorkflow entity = new OpsAiWorkflow();
        entity.setId(this.id!=null?Long.valueOf(this.id):null);
        entity.setTitle(this.title);
        entity.setDescription(this.description);
        entity.setIcon(this.icon);
        entity.setCategory(this.category);
        entity.setType(this.type);
        entity.setContent(JSONUtil.toJsonStr(this.content));
        entity.setVersion(this.version);
        entity.setPublished(this.published);
        entity.setDeleted(this.deleted);
        return entity;
    }

    public OpsAiWorkflowVO convertFromEntity(OpsAiWorkflow entity, Map<String,String> nameMap) {
        OpsAiWorkflowVO vo = new OpsAiWorkflowVO();
        vo.setId(String.valueOf(entity.getId()));
        vo.setTitle(entity.getTitle());
        vo.setDescription(entity.getDescription());
        vo.setIcon(entity.getIcon());
        vo.setCategory(entity.getCategory());
        vo.setType(entity.getType());
        //vo.setContent(JSONUtil.parseObj(entity.getContent()));
        vo.setVersion(entity.getVersion());
        vo.setPublished(entity.getPublished());
        vo.setDeleted(entity.getDeleted());
        vo.setCreateBy(nameMap.get(entity.getCreateBy()));
        vo.setCreateTime(entity.getCreateTime())
                .setUpdateTime(entity.getUpdateTime())
                .setUpdateBy(nameMap.get(entity.getUpdateBy()));
        return vo;
    }

    public OpsAiWorkflowVO convertSingle(OpsAiWorkflow entity) {
        OpsAiWorkflowVO vo = new OpsAiWorkflowVO();
        vo.setId(String.valueOf(entity.getId()));
        vo.setTitle(entity.getTitle());
        vo.setDescription(entity.getDescription());
        vo.setIcon(entity.getIcon());
        vo.setCategory(entity.getCategory());
        vo.setType(entity.getType());
        vo.setContent(JSONUtil.parseObj(entity.getContent()));
        vo.setVersion(entity.getVersion());
        vo.setPublished(entity.getPublished());
        vo.setDeleted(entity.getDeleted());
        vo.setCreateBy(entity.getCreateBy());
        vo.setCreateTime(entity.getCreateTime())
                .setUpdateTime(entity.getUpdateTime())
                .setUpdateBy(entity.getUpdateBy());
        return vo;
    }

}
