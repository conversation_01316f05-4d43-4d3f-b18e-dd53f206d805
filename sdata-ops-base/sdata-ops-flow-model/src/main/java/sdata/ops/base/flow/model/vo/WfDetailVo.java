package sdata.ops.base.flow.model.vo;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.util.List;

/**
 * 流程详情视图对象
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
public class WfDetailVo {

    /**
     * 任务表单信息
     */
    private FormConf taskFormData;

    /**
     * 历史流程节点信息
     */
    private List<WfProcNodeVo> historyProcNodeList;

    /**
     * 流程表单列表
     */
    private List<FormConf> processFormList;

    /**
     * 历史表单列表（撤回前的表单数据）
     */
    private List<FormConf> historyFormList;

    /**
     * 流程XML
     */
    private String bpmnXml;

    private WfViewerVo flowViewer;

    private String taskName;

    /**
     * 是否存在任务表单信息
     * @return true:存在；false:不存在
     */
    public Boolean isExistTaskForm() {
        return ObjectUtil.isNotEmpty(this.taskFormData);
    }
}
