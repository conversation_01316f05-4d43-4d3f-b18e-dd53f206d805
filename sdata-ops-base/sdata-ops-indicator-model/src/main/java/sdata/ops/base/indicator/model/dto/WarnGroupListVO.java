package sdata.ops.base.indicator.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WarnGroupListVO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 父id,用于关联id,做父子级联
     */
    private String pid;

    /**
     * 分类名
     */
    private String groupName;

    /**
     * 排序
     */
    private Integer orderSort;

    /**
     * 监控单元的数量
     */
    private Integer count;
    /**
     * 子分组
     */
    private List<WarnGroupListVO> children = new ArrayList<>(0);
}
