package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警信息表实体类
 * 对应数据库表 ops_warn_info
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_info")
public class OpsWarnInfo extends BaseEntity {
    /**
     * 分组id
     */
    private String warnGroupId;

    /**
     * 预警名称
     */
    private String warnName;

    /**
     * 预警描述
     */
    private String warnDesc;

    /**
     * 是否启用
     */
    private Integer warnStatus;

    /**
     * 告警级别
     */
    private String warnLevel;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
