package sdata.ops.base.indicator.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class MessageDTO implements Serializable {

    private String subject;

    private List<String> from=new ArrayList<>();

    private List<String>  replayTo=new ArrayList<>();

    private List<String>  allRecipients=new ArrayList<>();

    private List<String>  attFileNames;

    private Object content;

    private String part;

    private String uid;

}
