package sdata.ops.base.indicator.model.dto;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public  class DDLResult {
    private List<String> createTableSQL;
    private final List<String> alterSQLs = new ArrayList<>();

    private List<JSONObject> pevContent;

    private List<JSONObject> newContent;
    
    public void addAlterSQL(String sql) {
        this.alterSQLs.add(sql);
    }
    
    public boolean hasChanges() {
        return createTableSQL != null || !alterSQLs.isEmpty();
    }

}