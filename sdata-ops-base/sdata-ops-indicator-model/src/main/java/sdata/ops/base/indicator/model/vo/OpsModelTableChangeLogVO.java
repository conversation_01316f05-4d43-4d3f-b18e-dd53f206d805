package sdata.ops.base.indicator.model.vo;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.indicator.model.entity.OpsModelTableChangeLog;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class OpsModelTableChangeLogVO {

    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 日志类型(FIELD,INDEX)
     */
    private String logType;

    /**
     * 变更类型(CREATE,ALTER,DROP)
     */
    private String changeType;

    /**
     * 变更内容(JSON格式)
     */
    private List<JSONObject> changeContent;

    /**
     * 变更前内容(JSON格式)
     */
    private List<JSONObject> oldContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    private String tableName;

    public OpsModelTableChangeLogVO convert(OpsModelTableChangeLog opsModelTableChangeLog){
        return this.setId(opsModelTableChangeLog.getId())
                .setTableId(opsModelTableChangeLog.getTableId())
                .setLogType(opsModelTableChangeLog.getLogType())
                .setChangeType(opsModelTableChangeLog.getChangeType())
                .setChangeContent(opsModelTableChangeLog.getChangeContent()!=null?JSONUtil.toList(opsModelTableChangeLog.getChangeContent(),JSONObject.class):new ArrayList<>())
                .setOldContent(opsModelTableChangeLog.getOldContent()==null?new ArrayList<>():JSONUtil.toList(opsModelTableChangeLog.getOldContent(),JSONObject.class))
                .setOperator(opsModelTableChangeLog.getOperator())
                .setOperateTime(opsModelTableChangeLog.getOperateTime())
                .setTableName(opsModelTableChangeLog.getTableName());
    }
}
