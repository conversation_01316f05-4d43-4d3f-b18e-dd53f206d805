package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 指标基本信息表
 * @TableName ops_metric_basic
 */
@TableName(value ="ops_metric_basic")
@Data
public class OpsMetricBasic {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 指标编码(唯一标识)
     */
    private String metricCode;

    /**
     * 指标备注
     */
    private String remark;

    /**
     * 绑定的任务流ID
     */
    private String taskFlowId;

    /**
     * jsonpath配置(JSON格式)
     */
    private String jsonpathConfig;

    /**
     * 周期标签配置
     */
    private String periodConfig;

    /**
     * 溯源URL配置
     */
    private String traceUrl;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除(0-未删除 1-已删除)
     */
    @TableLogic
    private Integer deleted;

    /**
     * 
     */
    private String groupId;

    private Integer orderSort;

    private String testParams;


}