package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 预警通知表实体类
 * 对应数据库表 ops_warn_alert
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_alert")
public class OpsWarnAlert extends BaseEntity {
    /**
     * 监控单元id
     */
    private String warnId;

    /**
     * 告警内容，内容格式是json格式
     */
    private String warnContent;

    /**
     * 告整状态, 10首次告警,20持续告警,30告警消失
     */
    private Integer warnStatus;

    /**
     * 告警开始时间
     */
    private Date warnStartTime;

    /**
     * 预警结束时间
     */
    private Date warnEndTime;

    /**
     * 预警渠道
     */
    private String warnChannel;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
