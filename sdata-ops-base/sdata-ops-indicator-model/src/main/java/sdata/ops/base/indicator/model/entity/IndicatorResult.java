package sdata.ops.base.indicator.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName ops_indicator_result
 */
@Getter
@TableName(value ="ops_indicator_result")
@Data
public class IndicatorResult implements Serializable {
    /**
     * 主键id
     * -- GETTER --
     *  主键id

     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 批次id
     * -- GETTER --
     *  批次id

     */
    private String batchId;

    /**
     * 指标id
     * -- GETTER --
     *  指标id

     */
    private String indicatorId;

    /**
     * 业务日期YYYYMMDD
     * -- GETTER --
     *  业务日期YYYYMMDD

     */
    private String shortTime;

    /**
     * 数据插入时间
     * -- GETTER --
     *  数据插入时间

     */
    private Date createTime;

    /**
     *
     * -- GETTER --
     *

     */
    private String result;


    private static final long serialVersionUID = 1L;


}