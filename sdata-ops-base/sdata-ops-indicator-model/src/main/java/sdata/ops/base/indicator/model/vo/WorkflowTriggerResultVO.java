package sdata.ops.base.indicator.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 工作流触发器执行结果VO
 * 
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
public class WorkflowTriggerResultVO implements Serializable {

    /**
     * 触发结果，true表示应该创建任务，false表示不创建任务
     */
    private Boolean triggerResult = false;

    /**
     * 工作流返回的任务信息（可选）
     * 包含工作流传递的任务相关信息，如taskName、taskDesc等
     * 用于任务创建时覆盖默认配置
     */
    private Map<String, Object> taskInfo;

    /**
     * 工作流执行开始时间
     */
    private String startTime;

    /**
     * 工作流执行结束时间
     */
    private String endTime;

    /**
     * 执行状态信息
     */
    private String message;
}
