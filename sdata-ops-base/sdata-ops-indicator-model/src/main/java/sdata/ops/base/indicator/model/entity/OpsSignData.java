package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务是否需要导入配置标记表
 * @TableName OPS_SIGN_DATA
 */
@TableName(value ="ops_sign_data")
@Data
public class OpsSignData implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务单元id
     */
    private String taskId;

    /**
     * 按钮类型 1 导入 2 .....
     */
    private String signType;

    /**
     * 按钮显示文字
     */
    private String signVal;

    /**
     * 脚本id
     */
    private String indicatorId;
    /**
     * 
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}