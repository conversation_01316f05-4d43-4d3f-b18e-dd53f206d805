package sdata.ops.base.indicator.model.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.EMailFilter;
import sdata.ops.base.indicator.model.entity.EMailFilterAction;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class EMailFilterDTO extends EMailFilter {

    private List<EMailFilterRuleDTO> rules; // 过滤器规则

    private List<EMailFilterAction> actions; // 过滤器动作

}
