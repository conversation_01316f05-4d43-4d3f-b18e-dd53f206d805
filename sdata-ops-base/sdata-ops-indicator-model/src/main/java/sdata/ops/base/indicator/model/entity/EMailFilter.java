package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邮件筐过滤规则
 * <AUTHOR>
 * @since 2023/10/2 11:09
 */
@EqualsAndHashCode(callSuper = false)
@TableName("email_filter")
@Data
public class EMailFilter extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    private String filterId;

    /**
     * 过滤器名称
     */
    private String filterName;

    /**
     * 过滤器类型（收取时、发送时）
     */
    // @see cn.sdata.modules.taskcenter.enums,EmailFilterType
    private String filterType;

    /**
     * 过滤器条件（AND/OR）
     */
    // @see cn.sdata.modules.taskcenter.enums,EmailFilterCondition
    private String filterCondition;

    /**
     * 过滤器规则JSON
     */
    private String filterRule;

    /**
     * 启用禁用（1 启用 0 禁用）
     */
    private Boolean enabled;

    //任务模板ID
    private String taskTemplateId;
}
