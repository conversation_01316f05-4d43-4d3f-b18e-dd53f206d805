package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 当前预警数据表实体类
 * 对应数据库表 ops_warn_data_today
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_data_today")
public class OpsWarnDataToday extends BaseEntity {
    /**
     * 监控信息id
     */
    private String warnId;

    /**
     * 告警日期
     */
    private String warnDate;

    /**
     * 告警数据
     */
    private String warnContent;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
