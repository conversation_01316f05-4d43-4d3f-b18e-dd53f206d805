package sdata.ops.base.indicator.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricTag;
import sdata.ops.common.core.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class OpsMetricBasicFieldVO  {

    private String id;

    private String metricName;

    private List<OpsMetricResultConfig>  fields=new ArrayList<>();

}
