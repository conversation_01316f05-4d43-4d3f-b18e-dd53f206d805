package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName OPS_THIRD_AUTH_INFO
 */
@TableName(value ="ops_third_auth_info")
@Data
public class OpsThirdAuthInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标识
     */
    private String code;

    /**
     * 环境
     */
    private String env;

    /**
     * 内容json字符串
     */
    private String content;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}