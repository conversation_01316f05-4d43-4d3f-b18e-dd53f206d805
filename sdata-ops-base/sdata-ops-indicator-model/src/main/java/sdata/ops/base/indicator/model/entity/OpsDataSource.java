package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ops_data_source")
public class OpsDataSource extends BaseEntity implements Serializable {


    /**
     * 数据源名称
     */
    private String sourceName;

    /**
     * 数据源类型
     */
    private String sourceType;

    /**
     * 数据源配置
     */
    private String sourceConfig;

    /**
     * 数据源相关参数配置
     */
    private String variableConfig;

    /**
     * 数据源分组id 1对1
     */
    private String groupId;

    /**
     * 类型数据源
     */
    private String subSourceType;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    private Integer deleted;


    private Integer orderSort;
}
