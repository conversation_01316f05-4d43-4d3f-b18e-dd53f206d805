package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.common.annotation.SaveGroup;

import javax.validation.constraints.NotBlank;

/**
 * 预警分类表实体类
 * 对应数据库表 ops_warn_group
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_group")
public class OpsWarnGroup extends BaseEntity {
    /**
     * 父id,用于关联id,做父子级联
     */
    private String pid;

    /**
     * 分类名
     */
    @NotBlank(groups = SaveGroup.class)
    private String groupName;

    /**
     * 排序
     */
    private Integer orderSort;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
