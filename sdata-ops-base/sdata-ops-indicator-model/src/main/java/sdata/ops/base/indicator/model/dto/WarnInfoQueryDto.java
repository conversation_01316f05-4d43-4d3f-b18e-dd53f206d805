package sdata.ops.base.indicator.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class WarnInfoQueryDto {
    private int pageNo = 1;
    private int pageSize = 10;
    /**
     * 监控单元名
     */
    private String warnName;

    /**
     * 分组id
     */
    private String warnGroupId;
    /**
     * 开始日期 yyyy-MM-dd
     */
    private String startDate;
    /**
     * 结束日期 yyyy-MM-dd
     */
    private String endDate;
    /**
     * 字典类型
     */
    private List<String> dictTypes;

}
