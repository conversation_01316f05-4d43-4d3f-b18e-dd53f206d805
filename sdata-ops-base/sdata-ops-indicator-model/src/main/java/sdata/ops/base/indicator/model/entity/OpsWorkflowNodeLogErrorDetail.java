package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 工作流异常详情表
 * @TableName ops_workflow_node_log_error_detail
 */
@TableName(value ="ops_workflow_node_log")
@Data
public class OpsWorkflowNodeLogErrorDetail implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String scheduleId;

    /**
     * 
     */
    private String flowId;

    /**
     * 
     */
    private String nodeId;

    /**
     * 
     */
    private String nodeName;

    /**
     * 
     */
    private String nodeType;

    /**
     * SUCCESS/FAILED/SKIPPED
     */
    private String status;

    /**
     * 
     */
    private Object inputParams;

    /**
     * 
     */
    private Object outputResult;

    /**
     * 
     */
    private String errorMessage;

    /**
     * 
     */
    private String errorStack;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}