package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据表字段配置表
 * @TableName ops_model_table_column
 */
@TableName(value ="ops_model_table_column")
@Data
@EqualsAndHashCode
public class OpsModelTableColumn implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 字段名
     */
    private String columnName;

    /**
     * 字段类型
     */
    private String columnType;

    /**
     * 字段长度
     */
    private Integer fieldLength;


    /**
     * 是否可空(0:否,1:是)
     */
    private Integer isNullable;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 是否主键(0:否,1:是)
     */
    private Integer isPrimary;

    /**
     * 是否自增(0:否,1:是)
     */
    private Integer isAutoIncrement;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}