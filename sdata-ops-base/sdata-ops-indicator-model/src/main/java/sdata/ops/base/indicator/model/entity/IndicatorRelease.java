package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ops_indicator_release")
public class IndicatorRelease extends BaseEntity implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 版本号
     */
    private String releaseNo;

    /**
     * 版本关联指标ID
     */
    private String indicatorId;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 分类
     */
    private String groupId;

    /**
     * 类型（SQL、JS、PYTHON、JAVA）
     */
    private String type;

    /**
     * 是否需要存储结果集(0否/1是）
     */
    private Integer isCached;

    /**
     * 结果集缓存期限（分钟）
     */
    private String cachedTerm;

    /**
     * 脚本数据
     */
    private String script;

    /**
     * 指标类型
     */
    private String indicatorType;

    /**
     * 指标描述
     */
    private String indicatorDesc;


}
