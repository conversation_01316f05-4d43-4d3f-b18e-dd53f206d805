package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 邮件筐过滤器执行动作
 * <AUTHOR>
 * @since 2023/10/2 11:09
 */
@EqualsAndHashCode(callSuper = false)
@TableName("email_filter_action")
@Data
public class EMailFilterAction implements Serializable {

    /**
     * 主键ID
     */
    @TableId
    private String actionId;

    /**
     * 过滤器ID
     */
    private String filterId;

    /**
     * 执行动作
     */
    private String action;

    /**
     * 邮件筐ID
     */
    private String boxId;
}
