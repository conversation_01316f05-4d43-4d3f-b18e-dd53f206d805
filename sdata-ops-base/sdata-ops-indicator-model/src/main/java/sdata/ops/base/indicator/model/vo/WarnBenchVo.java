package sdata.ops.base.indicator.model.vo;

import lombok.Data;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class WarnBenchVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 分组id
     */
    private String warnGroupId;

    /**
     * 预警名称
     */
    private String warnName;

    /**
     * 是否已读，0未读，1已读
     */
    private Boolean hasRead;
    /**
     * 字典id
     */
    private List<Long> dictIds = Collections.emptyList();
    /**
     * 字典类型
     */
    private Set<String> dictTypes = Collections.emptySet();
    /**
     * 最后同步时间
     */
    private Date warnSyncLastTime;

    public void belong(WarnInfoVO item) {
        this.id = item.getId();
        this.warnGroupId = item.getWarnGroupId();
        this.warnName = item.getWarnName();
        this.dictIds = item.getDictIds();
        if (item.getWarnSyncLastTimestamp() != null) {
            this.warnSyncLastTime = new Date(item.getWarnSyncLastTimestamp());
        } else {
            this.warnSyncLastTime = item.getCreateTime();
        }
    }
}
