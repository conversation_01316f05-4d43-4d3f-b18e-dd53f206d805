package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_read_log")
public class OpsWarnReadLog extends BaseEntity {
    /**
     * 监控单元id
     */
    private String warnId;
    /**
     * 读取用户id
     */
    private String readUserId;
    /**
     * 读取时间
     */
    private Date readAt;
    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
