package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 指标数据结果配置表
 * @TableName ops_metric_result_config
 */
@TableName(value ="ops_metric_result_config")
@Data
public class OpsMetricResultConfig {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 指标ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long metricId;

    /**
     * 结果字段名
     */
    private String resultField;

    /**
     * 字段类型 string|int 
     */
    private String fieldType;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 权限绑定id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String permissionId;

    /**
     * 字段描述
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}