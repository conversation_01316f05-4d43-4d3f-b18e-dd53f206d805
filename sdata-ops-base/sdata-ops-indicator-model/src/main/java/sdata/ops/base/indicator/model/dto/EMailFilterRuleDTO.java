package sdata.ops.base.indicator.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EMailFilterRuleDTO implements Serializable {
    private String target;          // 目标（receiver / sender / recipient / subject / content）
    private String type;            // 类型（include / exclude）
    private String condition;       // 条件（AND / OR）
    private List<String> markList;  // 规则内容
}
