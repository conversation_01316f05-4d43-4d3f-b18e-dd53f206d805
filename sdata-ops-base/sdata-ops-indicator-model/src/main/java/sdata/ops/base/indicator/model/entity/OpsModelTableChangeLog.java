package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 数据表变更记录表
 * @TableName ops_model_table_change_log
 */
@TableName(value ="ops_model_table_change_log")
@Data
public class OpsModelTableChangeLog implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 日志类型(FIELD,INDEX)
     */
    private String logType;

    /**
     * 变更类型(CREATE,ALTER,DROP)
     */
    private String changeType;

    /**
     * 变更内容(JSON格式)
     */
    private String changeContent;

    /**
     * 变更前内容(JSON格式)
     */
    private String oldContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    private String tableName;
}