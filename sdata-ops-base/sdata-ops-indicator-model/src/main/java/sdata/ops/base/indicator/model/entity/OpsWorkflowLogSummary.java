package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 工作流运行日志表
 *
 * @TableName ops_workflow_log_summary
 */
@TableName(value = "ops_workflow_log_summary")
@Data
public class OpsWorkflowLogSummary implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 调度记录id
     */
    private String scheduleId;

    /**
     *
     */
    private Long flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * SUCCESS/FAILED/TIMEOUT
     */
    private String status;

    /**
     *
     */
    private Date startTime;

    /**
     *
     */
    private Date endTime;

    /**
     * 总耗时(毫秒)
     */
    private Integer duration;

    /**
     * SCHEDULE/MANUAL/API
     */
    private String triggerType;

    /**
     * 简化版输入参数
     */
    private Object initParams;

    /**
     * 成功时的结果摘要
     */
    private String resultSummary;

    /**
     * 失败时的错误码
     */
    private String errorCode;

    /**
     * 失败时的错误摘要
     */
    private String errorMsg;

    /**
     * 是否有详细日志
     */
    private Integer hasDetail;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    private String executionId;
}