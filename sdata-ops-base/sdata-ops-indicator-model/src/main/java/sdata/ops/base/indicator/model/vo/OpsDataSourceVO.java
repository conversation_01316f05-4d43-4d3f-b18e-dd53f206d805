package sdata.ops.base.indicator.model.vo;


import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.BaseEntity;
import sdata.ops.base.indicator.model.entity.OpsDataSource;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsDataSourceVO extends BaseEntity {


    private String sourceName;

    private String sourceType;

    @JsonInclude()
    @JsonRawValue
    private JSONObject sourceConfig;

    @JsonInclude()
    @JsonRawValue
    private Map<String, Object> variableConfig;


    private String groupId;

    /**
     * 类型数据源
     */
    private String subSourceType;

    private Integer orderSort;


    public OpsDataSource convert(OpsDataSourceVO vo) {
        if (vo == null) {
            return null;
        }
        OpsDataSource dataSource = new OpsDataSource();
        dataSource.setSourceName(vo.getSourceName());
        dataSource.setSourceType(vo.getSourceType());
        dataSource.setSourceConfig(JSONUtil.toJsonStr(vo.getSourceConfig(),JSONConfig.create().setIgnoreNullValue(false)));
        dataSource.setVariableConfig(JSONUtil.toJsonStr(vo.getVariableConfig(), JSONConfig.create().setIgnoreNullValue(false)));
        dataSource.setGroupId(vo.getGroupId());
        dataSource.setSubSourceType(vo.getSubSourceType());
        dataSource.setOrderSort(vo.getOrderSort());
        dataSource.setId(vo.getId());
        return dataSource;
    }

    public OpsDataSourceVO convert(OpsDataSource dataSource) {
        if (dataSource == null) {
            return null;
        }
        OpsDataSourceVO vo = new OpsDataSourceVO();
        vo.setSourceName(dataSource.getSourceName());
        vo.setSourceType(dataSource.getSourceType());
        vo.setSourceConfig(JSONUtil.parseObj(dataSource.getSourceConfig()));
        vo.setVariableConfig(JSONUtil.parseObj(dataSource.getVariableConfig(), false));
        vo.setGroupId(dataSource.getGroupId());
        vo.setSubSourceType(dataSource.getSubSourceType());
        vo.setOrderSort(dataSource.getOrderSort());
        vo.setId(dataSource.getId());
        vo.setCreateBy(dataSource.getCreateBy());
        vo.setUpdateBy(dataSource.getUpdateBy());
        vo.setCreateTime(dataSource.getCreateTime());
        vo.setUpdateTime(dataSource.getUpdateTime());
        return vo;
    }

}
