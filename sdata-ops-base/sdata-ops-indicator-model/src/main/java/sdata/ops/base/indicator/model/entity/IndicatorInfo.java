package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ops_indicator_info")
public class IndicatorInfo implements Serializable {


    private static final long serialVersionUID = 1L;


    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 数据源ID
     */
    private String dataSourceId;


    /**
     * 当前版本
     */
    private String releaseId;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 脚本类型
     *
     * @see sdata.ops.indicator.enums.ExecuteType
     * SQL_READ("sql", SqlScriptHandler.class),
     * JS_SCRIPT("javascript", JavaScriptHandler.class),
     * JAVA_SCRIPT("java", JavaSourceHandler.class),
     * MAIL_FILTER("mail", MailMatchHandler.class),
     * EXPRESS_MATCH("express", ExpressHandler.class);
     */
    private String type;

    /*------------------------新增字段--------------*/
    /**
     * 数据源类型
     * api 查询数据源配置执行
     * mail 查询邮件返回体
     * script 查询脚本
     */
    private String dataSourceType;

    /**
     * 对本身脚本输出结果是否需要特殊配置
     */
    private String outputConfig;

    /**
     * 对本脚本输入结果是否需要特殊配置
     */
    private String inputConfig;


    /*------------------------新增字段--------------*/

    /**
     * 脚本配置序列化json内容
     * 包含 java js sql代码片段
     * 与 MailFilter邮件过滤规则结构化的内容 ，QLExpress 配置化内容
     * 五种类型
     */
    private String script;

    /**
     * 配置类型 类型（result,exec）
     */
    private String indicatorType;

    /**
     * 脚本描述
     */
    private String indicatorDesc;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    private Integer delFlag;

    /**
     * 排序
     */
    private Integer orderSort;


    /**
     * 是否缓存结果集(0否/1是）
     */
    private Integer isCached;

    /**
     * 结果集缓存期限（分钟）
     */
    private String cachedTerm;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
