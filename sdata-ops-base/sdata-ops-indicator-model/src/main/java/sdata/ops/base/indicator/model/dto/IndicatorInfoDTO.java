package sdata.ops.base.indicator.model.dto;

import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;

import java.util.HashMap;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorInfoDTO extends IndicatorInfo {
    /**
     * 请求参数
     */
    private JSONObject param;

    /**
     * localReference
     */
    private Map<String,Object> mailParams=new HashMap<>();

    /**
     * 是否保存日志
     */
    private Boolean save = false;

    /**
     * 产品编码
     */
    private String userId;

    /**
     * 当前currentURL
     */
    private String currentURL = "http://127.0.0.1:8086/manager";
}
