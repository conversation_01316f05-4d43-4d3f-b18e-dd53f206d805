package sdata.ops.base.indicator.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DataSourceConfigVO {
    private String dataSourceId;

    private String sourceType;

    private String subSourceType;

    private String url;

    private String port;

    private String username;

    private String password;

    private String serverName;

    private String driverClass;

    private String variableConfig;

    private String sourceConfig;

    private String host;

}
