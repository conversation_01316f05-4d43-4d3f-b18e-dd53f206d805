package sdata.ops.base.indicator.model.vo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Slf4j
@Data
public class WarnResultVo {
    /**
     * 监控单元id
     */
    private String id;

    /**
     * 分组id
     */
    private String warnGroupId;
    /**
     * 监控单元名称
     */
    private String warnName;
    /**
     * 监控单元状态
     * @see sdata.ops.indicator.enums.WarnRunStatusEnum#name()
     */
    private String warnStatus;
    /**
     * 告警数量
     */
    private Integer warnCount;
    /**
     * 字典id
     */
    private List<Long> dictIds = Collections.emptyList();
    /**
     * 字典类型
     */
    private Set<String> dictTypes = Collections.emptySet();

    public static WarnResultVo of(WarnInfoVO warn) {
        WarnResultVo vo = new WarnResultVo();
        vo.id = warn.getId();
        vo.warnName = warn.getWarnName();
        vo.warnGroupId = warn.getWarnGroupId();
        vo.warnCount = 0;
        vo.dictIds = warn.getDictIds();
        return vo;
    }
}
