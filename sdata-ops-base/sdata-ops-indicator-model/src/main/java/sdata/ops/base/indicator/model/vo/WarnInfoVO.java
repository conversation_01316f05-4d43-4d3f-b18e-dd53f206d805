package sdata.ops.base.indicator.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import sdata.ops.common.annotation.SaveGroup;
import sdata.ops.common.annotation.UpdateGroup;
import sdata.ops.base.indicator.model.entity.OpsWarnInfo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Data
public class WarnInfoVO {
    /**
     * 主键id
     */
    @NotEmpty(groups = UpdateGroup.class)
    private String id;

    /**
     * 分组id
     */
    private String warnGroupId;

    /**
     * 预警名称
     */
    private String warnName;

    /**
     * 预警描述
     */
    private String warnDesc;

    /**
     * 是否启用
     */
    private Integer warnStatus;

    /**
     * 告警级别
     */
    private String warnLevel;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 预警同步指标ID
     */
    private String warnSyncIndicatorId;
    /**
     * 预警同步开始时间
     */
    private String warnSyncStartTime;
    /**
     * 预警同步结束时间
     */
    private String warnSyncEndTime;
    /**
     * 预警同步类型
     * @see sdata.ops.indicator.enums.WarnSyncTypeEnum
     */
    @NotNull(groups = SaveGroup.class)
    private Integer warnSyncType;
    /**
     * 预警同步固定频率
     */
    private Integer warnSyncFixedRateMinutes;
    /**
     * 预警同步cron表达式
     */
    private String warnSyncCron;
    /**
     * 最后同步时间
     */
    @JsonIgnore
    private Long warnSyncLastTimestamp;
    /**
     * 异常状态, 1:取数为null  2:取数指标执行异常 3: 告警异常
     */
    @JsonIgnore
    private Integer warnExceptionStatus;
    /**
     * 告警规则,1:有数据就告警 2:根据表达式匹配规则
     * @see sdata.ops.indicator.enums.WarnAlertRuleTypeEnum
     */
    private Integer warnAlertRuleType;
    /**
     * 最后通知时间
     */
    @JsonIgnore
    private Long warnAlertLastTimestamp;
    /**
     * 告警表达式
     */
    private String warnAlertExpression;
    /**
     * 告警表达式 - key
     */
    private List<String> warnAlertDimKey;
    /**
     * 告警表达式 - value
     */
    private List<Object> warnAlertDimValue;

    /**
     * 告警渠道
     */
    private List<String> warnAlertChannels;
    /**
     * 字典id
     */
    private List<Long> dictIds = Collections.emptyList();
    /**
     * 取数任务id
     */
    @JsonIgnore
    private String syncJobId;

    /**
     * 告警任务id
     */
    @JsonIgnore
    private String alertJobId;
    /**
     * 日历类型，规定只按指定日历执行定时任务
     * @see  sdata.ops.base.system.model.entity.OpsSysCalendar#market
     */
    @NotEmpty(groups = SaveGroup.class)
    private String market;

    public OpsWarnInfo toSaveEntity() {
        OpsWarnInfo entity = new OpsWarnInfo();
        entity.setId(this.id);
        entity.setWarnGroupId(this.warnGroupId);
        entity.setWarnName(this.warnName);
        entity.setWarnDesc(this.warnDesc);
        entity.setWarnStatus(this.warnStatus);
        entity.setWarnLevel(this.warnLevel);
        return entity;
    }
}
