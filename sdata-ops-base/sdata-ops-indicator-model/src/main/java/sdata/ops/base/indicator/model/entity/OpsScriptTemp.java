package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 脚本执行结果暂存表
 * @TableName OPS_SCRIPT_TEMP
 */
@TableName(value ="ops_script_temp")
@Data
public class OpsScriptTemp implements Serializable {
    /**
     * 
     */
    @TableId(type =IdType.ASSIGN_ID)
    private Long id;

    /**
     * 指标id
     */
    private Long indicatorId;

    /**
     * 第三方id
     */
    private String thirdId;

    /**
     * 执行日期
     */
    private String dayVal;

    /**
     * 
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}