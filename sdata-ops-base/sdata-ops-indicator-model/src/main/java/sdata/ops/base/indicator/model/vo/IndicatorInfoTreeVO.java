package sdata.ops.base.indicator.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/22 18:39
 */
@Data
public class IndicatorInfoTreeVO {

    /**
     * 指标ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 脚本类型
     */
    private String type;

    /**
     * 排序
     */
    private Integer orderSort;

    /***
     * 指标类型
     */
    private String indicatorType;
}
