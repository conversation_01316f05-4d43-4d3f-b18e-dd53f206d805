package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @TableName ops_metric_group
 */
@TableName(value = "ops_metric_group")
@Data
public class OpsMetricGroup {
    /**
     * 分组id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 父级分组id
     */
    @JsonProperty("pid")
    private String pId;

    /**
     * 删除状态0未删除 1已删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 排序
     */
    private Integer orderSort;


    @TableField(exist = false)
    private Integer count;

    @TableField(exist = false)
    private List<OpsMetricGroup> children = new ArrayList<>();
}