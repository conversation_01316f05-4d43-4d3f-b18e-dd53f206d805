package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警信息配置表实体类
 * 对应数据库表 ops_warn_config
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(schema = sdata.ops.common.api.CommonConstant.DB_NAME, value = "ops_warn_config")
public class OpsWarnConfig extends BaseEntity {
    /**
     * 监控单元id
     */
    private String warnId;

    /**
     * 配置key
     */
    private String configKey;

    /**
     * 配置value
     */
    private String configValue;

    /**
     * 配置value的类型、用于解析配置value，例如number/array/object
     */
    private String configValueType;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;
}
