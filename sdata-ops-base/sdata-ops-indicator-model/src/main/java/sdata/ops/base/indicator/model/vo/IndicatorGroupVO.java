package sdata.ops.base.indicator.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 18:27
 */
@Data
public class IndicatorGroupVO {

    /**
     * 分组ID
     */
    private String id;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 父级分组ID
     */
    private String parentId;

    /**
     * 排序
     */
    private Integer orderSort;

    /**
     * 指标列表
     */
    private List<IndicatorInfoTreeVO> infoList;
}
