package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ops_indicator_history")
public class IndicatorHistory  implements Serializable {


    /**
     * 数据标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;



    private String indicatorId;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 版本ID
     */
    private String releaseId;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 类型
     */
    private String type;

    /**
     * 脚本
     */
    private String script;

    /**
     * 执行脚本
     */
    private String execScript;

    /**
     * 参数信息
     */
    private String paramInfo;

    /**
     * 执行时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;

    /**
     * 异常信息
     */
    private String exceptionInfo;

    /**
     * 来源
     */
    private String fromSystem;

    /**
     * 执行人
     */
    private String executeUser;
}
