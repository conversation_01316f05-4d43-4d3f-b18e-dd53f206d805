package sdata.ops.base.indicator.model.vo;


import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.BaseEntity;
import sdata.ops.base.indicator.model.entity.OpsDataSource;
import sdata.ops.common.core.util.JsonUtils;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsDataSourceFrontVO extends BaseEntity {


    private String sourceName;

    private String sourceType;

    @JsonRawValue
    private JsonNode sourceConfig;

    @JsonRawValue
    private JsonNode variableConfig;


    private String groupId;

    /**
     * 类型数据源
     */
    private String subSourceType;

    private Integer orderSort;


    public OpsDataSource convert(OpsDataSourceFrontVO vo) {
        if (vo == null) {
            return null;
        }
        OpsDataSource dataSource = new OpsDataSource();
        dataSource.setSourceName(vo.getSourceName());
        dataSource.setSourceType(vo.getSourceType());
        dataSource.setSourceConfig(vo.getSourceConfig().toPrettyString());
        dataSource.setVariableConfig(vo.getVariableConfig().toPrettyString());
        dataSource.setGroupId(vo.getGroupId());
        dataSource.setSubSourceType(vo.getSubSourceType());
        dataSource.setOrderSort(vo.getOrderSort());
        dataSource.setId(vo.getId());
        return dataSource;
    }

    public OpsDataSourceFrontVO convert(OpsDataSource dataSource, Map<String,String> nameIdMapping) {
        if (dataSource == null) {
            return null;
        }
        OpsDataSourceFrontVO vo = new OpsDataSourceFrontVO();
        vo.setSourceName(dataSource.getSourceName());
        vo.setSourceType(dataSource.getSourceType());
        vo.setSourceConfig(JsonUtils.toJsonObj(dataSource.getSourceConfig()));
        vo.setVariableConfig(JsonUtils.toJsonObj(dataSource.getVariableConfig()));
        vo.setGroupId(dataSource.getGroupId());
        vo.setSubSourceType(dataSource.getSubSourceType());
        vo.setOrderSort(dataSource.getOrderSort());
        vo.setId(dataSource.getId());
        vo.setCreateBy(nameIdMapping.get(dataSource.getCreateBy())==null?"":nameIdMapping.get(dataSource.getCreateBy()));
        vo.setUpdateBy(nameIdMapping.get(dataSource.getUpdateBy())==null?"":nameIdMapping.get(dataSource.getUpdateBy()));
        vo.setCreateTime(dataSource.getCreateTime());
        vo.setUpdateTime(dataSource.getUpdateTime());
        return vo;
    }


}
