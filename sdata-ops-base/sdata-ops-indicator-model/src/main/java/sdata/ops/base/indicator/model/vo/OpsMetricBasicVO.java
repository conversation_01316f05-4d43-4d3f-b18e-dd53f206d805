package sdata.ops.base.indicator.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricTag;
import sdata.ops.common.core.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsMetricBasicVO extends OpsMetricBasic {

    private List<String> tags=new ArrayList<>();


    private Map<String,Object>  params;

    public static OpsMetricBasicVO fromEntity(OpsMetricBasic metricBasic, Map<String,List<OpsMetricTag>> tag,Map<String, String> nameMap) {
        OpsMetricBasicVO metricBasicVO = new OpsMetricBasicVO();
        metricBasicVO.setId(metricBasic.getId());
        metricBasicVO.setMetricName(metricBasic.getMetricName());
        metricBasicVO.setMetricCode(metricBasic.getMetricCode());
        metricBasicVO.setRemark(metricBasic.getRemark());
        metricBasicVO.setTaskFlowId(metricBasic.getTaskFlowId());
        metricBasicVO.setJsonpathConfig(metricBasic.getJsonpathConfig());
        metricBasicVO.setPeriodConfig(metricBasic.getPeriodConfig());
        metricBasicVO.setTraceUrl(metricBasic.getTraceUrl());
        metricBasicVO.setCreateBy(nameMap.get(metricBasic.getCreateBy()));
        metricBasicVO.setCreateTime(metricBasic.getCreateTime());
        metricBasicVO.setUpdateBy(nameMap.get(metricBasic.getUpdateBy()));
        metricBasicVO.setUpdateTime(metricBasic.getUpdateTime());
        metricBasicVO.setDeleted(metricBasic.getDeleted());
        metricBasicVO.setGroupId(metricBasic.getGroupId());
        metricBasicVO.setOrderSort(metricBasic.getOrderSort());
        if(tag != null&& tag.containsKey(metricBasic.getId().toString())) {
            metricBasicVO.setTags(tag.get(metricBasic.getId().toString()).stream().map(OpsMetricTag::getTagName).collect(Collectors.toList()));
        }
        if(metricBasic.getTestParams() != null){
            metricBasicVO.setParams(JsonUtils.toMap(metricBasic.getTestParams()));
        }
        return metricBasicVO;
    }
}
