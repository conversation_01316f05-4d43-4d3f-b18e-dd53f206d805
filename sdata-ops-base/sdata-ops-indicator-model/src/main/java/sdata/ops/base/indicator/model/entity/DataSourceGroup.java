package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@TableName("ops_data_source_group")
public class DataSourceGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 父级分组ID
     */
    @JsonProperty("pid")
    private String pId;

    /**
     * 排序
     */
    private Integer orderSort;

    /**
     * 删除标识 0、未删除  1、已删除
     */
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private int count;

    @TableField(exist = false)
    private List<DataSourceGroup> children=new ArrayList<>();
}
