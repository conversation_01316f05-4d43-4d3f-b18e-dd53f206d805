package sdata.ops.base.indicator.model.vo;

import java.util.Date;
import java.util.Map;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
public class OpsMetricApiConfigVO {
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 指标ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long metricId;

    /**
     * API路径
     */
    private String apiPath;

    /**
     * HTTP方法(GET/POST)
     */
    private String httpMethod;

    /**
     * API描述
     */
    private String remark;

    /**
     * 参数配置
     */
    private Map<String, Object> params;

    /**
     * 是否启用(1-启用 0-禁用)
     */
    private Integer isEnabled;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除(0-未删除 1-已删除)
     */
    private Integer deleted;
}