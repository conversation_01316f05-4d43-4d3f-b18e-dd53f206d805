package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 定时任务调度表
 * @TableName ops_quartz_job
 */
@TableName(value ="ops_quartz_job")
@Data
public class OpsQuartzJob implements Serializable {
    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务组名
     */
    private String jobGroup;

    /**
     * 调用目标字符串
     */
    private String invokeTarget;

    /**
     * cron执行表达式
     */
    private String cronExpression;

    /**
     * 计划执行错误策略（1立即执行 2执行一次 3放弃执行）
     */
    private String misfirePolicy;

    /**
     * 是否并发执行（0允许 1禁止）
     */
    private String concurrent;

    /**
     * 状态（0正常 1暂停）
     */
    private String status;

    /**
     * 工作流id绑定
     */
    private String bindWfId;

    /**
     * 任务类型 flow_task , dynamic_task ,warn_task
     */
    private String jobType;

    /**
     * 监控中心-简单单元id
     */
    private String warnId;

    /**
     * 日历类型，规定只按指定日历执行定时任务
     *
     * @see sdata.ops.base.system.model.entity.OpsSysCalendar#market
     */
    private String market;


    /**
     * 快捷模式 quick
     * 自定义  custom
     */
    private String timeType;

    /**
     * 频率 5分钟 10分钟 30分钟 1小时
     */
    private String frequency;

    /**
     *  开始时间  字符串 HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 字符 HH:mm:ss
     */
    private String endTime;


    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 备注信息
     */
    private String remark;
}