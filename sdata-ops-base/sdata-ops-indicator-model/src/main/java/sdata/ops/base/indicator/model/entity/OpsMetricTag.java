package sdata.ops.base.indicator.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import lombok.Data;

/**
 * 标签表
 * @TableName ops_metric_tag
 */
@TableName(value ="ops_metric_tag")
@Data
public class OpsMetricTag {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String metricId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}