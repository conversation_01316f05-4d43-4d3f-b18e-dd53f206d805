package sdata.ops.base.system.model.enums;

import lombok.Getter;

/**
 * 触发器调度频率枚举
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Getter
public enum TriggerScheduleFrequency {

    /**
     * 30分钟
     */
    THIRTY_MINUTES("30m", "30分钟", 30),

    /**
     * 1小时
     */
    ONE_HOUR("1h", "1小时", 60),

    /**
     * 2小时
     */
    TWO_HOURS("2h", "2小时", 120);


    /**
     * 频率代码
     */
    private final String code;

    /**
     * 频率描述
     */
    private final String description;

    /**
     * 间隔分钟数
     */
    private final int intervalMinutes;

    TriggerScheduleFrequency(String code, String description, int intervalMinutes) {
        this.code = code;
        this.description = description;
        this.intervalMinutes = intervalMinutes;
    }
}
