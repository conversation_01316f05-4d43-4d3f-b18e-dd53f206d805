package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName ops_view_preferences
 */
@Data
@TableName(value ="ops_view_preferences")
public class OpsViewPreferences implements Serializable {


    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 视图id
     */
    private String viewId;

    /**
     * 显隐
     */
    private Integer isVisible;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

   
}