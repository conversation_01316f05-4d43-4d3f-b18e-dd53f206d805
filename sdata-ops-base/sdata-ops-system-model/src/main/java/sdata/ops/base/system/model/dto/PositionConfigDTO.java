package sdata.ops.base.system.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class PositionConfigDTO {

    //操作模板id
    private String templateId;

    //岗位id
    private String orgId;

    //岗位名称
    private String orgName;
    //对于跨岗替换，人员权限是临时新增还是永久转岗 1 默认新增 2 永久替换
    private Integer replaced;

    //交接时 ，当天已生成的任务是否需要转走 0 否 1 是
    private Integer currentTaskIsTransfer=0;

    private List<PositionStepDTO>  steps;
    //回显配置字符串
    private String previewStr;

    private List<PositionMultiStepDTO> configs;

}
