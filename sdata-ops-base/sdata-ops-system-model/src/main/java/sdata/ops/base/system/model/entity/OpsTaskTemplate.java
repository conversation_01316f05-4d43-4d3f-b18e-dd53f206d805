package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 菜单权限表
 *
 * @TableName ops_task_template
 */
@TableName(value = "ops_task_template")
@Data
public class OpsTaskTemplate implements Serializable {
    /**
     * 菜单ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer templateType;

    /**
     * 模板使用场景
     */
    private Integer templateUse;

    /**
     * 模板状态 0 有效 1 作废
     */
    private Integer templateStatus;

    /**
     * 生成周期
     * manual 立即生成
     * daily 日常工作日
     * dynamic 自定义，走cron或者特殊规则，例如某个月的第几个工作日
     */
    private String schedulerType;

    /***
     * 扩展 - 触发器使用，覆盖模板内所有任务生成逻辑
     */
    private String triggerId;


    /**
     * cron表达式，由于特殊限制，此字段只存固定 频率 1 小时 2 小时
     * 进入quartz之前会替换为cronExpress
     */
    private String cronVal;


    /**
     * 模板外层权限控制，此模板属于哪个岗位的
     */
    private String orgId;


    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    @TableLogic
    private Integer deleted;

    private Integer tempSort;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}