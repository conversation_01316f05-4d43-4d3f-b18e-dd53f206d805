package sdata.ops.base.system.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsView;

@Data
@Accessors(chain = true)
public class ShortOpsViewVO {

    private String id;

    /**
     * 视图名称
     */
    private String name;


    private String source;

    /**
     * 是否显隐
     */
    private Boolean isVisible;


    public ShortOpsViewVO convert(OpsView entity, int isVisible) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.source = entity.getSource();
        this.isVisible = isVisible == 0;
        return this;
    }

}
