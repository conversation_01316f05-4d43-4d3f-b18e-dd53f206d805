package sdata.ops.base.system.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasic;

@Data
@Accessors(chain = true)
public class TaskBasicShortVO {


    private String id;

    private String name;

    public  TaskBasicShortVO convert(OpsTaskAttrBasic basic){
        TaskBasicShortVO item=new TaskBasicShortVO();
        item.setId(basic.getId()+"");
        item.setName(basic.getTaskName());
        return item;
    }
}
