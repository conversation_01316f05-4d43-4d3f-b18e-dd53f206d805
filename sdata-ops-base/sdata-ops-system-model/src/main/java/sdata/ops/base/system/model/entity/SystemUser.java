package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("ops_sys_user")
public class SystemUser extends BaseEntity implements Serializable {

    /**
     * 登录账户
     */
    private String username;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String name;


    private String role;

    /**
     * 邮箱
     */
    private String email;


    /**
     * 联系方式
     */
    private String phoneNum;

    /**
     * 所在部门
     */
    private String dept;

    /**
     * 用户状态 0 正常  1冻结
     */
    private int status;


    /**
     * 登录认证情况
     */
    private int authType;

    /**
     * 0 系统用户 1 临时用户 ext....
     */
    private int userType;


    /**
     * 是否来源同步 0 否 1 同步其他三分系统
     */
    private int synced;


    /**
     * 附属部门id
     */
    private String ownerDept;

    @TableField(exist = false)
    private String roleId;


    private static final long serialVersionUID = 1L;

}
