package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 角色和机构岗位关联表
 * @TableName ops_sys_role_org
 */
@TableName(value ="ops_sys_role_org")
@Data
public class OpsSysRoleOrg implements Serializable {
    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 部门ID
     */
    private Long orgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}