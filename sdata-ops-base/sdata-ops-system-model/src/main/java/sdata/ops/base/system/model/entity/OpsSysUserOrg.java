package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户与机构岗位关联表
 * @TableName ops_sys_user_post
 */
@TableName(value ="ops_sys_user_org")
@Data
public class OpsSysUserOrg implements Serializable {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位ID
     */
    private Long orgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}