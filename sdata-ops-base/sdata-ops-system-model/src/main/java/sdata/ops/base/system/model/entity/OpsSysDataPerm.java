package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据授权信息表
 *
 * @TableName ops_sys_data_perm
 */
@TableName(value = "ops_sys_data_perm")
@Data
public class OpsSysDataPerm implements Serializable {
    /**
     * 配置id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 授权配置名称
     */
    private String permName;


    /**
     * 授权类型
     * 1 快速 :quick
     * 2 sql脚本 :sql
     * 3 规则表达式:rule
     */
    private String permType;



    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}