package sdata.ops.base.system.model.vo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import sdata.ops.common.core.model.AuditLog;

@Data
public class AuditLogExportVo {
    @ExcelProperty(value = "日志编号")
    private String id;
    @ExcelProperty(value = "系统模块")
    private String moduleName;
    @ExcelProperty(value = "操作类型")
    private String operatorName;
    @ExcelProperty(value = "请求方式")
    private String method;
    @ExcelProperty(value = "操作地址")
    private String operatorIp;
    @ExcelProperty(value = "日志状态")
    private String status;
    @ExcelProperty(value = "操作人")
    private String nickname;
    @ExcelProperty(value = "操作日期")
    private String operatorTime;

    public static AuditLogExportVo of(AuditLog auditLog) {
        AuditLogExportVo vo = new AuditLogExportVo();
        vo.id = auditLog.getId();
        vo.moduleName = auditLog.getModuleName();
        vo.operatorName = auditLog.getOperatorName();
        vo.method = auditLog.getUrl().split(" ")[0];
        vo.operatorIp = auditLog.getOperatorIp();
        vo.status = auditLog.getStatus();
        vo.nickname = auditLog.getNickname();
        vo.operatorTime = DateUtil.formatDateTime(auditLog.getOperatorTime());
        return vo;
    }
}
