package sdata.ops.base.system.model.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.system.model.entity.OpsSysDataModel;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsSysDataModelVO extends OpsSysDataModel {

    private JSONObject config;


    public OpsSysDataModelVO convert(OpsSysDataModel model){
        OpsSysDataModelVO res=new OpsSysDataModelVO();
        BeanUtil.copyProperties(model,res);
        res.setConfig(JSONUtil.parseObj(model.getDataResultConfig()));
        return res;
    }
}
