package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 授权主体表
 * @TableName ops_sys_data_perm_auth_subject
 */
@TableName(value ="ops_sys_data_perm_auth_subject")
@Data
public class OpsSysDataPermAuthSubject {
    /**
     * 主体ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 主体类型(1-用户 2-角色 3-部门)
     */
    private Integer subjectType;

    /**
     * 主体标识(用户ID/角色ID/部门ID)
     */
    private String subjectId;

    /**
     * 主体名称(冗余存储)
     */
    private String subjectName;

    /**
     * 授权类型ID
     */
    private String permDimensionId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 权限主表id
     */
    private String permId;
}