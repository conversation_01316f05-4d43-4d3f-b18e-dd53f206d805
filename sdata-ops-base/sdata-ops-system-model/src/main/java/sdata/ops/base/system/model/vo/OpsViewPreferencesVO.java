package sdata.ops.base.system.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsViewPreferences;

@Data
@Accessors(chain = true)
public class OpsViewPreferencesVO {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 视图id
     */
    private String viewId;

    /**
     * 显隐
     */
    private Boolean isVisible;


    public OpsViewPreferences convert(OpsViewPreferencesVO entity) {
        OpsViewPreferences preferences=new OpsViewPreferences();
        preferences.setUserId(entity.getUserId());
        preferences.setViewId(entity.getViewId());
        preferences.setIsVisible(entity.getIsVisible()?0:1);
        return preferences;
    }

}
