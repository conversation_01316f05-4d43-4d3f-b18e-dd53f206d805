package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @TableName ops_sys_job
 */
@TableName(value = "ops_sys_job")
@Data
@Accessors(chain = true)
public class OpsSysJob implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务单元id
     */
    private Long taskId;

    /**
     * 任务类型 1 日常任务 2 自定义任务(日常任务是默认一个调度任务，自定义则是按需添加)
     */
    private String jobType;

    /**
     * 调度任务名称
     */
    private String jobName;

    /**
     * 任务分组
     */
    private String jobGroup;

    /**
     * cron表达式配置
     */
    private String cronExpress;

    /**
     * 策略
     */
    private String misfirePolicy = "0";

    /**
     * 任务状态
     */
    private String status = "0";

    /**
     * 任务类型,当定时任务id不是1 ,即为非缺省任务时候，需要判定类型是单元还是模板生成
     * def 0 任务单元 1 任务模板 2
     */
    private String taskType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}