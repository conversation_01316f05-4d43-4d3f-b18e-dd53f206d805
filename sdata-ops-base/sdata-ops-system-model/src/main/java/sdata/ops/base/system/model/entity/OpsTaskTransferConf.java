package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务转派配置-未来日期自动转派
 * @TableName OPS_TASK_TRANSFER_CONF
 */
@TableName(value ="ops_task_transfer_conf")
@Data
public class OpsTaskTransferConf implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 任务单元id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskUnitId;

    /**
     * 转派配置内容json字符串
     */
    private String trContent;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 开始时间
     */
    private Date trStartTime;

    /**
     * 截止时间
     */
    private Date trEndTime;


    /**
     * 创建人id
     */
    private String createBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}