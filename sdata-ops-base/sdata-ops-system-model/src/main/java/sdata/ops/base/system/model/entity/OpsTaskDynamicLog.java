package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 自定义任务已经触发成功的日志信息
 *
 * @TableName OPS_TASK_DYNAMIC_LOG
 */
@TableName(value = "ops_task_dynamic_log")
@Data
public class OpsTaskDynamicLog implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务配置id
     */
    private Long taskId;

    /**
     * 触发成功日期 yyyy-MM-dd 文本格式
     */
    private String triggerDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}