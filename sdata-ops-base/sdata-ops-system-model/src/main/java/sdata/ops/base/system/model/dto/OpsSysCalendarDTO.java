package sdata.ops.base.system.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:30
 */
@Data
public class OpsSysCalendarDTO {

    /**
     * 元旦
     */
    private String newYearDayStart;
    private String newYearDayEnd;

    /**
     * 春节
     */
    private String springFestivalStart;
    private String springFestivalEnd;

    /**
     * 清明节
     */
    private String tombSweepingDayStart;
    private String tombSweepingDayEnd;

    /**
     * 劳动节
     */
    private String labourDayStart;
    private String labourDayEnd;

    /**
     * 端午节
     */
    private String dragonBoatFestivalStart;
    private String dragonBoatFestivalEnd;

    /**
     * 中秋节
     */
    private String midAutumnFestivalStart;
    private String midAutumnFestivalEnd;

    /**
     * 国庆节
     */
    private String nationalDayStart;
    private String nationalDayEnd;
}
