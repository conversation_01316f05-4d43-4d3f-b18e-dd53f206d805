package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName ops_dashboard_layout
 */
@TableName(value ="ops_dashboard_layout")
@Data
@EqualsAndHashCode(callSuper = true)
public class OpsDashboardLayout extends BaseEntity implements Serializable {

    /**
     * 配置名称
     */
    private String name;

    /**
     * 组件位置信息配置
     */
    private String config;

    /**
     * 1默认配置 0 用户自定义配置
     */
    private Integer type;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}