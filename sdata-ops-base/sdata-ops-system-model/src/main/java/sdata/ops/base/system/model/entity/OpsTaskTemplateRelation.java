package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 菜单权限表
 * @TableName ops_task_template_relation
 */
@TableName(value ="ops_task_template_relation")
@Data
@Accessors(chain = true)
public class OpsTaskTemplateRelation implements Serializable {
    /**
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 任务单元id
     */
    private String taskId;

    /**
     * 任务单元复制id
     */
    private String taskReplicaId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}