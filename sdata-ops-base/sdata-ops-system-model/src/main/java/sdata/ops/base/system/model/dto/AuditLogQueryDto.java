package sdata.ops.base.system.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import sdata.ops.common.annotation.ExportGroup;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AuditLogQueryDto {
    private int pageNo = 1;
    private int pageSize = 10;
    //操作编码
    private String operatorCode;
    /**
     * 操作时间-开始
     */
    @NotNull(groups = ExportGroup.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date opStartTime;
    /**
     * 操作时间-结束
     */
    @NotNull(groups = ExportGroup.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date opEndTime;
}
