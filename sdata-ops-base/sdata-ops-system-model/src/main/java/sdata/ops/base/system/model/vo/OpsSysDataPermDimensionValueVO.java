package sdata.ops.base.system.model.vo;


import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import sdata.ops.base.system.model.entity.OpsSysDataPermDimensionValue;
import sdata.ops.common.core.util.JsonUtils;

import java.util.Date;
import java.util.Map;

@Data
public class OpsSysDataPermDimensionValueVO {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联表权限主表id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long permId;


    /**
     * 内容配置
     */
    private Map<String,Object> valueConf;

    /**
     * 内容类型( 1-范围值 2-表达式)
     */
    private Integer valueType;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public OpsSysDataPermDimensionValue covertEntity() {
        OpsSysDataPermDimensionValue entity = new OpsSysDataPermDimensionValue();
        entity.setId(this.id);
        entity.setPermId(this.permId);
        entity.setValueConf(JSONUtil.toJsonStr(this.valueConf));
        entity.setValueType(this.valueType);
        entity.setCreateTime(this.createTime);
        entity.setUpdateTime(this.updateTime);
        return entity;
    }
    public static OpsSysDataPermDimensionValueVO covertVO(OpsSysDataPermDimensionValue entity) {
        OpsSysDataPermDimensionValueVO vo = new OpsSysDataPermDimensionValueVO();
        vo.setId(entity.getId());
        vo.setPermId(entity.getPermId());
        vo.setValueConf(JsonUtils.toMap(entity.getValueConf()));
        vo.setValueType(entity.getValueType());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

}
