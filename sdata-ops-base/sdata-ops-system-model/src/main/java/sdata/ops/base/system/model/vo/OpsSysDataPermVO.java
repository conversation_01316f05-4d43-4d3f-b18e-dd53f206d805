package sdata.ops.base.system.model.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdata.ops.base.system.model.entity.OpsSysDataPerm;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsSysDataPermVO extends OpsSysDataPerm {

    private JSONObject config;

    private List<String> permValIds;

    private List<String> permValList;


    public OpsSysDataPermVO convert(OpsSysDataPerm perm, Map<String, String> map) {
        OpsSysDataPermVO res = new OpsSysDataPermVO();
        BeanUtil.copyProperties(perm, res);
        List<String> vars = new ArrayList<>();
        for (String id : res.getPermValIds()) {
            vars.add(map.get(id));
        }
        res.setPermValList(vars);
        return res;
    }

}
