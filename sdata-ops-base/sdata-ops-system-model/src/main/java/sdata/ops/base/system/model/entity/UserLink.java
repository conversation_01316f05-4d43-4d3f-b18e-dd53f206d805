package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_user_links")
public class UserLink {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private String title;
    private Integer linkType;  // 1-站内 2-站外
    private String icon;
    private String url;        // 站外链接
    private String routePath;  // 站内路由
    private Integer sort;
    private Date createTime;
    private Date updateTime;
}
