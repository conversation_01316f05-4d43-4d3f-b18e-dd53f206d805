package sdata.ops.base.system.model.vo;


import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.SystemUser;

import java.util.*;

@Data
@Accessors(chain = true)
public class SystemUserVO {


    private String id;
    /**
     * 登录账户
     */
    private String username;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String name;


    /**
     * 绑定角色
     */

    private String role;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 绑定角色名称
     */
    private String roleNames;
    /**
     * 所在部门/岗位
     */
    private String dept;

    /**
     * 所在部门/岗位
     */
    private String deptNames;


    /**
     * 用户状态 0 正常  1冻结
     */
    private int status;


    /**
     * 登录认证情况
     */
    private int authType;


    /**
     * 是否来源同步 0 否 1 同步其他三分系统
     */
    private int synced;

    private Integer leader=0;

    private List<MapperVO> orgInfos = new ArrayList<>();


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    //归属机构id
    private String ownerDept;
    //归属机构名称
    private String ownerDeptName;

    public SystemUserVO convertVo(SystemUser user) {
        SystemUserVO vo = new SystemUserVO();
        BeanUtil.copyProperties(user, vo);
        return vo;
    }

    public SystemUserVO convertVoFill(SystemUser user, Map<String, String> mapper) {
        SystemUserVO vo = new SystemUserVO();
        BeanUtil.copyProperties(user, vo);
        vo.setDeptNames(genericName(vo.getDept(), mapper));
        vo.setRoleNames(genericName(vo.getRole(), mapper));
        if (StringUtils.hasText(user.getOwnerDept())) {
            vo.setOwnerDeptName(mapper.get(user.getOwnerDept()));
        }
        return vo;
    }

    public String genericName(String ids, Map<String, String> mapper) {
        if (!StringUtils.hasText(ids)) {
            return "";
        }
        StringJoiner append = new StringJoiner(",");
        for (String string : ids.split(",")) {
            append.add(mapper.get(string));
        }
        return append.toString();
    }

    public SystemUser convertEn() {
        SystemUser user = new SystemUser();
        BeanUtil.copyProperties(this, user);
        return user;
    }
}
