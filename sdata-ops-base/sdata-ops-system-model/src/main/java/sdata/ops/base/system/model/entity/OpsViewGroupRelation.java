package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName ops_view_group_relation
 */
@TableName(value ="ops_view_group_relation")
@Data
public class OpsViewGroupRelation implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Long groupId;

    /**
     * 
     */
    private Long viewId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}