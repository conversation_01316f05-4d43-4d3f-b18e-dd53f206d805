package sdata.ops.base.system.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class TaskTransferVO {

    private String orgId;

    private String orgName;

    private String userId;

    private String userName;

    private String taskDesc;

    private Boolean immediate;

    private Date tranEndTime;

    private List<String> taskIds;

    private String taskId;

    //转派到人，或是到岗位  1 到岗 2 到人
    private String type;

    // 1 经办转派 2复核转派
    @TableField(exist = false)
    private String transferType;

}
