package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName ops_task_job_relation
 */
@TableName(value ="ops_task_job_relation")
@Data
public class OpsTaskJobRelation implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 任务调度id
     */
    private String jobId;

    /**
     * 任务单元id
     */
    private String taskId;

    /**
     * 任务表来源不同
     * 1 是basic表  基础单元表配置
     * 2 是replica 表 由模板关系的副本数据
     */
    private String taskType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}