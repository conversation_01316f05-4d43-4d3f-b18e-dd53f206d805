package sdata.ops.base.system.model.entity;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName OPS_TASK_ATTR_BASIC_SLAVE
 */
@TableName(value = "ops_task_attr_basic_slave")
public class OpsTaskAttrBasicSlave implements Serializable {
    /**
     *
     */
    @TableId
    private String id;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务单元状态 0 未上线 1 上线
     */
    private Integer taskStatus;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务触发类型 （manual 手动 auto 自动)
     */
    private String taskTriggerType;

    /**
     * 任务触发器引用单元id
     */
    private String taskTriggerId;

    /**
     * 任务配置定时任务表达式
     */
    private String taskCronVal;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    private String taskCompleteType;

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    private String taskCompleteUnitId;

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    private String taskAuditType;

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    private String taskAuditUnitId;

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    private String taskWarnNotice;

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    private String taskPriority;

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    private String taskLevel;

    /**
     * 任务是否需要附件（0否1 是）
     */
    private String taskAttachmentsType;

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    private String taskOwnerType;

    /**
     * 任务归属id ，岗位id或者人员id
     */
    private String taskOwnerId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskOwnerVal;

    /**
     * 任务是否需要复核 （0否1 是）
     */
    private String taskCheckReq;

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    private String taskCheckType;

    /**
     * 复核权限对象id
     */
    private String taskCheckId;

    /**
     * 复核权限对象真实值，冗余字段
     */
    private String taskCheckVal;

    /**
     *
     */
    private Date taskStartTime;

    /**
     *
     */
    private Date taskEndTime;

    /**
     * 任务属性标签
     */
    private String taskTags;

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    private String taskAuthType;

    /**
     * 任务归属id
     */
    private String taskAuthId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskAuthVal;

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    private String taskAuthScope;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private String updateBy;

    /**
     * 删除标记
     */
    private String deleted;

    /**
     * 任务是否可以顺延 0 否 1是
     */
    private String taskDeferredType;

    /**
     * 任务顺延几天，当前几天是默认工作日
     */
    private Integer taskDeferredCount;

    /**
     * 任务完成需要依赖节点已经完成
     */
    private String dependOnIds;

    /**
     * 必填项校验，点击完成前校验任务自身那些属性是不能为空
     */
    private String requiredItem;

    /**
     * 任务生成来源-任务单元id
     */
    private String taskRefId;

    /**
     * 冗余岗位id方便查询
     */
    private String ownerOrgId;

    /**
     * 冗余复核岗位id
     */
    private String checkOrgId;

    /**
     * 真实允许开始时间阈值
     */
    private Integer taskStartThreshold;

    /**
     * 真实允许结束时间阈值
     */
    private Integer taskEndThreshold;

    /**
     * 任务绑定模板id
     */
    private Long taskBindTemplateId;

    /**
     *
     */
    private Integer taskSort;

    /**
     * 数据最低权限下可见性0 否 1 是
     */
    private Integer accessLevel;

    /**
     *
     */
    private Integer workAmount;

    /**
     *
     */
    private String workAmountFlag;

    /**
     *
     */
    private Integer importStatus;

    /**
     * 任务创建追加日期标记
     */
    private Integer taskNameAppend;

    /**
     * 任务创建追加日期标记类型 1 年 2 季度 3 月 4 周
     */
    private Integer taskAppendType;

    /**
     * 任务创建类型 0 update 1 insert
     */
    private Integer taskCreateType;

    /**
     * 副本转派id
     */
    private Long transferId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public String getId() {
        return id;
    }

    /**
     *
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 父id
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 父id
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 任务编号
     */
    public String getTaskNo() {
        return taskNo;
    }

    /**
     * 任务编号
     */
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    /**
     * 任务单元状态 0 未上线 1 上线
     */
    public Integer getTaskStatus() {
        return taskStatus;
    }

    /**
     * 任务单元状态 0 未上线 1 上线
     */
    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    /**
     * 任务名称
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 任务名称
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 任务类型
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * 任务类型
     */
    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    /**
     * 任务触发类型 （manual 手动 auto 自动)
     */
    public String getTaskTriggerType() {
        return taskTriggerType;
    }

    /**
     * 任务触发类型 （manual 手动 auto 自动)
     */
    public void setTaskTriggerType(String taskTriggerType) {
        this.taskTriggerType = taskTriggerType;
    }

    /**
     * 任务触发器引用单元id
     */
    public String getTaskTriggerId() {
        return taskTriggerId;
    }

    /**
     * 任务触发器引用单元id
     */
    public void setTaskTriggerId(String taskTriggerId) {
        this.taskTriggerId = taskTriggerId;
    }

    /**
     * 任务配置定时任务表达式
     */
    public String getTaskCronVal() {
        return taskCronVal;
    }

    /**
     * 任务配置定时任务表达式
     */
    public void setTaskCronVal(String taskCronVal) {
        this.taskCronVal = taskCronVal;
    }

    /**
     * 任务描述
     */
    public String getTaskDesc() {
        return taskDesc;
    }

    /**
     * 任务描述
     */
    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    public String getTaskCompleteType() {
        return taskCompleteType;
    }

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    public void setTaskCompleteType(String taskCompleteType) {
        this.taskCompleteType = taskCompleteType;
    }

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    public String getTaskCompleteUnitId() {
        return taskCompleteUnitId;
    }

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    public void setTaskCompleteUnitId(String taskCompleteUnitId) {
        this.taskCompleteUnitId = taskCompleteUnitId;
    }

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    public String getTaskAuditType() {
        return taskAuditType;
    }

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    public void setTaskAuditType(String taskAuditType) {
        this.taskAuditType = taskAuditType;
    }

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    public String getTaskAuditUnitId() {
        return taskAuditUnitId;
    }

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    public void setTaskAuditUnitId(String taskAuditUnitId) {
        this.taskAuditUnitId = taskAuditUnitId;
    }

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    public String getTaskWarnNotice() {
        return taskWarnNotice;
    }

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    public void setTaskWarnNotice(String taskWarnNotice) {
        this.taskWarnNotice = taskWarnNotice;
    }

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    public String getTaskPriority() {
        return taskPriority;
    }

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    public void setTaskPriority(String taskPriority) {
        this.taskPriority = taskPriority;
    }

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    public String getTaskLevel() {
        return taskLevel;
    }

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    public void setTaskLevel(String taskLevel) {
        this.taskLevel = taskLevel;
    }

    /**
     * 任务是否需要附件（0否1 是）
     */
    public String getTaskAttachmentsType() {
        return taskAttachmentsType;
    }

    /**
     * 任务是否需要附件（0否1 是）
     */
    public void setTaskAttachmentsType(String taskAttachmentsType) {
        this.taskAttachmentsType = taskAttachmentsType;
    }

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    public String getTaskOwnerType() {
        return taskOwnerType;
    }

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    public void setTaskOwnerType(String taskOwnerType) {
        this.taskOwnerType = taskOwnerType;
    }

    /**
     * 任务归属id ，岗位id或者人员id
     */
    public String getTaskOwnerId() {
        return taskOwnerId;
    }

    /**
     * 任务归属id ，岗位id或者人员id
     */
    public void setTaskOwnerId(String taskOwnerId) {
        this.taskOwnerId = taskOwnerId;
    }

    /**
     * 任务归属真实值，冗余字段
     */
    public String getTaskOwnerVal() {
        return taskOwnerVal;
    }

    /**
     * 任务归属真实值，冗余字段
     */
    public void setTaskOwnerVal(String taskOwnerVal) {
        this.taskOwnerVal = taskOwnerVal;
    }

    /**
     * 任务是否需要复核 （0否1 是）
     */
    public String getTaskCheckReq() {
        return taskCheckReq;
    }

    /**
     * 任务是否需要复核 （0否1 是）
     */
    public void setTaskCheckReq(String taskCheckReq) {
        this.taskCheckReq = taskCheckReq;
    }

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    public String getTaskCheckType() {
        return taskCheckType;
    }

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    public void setTaskCheckType(String taskCheckType) {
        this.taskCheckType = taskCheckType;
    }

    /**
     * 复核权限对象id
     */
    public String getTaskCheckId() {
        return taskCheckId;
    }

    /**
     * 复核权限对象id
     */
    public void setTaskCheckId(String taskCheckId) {
        this.taskCheckId = taskCheckId;
    }

    /**
     * 复核权限对象真实值，冗余字段
     */
    public String getTaskCheckVal() {
        return taskCheckVal;
    }

    /**
     * 复核权限对象真实值，冗余字段
     */
    public void setTaskCheckVal(String taskCheckVal) {
        this.taskCheckVal = taskCheckVal;
    }

    /**
     *
     */
    public Date getTaskStartTime() {
        return taskStartTime;
    }

    /**
     *
     */
    public void setTaskStartTime(Date taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    /**
     *
     */
    public Date getTaskEndTime() {
        return taskEndTime;
    }

    /**
     *
     */
    public void setTaskEndTime(Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    /**
     * 任务属性标签
     */
    public String getTaskTags() {
        return taskTags;
    }

    /**
     * 任务属性标签
     */
    public void setTaskTags(String taskTags) {
        this.taskTags = taskTags;
    }

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    public String getTaskAuthType() {
        return taskAuthType;
    }

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    public void setTaskAuthType(String taskAuthType) {
        this.taskAuthType = taskAuthType;
    }

    /**
     * 任务归属id
     */
    public String getTaskAuthId() {
        return taskAuthId;
    }

    /**
     * 任务归属id
     */
    public void setTaskAuthId(String taskAuthId) {
        this.taskAuthId = taskAuthId;
    }

    /**
     * 任务归属真实值，冗余字段
     */
    public String getTaskAuthVal() {
        return taskAuthVal;
    }

    /**
     * 任务归属真实值，冗余字段
     */
    public void setTaskAuthVal(String taskAuthVal) {
        this.taskAuthVal = taskAuthVal;
    }

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    public String getTaskAuthScope() {
        return taskAuthScope;
    }

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    public void setTaskAuthScope(String taskAuthScope) {
        this.taskAuthScope = taskAuthScope;
    }

    /**
     *
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     *
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     *
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 删除标记
     */
    public String getDeleted() {
        return deleted;
    }

    /**
     * 删除标记
     */
    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    /**
     * 任务是否可以顺延 0 否 1是
     */
    public String getTaskDeferredType() {
        return taskDeferredType;
    }

    /**
     * 任务是否可以顺延 0 否 1是
     */
    public void setTaskDeferredType(String taskDeferredType) {
        this.taskDeferredType = taskDeferredType;
    }

    /**
     * 任务顺延几天，当前几天是默认工作日
     */
    public Integer getTaskDeferredCount() {
        return taskDeferredCount;
    }

    /**
     * 任务顺延几天，当前几天是默认工作日
     */
    public void setTaskDeferredCount(Integer taskDeferredCount) {
        this.taskDeferredCount = taskDeferredCount;
    }

    /**
     * 任务完成需要依赖节点已经完成
     */
    public String getDependOnIds() {
        return dependOnIds;
    }

    /**
     * 任务完成需要依赖节点已经完成
     */
    public void setDependOnIds(String dependOnIds) {
        this.dependOnIds = dependOnIds;
    }

    /**
     * 必填项校验，点击完成前校验任务自身那些属性是不能为空
     */
    public String getRequiredItem() {
        return requiredItem;
    }

    /**
     * 必填项校验，点击完成前校验任务自身那些属性是不能为空
     */
    public void setRequiredItem(String requiredItem) {
        this.requiredItem = requiredItem;
    }

    /**
     * 任务生成来源-任务单元id
     */
    public String getTaskRefId() {
        return taskRefId;
    }

    /**
     * 任务生成来源-任务单元id
     */
    public void setTaskRefId(String taskRefId) {
        this.taskRefId = taskRefId;
    }

    /**
     * 冗余岗位id方便查询
     */
    public String getOwnerOrgId() {
        return ownerOrgId;
    }

    /**
     * 冗余岗位id方便查询
     */
    public void setOwnerOrgId(String ownerOrgId) {
        this.ownerOrgId = ownerOrgId;
    }

    /**
     * 冗余复核岗位id
     */
    public String getCheckOrgId() {
        return checkOrgId;
    }

    /**
     * 冗余复核岗位id
     */
    public void setCheckOrgId(String checkOrgId) {
        this.checkOrgId = checkOrgId;
    }

    /**
     * 真实允许开始时间阈值
     */
    public Integer getTaskStartThreshold() {
        return taskStartThreshold;
    }

    /**
     * 真实允许开始时间阈值
     */
    public void setTaskStartThreshold(Integer taskStartThreshold) {
        this.taskStartThreshold = taskStartThreshold;
    }

    /**
     * 真实允许结束时间阈值
     */
    public Integer getTaskEndThreshold() {
        return taskEndThreshold;
    }

    /**
     * 真实允许结束时间阈值
     */
    public void setTaskEndThreshold(Integer taskEndThreshold) {
        this.taskEndThreshold = taskEndThreshold;
    }

    /**
     * 任务绑定模板id
     */
    public Long getTaskBindTemplateId() {
        return taskBindTemplateId;
    }

    /**
     * 任务绑定模板id
     */
    public void setTaskBindTemplateId(Long taskBindTemplateId) {
        this.taskBindTemplateId = taskBindTemplateId;
    }

    /**
     *
     */
    public Integer getTaskSort() {
        return taskSort;
    }

    /**
     *
     */
    public void setTaskSort(Integer taskSort) {
        this.taskSort = taskSort;
    }

    /**
     * 数据最低权限下可见性0 否 1 是
     */
    public Integer getAccessLevel() {
        return accessLevel;
    }

    /**
     * 数据最低权限下可见性0 否 1 是
     */
    public void setAccessLevel(Integer accessLevel) {
        this.accessLevel = accessLevel;
    }

    /**
     *
     */
    public Integer getWorkAmount() {
        return workAmount;
    }

    /**
     *
     */
    public void setWorkAmount(Integer workAmount) {
        this.workAmount = workAmount;
    }

    /**
     *
     */
    public String getWorkAmountFlag() {
        return workAmountFlag;
    }

    /**
     *
     */
    public void setWorkAmountFlag(String workAmountFlag) {
        this.workAmountFlag = workAmountFlag;
    }

    /**
     *
     */
    public Integer getImportStatus() {
        return importStatus;
    }

    /**
     *
     */
    public void setImportStatus(Integer importStatus) {
        this.importStatus = importStatus;
    }

    /**
     * 任务创建追加日期标记
     */
    public Integer getTaskNameAppend() {
        return taskNameAppend;
    }

    /**
     * 任务创建追加日期标记
     */
    public void setTaskNameAppend(Integer taskNameAppend) {
        this.taskNameAppend = taskNameAppend;
    }

    /**
     * 任务创建追加日期标记类型 1 年 2 季度 3 月 4 周
     */
    public Integer getTaskAppendType() {
        return taskAppendType;
    }

    /**
     * 任务创建追加日期标记类型 1 年 2 季度 3 月 4 周
     */
    public void setTaskAppendType(Integer taskAppendType) {
        this.taskAppendType = taskAppendType;
    }

    /**
     * 任务创建类型 0 update 1 insert
     */
    public Integer getTaskCreateType() {
        return taskCreateType;
    }

    /**
     * 任务创建类型 0 update 1 insert
     */
    public void setTaskCreateType(Integer taskCreateType) {
        this.taskCreateType = taskCreateType;
    }

    /**
     * 副本转派id
     */
    public Long getTransferId() {
        return transferId;
    }

    /**
     * 副本转派id
     */
    public void setTransferId(Long transferId) {
        this.transferId = transferId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OpsTaskAttrBasicSlave other = (OpsTaskAttrBasicSlave) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
                && (this.getTaskNo() == null ? other.getTaskNo() == null : this.getTaskNo().equals(other.getTaskNo()))
                && (this.getTaskStatus() == null ? other.getTaskStatus() == null : this.getTaskStatus().equals(other.getTaskStatus()))
                && (this.getTaskName() == null ? other.getTaskName() == null : this.getTaskName().equals(other.getTaskName()))
                && (this.getTaskType() == null ? other.getTaskType() == null : this.getTaskType().equals(other.getTaskType()))
                && (this.getTaskTriggerType() == null ? other.getTaskTriggerType() == null : this.getTaskTriggerType().equals(other.getTaskTriggerType()))
                && (this.getTaskTriggerId() == null ? other.getTaskTriggerId() == null : this.getTaskTriggerId().equals(other.getTaskTriggerId()))
                && (this.getTaskCronVal() == null ? other.getTaskCronVal() == null : this.getTaskCronVal().equals(other.getTaskCronVal()))
                && (this.getTaskDesc() == null ? other.getTaskDesc() == null : this.getTaskDesc().equals(other.getTaskDesc()))
                && (this.getTaskCompleteType() == null ? other.getTaskCompleteType() == null : this.getTaskCompleteType().equals(other.getTaskCompleteType()))
                && (this.getTaskCompleteUnitId() == null ? other.getTaskCompleteUnitId() == null : this.getTaskCompleteUnitId().equals(other.getTaskCompleteUnitId()))
                && (this.getTaskAuditType() == null ? other.getTaskAuditType() == null : this.getTaskAuditType().equals(other.getTaskAuditType()))
                && (this.getTaskAuditUnitId() == null ? other.getTaskAuditUnitId() == null : this.getTaskAuditUnitId().equals(other.getTaskAuditUnitId()))
                && (this.getTaskWarnNotice() == null ? other.getTaskWarnNotice() == null : this.getTaskWarnNotice().equals(other.getTaskWarnNotice()))
                && (this.getTaskPriority() == null ? other.getTaskPriority() == null : this.getTaskPriority().equals(other.getTaskPriority()))
                && (this.getTaskLevel() == null ? other.getTaskLevel() == null : this.getTaskLevel().equals(other.getTaskLevel()))
                && (this.getTaskAttachmentsType() == null ? other.getTaskAttachmentsType() == null : this.getTaskAttachmentsType().equals(other.getTaskAttachmentsType()))
                && (this.getTaskOwnerType() == null ? other.getTaskOwnerType() == null : this.getTaskOwnerType().equals(other.getTaskOwnerType()))
                && (this.getTaskOwnerId() == null ? other.getTaskOwnerId() == null : this.getTaskOwnerId().equals(other.getTaskOwnerId()))
                && (this.getTaskOwnerVal() == null ? other.getTaskOwnerVal() == null : this.getTaskOwnerVal().equals(other.getTaskOwnerVal()))
                && (this.getTaskCheckReq() == null ? other.getTaskCheckReq() == null : this.getTaskCheckReq().equals(other.getTaskCheckReq()))
                && (this.getTaskCheckType() == null ? other.getTaskCheckType() == null : this.getTaskCheckType().equals(other.getTaskCheckType()))
                && (this.getTaskCheckId() == null ? other.getTaskCheckId() == null : this.getTaskCheckId().equals(other.getTaskCheckId()))
                && (this.getTaskCheckVal() == null ? other.getTaskCheckVal() == null : this.getTaskCheckVal().equals(other.getTaskCheckVal()))
                && (this.getTaskStartTime() == null ? other.getTaskStartTime() == null : this.getTaskStartTime().equals(other.getTaskStartTime()))
                && (this.getTaskEndTime() == null ? other.getTaskEndTime() == null : this.getTaskEndTime().equals(other.getTaskEndTime()))
                && (this.getTaskTags() == null ? other.getTaskTags() == null : this.getTaskTags().equals(other.getTaskTags()))
                && (this.getTaskAuthType() == null ? other.getTaskAuthType() == null : this.getTaskAuthType().equals(other.getTaskAuthType()))
                && (this.getTaskAuthId() == null ? other.getTaskAuthId() == null : this.getTaskAuthId().equals(other.getTaskAuthId()))
                && (this.getTaskAuthVal() == null ? other.getTaskAuthVal() == null : this.getTaskAuthVal().equals(other.getTaskAuthVal()))
                && (this.getTaskAuthScope() == null ? other.getTaskAuthScope() == null : this.getTaskAuthScope().equals(other.getTaskAuthScope()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
                && (this.getTaskDeferredType() == null ? other.getTaskDeferredType() == null : this.getTaskDeferredType().equals(other.getTaskDeferredType()))
                && (this.getTaskDeferredCount() == null ? other.getTaskDeferredCount() == null : this.getTaskDeferredCount().equals(other.getTaskDeferredCount()))
                && (this.getDependOnIds() == null ? other.getDependOnIds() == null : this.getDependOnIds().equals(other.getDependOnIds()))
                && (this.getRequiredItem() == null ? other.getRequiredItem() == null : this.getRequiredItem().equals(other.getRequiredItem()))
                && (this.getTaskRefId() == null ? other.getTaskRefId() == null : this.getTaskRefId().equals(other.getTaskRefId()))
                && (this.getOwnerOrgId() == null ? other.getOwnerOrgId() == null : this.getOwnerOrgId().equals(other.getOwnerOrgId()))
                && (this.getCheckOrgId() == null ? other.getCheckOrgId() == null : this.getCheckOrgId().equals(other.getCheckOrgId()))
                && (this.getTaskStartThreshold() == null ? other.getTaskStartThreshold() == null : this.getTaskStartThreshold().equals(other.getTaskStartThreshold()))
                && (this.getTaskEndThreshold() == null ? other.getTaskEndThreshold() == null : this.getTaskEndThreshold().equals(other.getTaskEndThreshold()))
                && (this.getTaskBindTemplateId() == null ? other.getTaskBindTemplateId() == null : this.getTaskBindTemplateId().equals(other.getTaskBindTemplateId()))
                && (this.getTaskSort() == null ? other.getTaskSort() == null : this.getTaskSort().equals(other.getTaskSort()))
                && (this.getAccessLevel() == null ? other.getAccessLevel() == null : this.getAccessLevel().equals(other.getAccessLevel()))
                && (this.getWorkAmount() == null ? other.getWorkAmount() == null : this.getWorkAmount().equals(other.getWorkAmount()))
                && (this.getWorkAmountFlag() == null ? other.getWorkAmountFlag() == null : this.getWorkAmountFlag().equals(other.getWorkAmountFlag()))
                && (this.getImportStatus() == null ? other.getImportStatus() == null : this.getImportStatus().equals(other.getImportStatus()))
                && (this.getTaskNameAppend() == null ? other.getTaskNameAppend() == null : this.getTaskNameAppend().equals(other.getTaskNameAppend()))
                && (this.getTaskAppendType() == null ? other.getTaskAppendType() == null : this.getTaskAppendType().equals(other.getTaskAppendType()))
                && (this.getTaskCreateType() == null ? other.getTaskCreateType() == null : this.getTaskCreateType().equals(other.getTaskCreateType()))
                && (this.getTransferId() == null ? other.getTransferId() == null : this.getTransferId().equals(other.getTransferId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getTaskNo() == null) ? 0 : getTaskNo().hashCode());
        result = prime * result + ((getTaskStatus() == null) ? 0 : getTaskStatus().hashCode());
        result = prime * result + ((getTaskName() == null) ? 0 : getTaskName().hashCode());
        result = prime * result + ((getTaskType() == null) ? 0 : getTaskType().hashCode());
        result = prime * result + ((getTaskTriggerType() == null) ? 0 : getTaskTriggerType().hashCode());
        result = prime * result + ((getTaskTriggerId() == null) ? 0 : getTaskTriggerId().hashCode());
        result = prime * result + ((getTaskCronVal() == null) ? 0 : getTaskCronVal().hashCode());
        result = prime * result + ((getTaskDesc() == null) ? 0 : getTaskDesc().hashCode());
        result = prime * result + ((getTaskCompleteType() == null) ? 0 : getTaskCompleteType().hashCode());
        result = prime * result + ((getTaskCompleteUnitId() == null) ? 0 : getTaskCompleteUnitId().hashCode());
        result = prime * result + ((getTaskAuditType() == null) ? 0 : getTaskAuditType().hashCode());
        result = prime * result + ((getTaskAuditUnitId() == null) ? 0 : getTaskAuditUnitId().hashCode());
        result = prime * result + ((getTaskWarnNotice() == null) ? 0 : getTaskWarnNotice().hashCode());
        result = prime * result + ((getTaskPriority() == null) ? 0 : getTaskPriority().hashCode());
        result = prime * result + ((getTaskLevel() == null) ? 0 : getTaskLevel().hashCode());
        result = prime * result + ((getTaskAttachmentsType() == null) ? 0 : getTaskAttachmentsType().hashCode());
        result = prime * result + ((getTaskOwnerType() == null) ? 0 : getTaskOwnerType().hashCode());
        result = prime * result + ((getTaskOwnerId() == null) ? 0 : getTaskOwnerId().hashCode());
        result = prime * result + ((getTaskOwnerVal() == null) ? 0 : getTaskOwnerVal().hashCode());
        result = prime * result + ((getTaskCheckReq() == null) ? 0 : getTaskCheckReq().hashCode());
        result = prime * result + ((getTaskCheckType() == null) ? 0 : getTaskCheckType().hashCode());
        result = prime * result + ((getTaskCheckId() == null) ? 0 : getTaskCheckId().hashCode());
        result = prime * result + ((getTaskCheckVal() == null) ? 0 : getTaskCheckVal().hashCode());
        result = prime * result + ((getTaskStartTime() == null) ? 0 : getTaskStartTime().hashCode());
        result = prime * result + ((getTaskEndTime() == null) ? 0 : getTaskEndTime().hashCode());
        result = prime * result + ((getTaskTags() == null) ? 0 : getTaskTags().hashCode());
        result = prime * result + ((getTaskAuthType() == null) ? 0 : getTaskAuthType().hashCode());
        result = prime * result + ((getTaskAuthId() == null) ? 0 : getTaskAuthId().hashCode());
        result = prime * result + ((getTaskAuthVal() == null) ? 0 : getTaskAuthVal().hashCode());
        result = prime * result + ((getTaskAuthScope() == null) ? 0 : getTaskAuthScope().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getTaskDeferredType() == null) ? 0 : getTaskDeferredType().hashCode());
        result = prime * result + ((getTaskDeferredCount() == null) ? 0 : getTaskDeferredCount().hashCode());
        result = prime * result + ((getDependOnIds() == null) ? 0 : getDependOnIds().hashCode());
        result = prime * result + ((getRequiredItem() == null) ? 0 : getRequiredItem().hashCode());
        result = prime * result + ((getTaskRefId() == null) ? 0 : getTaskRefId().hashCode());
        result = prime * result + ((getOwnerOrgId() == null) ? 0 : getOwnerOrgId().hashCode());
        result = prime * result + ((getCheckOrgId() == null) ? 0 : getCheckOrgId().hashCode());
        result = prime * result + ((getTaskStartThreshold() == null) ? 0 : getTaskStartThreshold().hashCode());
        result = prime * result + ((getTaskEndThreshold() == null) ? 0 : getTaskEndThreshold().hashCode());
        result = prime * result + ((getTaskBindTemplateId() == null) ? 0 : getTaskBindTemplateId().hashCode());
        result = prime * result + ((getTaskSort() == null) ? 0 : getTaskSort().hashCode());
        result = prime * result + ((getAccessLevel() == null) ? 0 : getAccessLevel().hashCode());
        result = prime * result + ((getWorkAmount() == null) ? 0 : getWorkAmount().hashCode());
        result = prime * result + ((getWorkAmountFlag() == null) ? 0 : getWorkAmountFlag().hashCode());
        result = prime * result + ((getImportStatus() == null) ? 0 : getImportStatus().hashCode());
        result = prime * result + ((getTaskNameAppend() == null) ? 0 : getTaskNameAppend().hashCode());
        result = prime * result + ((getTaskAppendType() == null) ? 0 : getTaskAppendType().hashCode());
        result = prime * result + ((getTaskCreateType() == null) ? 0 : getTaskCreateType().hashCode());
        result = prime * result + ((getTransferId() == null) ? 0 : getTransferId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", parentId=").append(parentId);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", taskName=").append(taskName);
        sb.append(", taskType=").append(taskType);
        sb.append(", taskTriggerType=").append(taskTriggerType);
        sb.append(", taskTriggerId=").append(taskTriggerId);
        sb.append(", taskCronVal=").append(taskCronVal);
        sb.append(", taskDesc=").append(taskDesc);
        sb.append(", taskCompleteType=").append(taskCompleteType);
        sb.append(", taskCompleteUnitId=").append(taskCompleteUnitId);
        sb.append(", taskAuditType=").append(taskAuditType);
        sb.append(", taskAuditUnitId=").append(taskAuditUnitId);
        sb.append(", taskWarnNotice=").append(taskWarnNotice);
        sb.append(", taskPriority=").append(taskPriority);
        sb.append(", taskLevel=").append(taskLevel);
        sb.append(", taskAttachmentsType=").append(taskAttachmentsType);
        sb.append(", taskOwnerType=").append(taskOwnerType);
        sb.append(", taskOwnerId=").append(taskOwnerId);
        sb.append(", taskOwnerVal=").append(taskOwnerVal);
        sb.append(", taskCheckReq=").append(taskCheckReq);
        sb.append(", taskCheckType=").append(taskCheckType);
        sb.append(", taskCheckId=").append(taskCheckId);
        sb.append(", taskCheckVal=").append(taskCheckVal);
        sb.append(", taskStartTime=").append(taskStartTime);
        sb.append(", taskEndTime=").append(taskEndTime);
        sb.append(", taskTags=").append(taskTags);
        sb.append(", taskAuthType=").append(taskAuthType);
        sb.append(", taskAuthId=").append(taskAuthId);
        sb.append(", taskAuthVal=").append(taskAuthVal);
        sb.append(", taskAuthScope=").append(taskAuthScope);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", deleted=").append(deleted);
        sb.append(", taskDeferredType=").append(taskDeferredType);
        sb.append(", taskDeferredCount=").append(taskDeferredCount);
        sb.append(", dependOnIds=").append(dependOnIds);
        sb.append(", requiredItem=").append(requiredItem);
        sb.append(", taskRefId=").append(taskRefId);
        sb.append(", ownerOrgId=").append(ownerOrgId);
        sb.append(", checkOrgId=").append(checkOrgId);
        sb.append(", taskStartThreshold=").append(taskStartThreshold);
        sb.append(", taskEndThreshold=").append(taskEndThreshold);
        sb.append(", taskBindTemplateId=").append(taskBindTemplateId);
        sb.append(", taskSort=").append(taskSort);
        sb.append(", accessLevel=").append(accessLevel);
        sb.append(", workAmount=").append(workAmount);
        sb.append(", workAmountFlag=").append(workAmountFlag);
        sb.append(", importStatus=").append(importStatus);
        sb.append(", taskNameAppend=").append(taskNameAppend);
        sb.append(", taskAppendType=").append(taskAppendType);
        sb.append(", taskCreateType=").append(taskCreateType);
        sb.append(", transferId=").append(transferId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }


    public OpsTaskAttrBasicSlave convert(OpsTaskAttrBasicReplica replica, Long trId) {
        OpsTaskAttrBasicSlave s = new OpsTaskAttrBasicSlave();
        BeanUtil.copyProperties(replica, s);
        s.setTransferId(trId);
        return s;
    }

    public OpsTaskAttrBasicReplica convert(OpsTaskAttrBasicSlave replica) {
        OpsTaskAttrBasicReplica s = new OpsTaskAttrBasicReplica();
        BeanUtil.copyProperties(replica, s);
        return s;
    }
}