package sdata.ops.base.system.model.vo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import sdata.ops.base.system.model.entity.OpsViewTemplate;

import java.text.DateFormat;
import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class OpsViewTemplateVO {

    private String id;

    /**
     * 视图名称
     */
    private String name;

    /**
     * 描述内容
     */
    private String description;

    /**
     * 视图类型（dashboard, report等）
     */
    private String type;

    /**
     * 存储视图配置（布局、筛选条件、样式等）
     */
    private JSONObject props;

    /**
     *
     */
    private JSONArray cardList;

    private String createBy;

    private String updateBy;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;


    private String groupId;

    /**
     * 是否显隐
     */

    public OpsViewTemplateVO convert(OpsViewTemplate entity, Map<String,String> nameIdMapping,Map<String,String> groupMapper) {
        if (entity == null) {
            return null;
        }
        OpsViewTemplateVO vo = new OpsViewTemplateVO();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setType(entity.getType());
        vo.setProps(JSONUtil.parseObj(entity.getProps()));
        vo.setCardList(JSONUtil.parseArray(entity.getCardList()));
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setCreateBy(nameIdMapping.get(entity.getCreateBy()));
        vo.setUpdateBy(nameIdMapping.get(entity.getUpdateBy()));
        if(groupMapper!=null&&!groupMapper.isEmpty()){
            vo.setGroupId(groupMapper.get(entity.getId()));
        }
        return vo;
    }


}
