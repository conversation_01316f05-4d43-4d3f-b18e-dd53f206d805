package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数据授权来源信息配置表
 * @TableName ops_sys_data_model
 */
@TableName(value ="ops_sys_data_model")
@Data
public class OpsSysDataModel implements Serializable {
    /**
     * 数据模型ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 模型指标命名
     */
    private String modelName;

    /**
     * 指标数据集id
     */
    private String dataSourceId;

    /**
     * 数据结果内容配置
     */
    private String dataResultConfig;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}