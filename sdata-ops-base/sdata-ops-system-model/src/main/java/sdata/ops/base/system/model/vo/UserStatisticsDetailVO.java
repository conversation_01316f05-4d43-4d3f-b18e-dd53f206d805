package sdata.ops.base.system.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 统计指标详情列表vo
 *
 * <AUTHOR>
 * @Date 2025/1/9 9:29
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class UserStatisticsDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 已完成
     */
    private List<OpsTaskGenInfo> complete = new ArrayList<>();

    /**
     * 未完成
     */
    private List<OpsTaskGenInfo> unComplete = new ArrayList<>();

}
