package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import sdata.ops.base.system.model.dto.OrgUserDTO;

/**
 * 部门表
 * @TableName ops_sys_org
 */
@TableName(value ="ops_sys_org")
@Data
public class OpsSysOrg implements Serializable {
    /**
     * 组织架构id
     */
    @TableId(type=IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 父级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 1 机构 2 部门 3岗位 (树状组织关系一个属性定义)
     */
    private String orgType;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String deleted;

    /**
     * 是否需要统计标志 0 否 1 是
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private int stSign;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<OpsSysOrg> children = new ArrayList<>();
    @TableField(exist = false)
    private List<OrgUserDTO>  userlist=new ArrayList<>();

    @TableField(exist = false)
    private Map<String,OpsSysOrg> idMap=new HashMap<>();
}