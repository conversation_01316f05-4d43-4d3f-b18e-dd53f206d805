package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonSerializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName ops_view
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="ops_view")
public class OpsView extends BaseEntity implements Serializable {


    /**
     * 视图名称
     */
    private String name;

    /**
     * 描述内容
     */
    private String description;

    /**
     * 视图类型（dashboard, report等）
     */
    private String type;

    /**
     * 存储视图配置（布局、筛选条件、样式等）
     */
    private String props;

    /**
     * 
     */
    private String cardList;

    /**
     * 来源分类：system（系统预制）、user（用户创建）
     */
    private String source;

    /**
     * 关联预置模板id
     */
    private String templateId;

    @TableField(exist = false)
    private Integer isVisible;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



}