package sdata.ops.base.system.model.vo;


import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class LeaderInfoVO {

    //名称
    private String name;

    //id
    private String id;

    //类型  1 部门 2 岗位  3 模板 4 人员/岗位
    private int type;

    //总数
    private Long total;

    //完成
    private Long over;

    //未完成
    private Long unOver;

    //完成率
    private String rate;

    //子
    private List<LeaderInfoVO> child = new ArrayList<>();

    //任务详情
    private List<OpsTaskGenInfo> detail;

    /**
     * 任务详情-完成
     */
    private List<OpsTaskGenInfo> detailComplete = new ArrayList<>();

    /**
     * 任务详情-未完成
     */
    private List<OpsTaskGenInfo> detailUnComplete = new ArrayList<>();

}
