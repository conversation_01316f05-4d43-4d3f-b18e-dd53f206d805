package sdata.ops.base.system.model.vo;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import sdata.ops.base.system.model.entity.OpsView;

@Data
@Accessors(chain = true)
public class OpsViewVO {

    private String id;

    /**
     * 视图名称
     */
    private String name;

    /**
     * 描述内容
     */
    private String description;

    /**
     * 视图类型（dashboard, report等）
     */
    private String type;

    /**
     * 存储视图配置（布局、筛选条件、样式等）
     */
    private JSONObject props;

    /**
     *
     */
    private JSONArray cardList;

    /**
     * 来源分类：system（系统预制）、user（用户创建）
     */
    private String source;

    /**
     * 关联预置模板id
     */
    private String templateId;

    /**
     * 是否显隐
     */
    private Boolean isVisible;

    public OpsViewVO convert(OpsView entity,int isVisible) {
        if (entity == null) {
            return null;
        }
        OpsViewVO vo = new OpsViewVO();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setType(entity.getType());
        vo.setProps(JSONUtil.parseObj(entity.getProps()));
        vo.setCardList(JSONUtil.parseArray(entity.getCardList()));
        vo.setTemplateId(entity.getTemplateId());
        vo.setSource(entity.getSource());
        vo.setIsVisible(isVisible == 0);
        return vo;
    }


}
