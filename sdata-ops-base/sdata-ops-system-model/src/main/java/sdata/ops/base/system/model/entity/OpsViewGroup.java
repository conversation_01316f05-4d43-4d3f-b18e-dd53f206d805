package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @TableName ops_view_group
 */
@Data
@EqualsAndHashCode
@TableName(value = "ops_view_group")
public class OpsViewGroup implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    private String name;

    /**
     *
     */
    private Long pId;

    /**
     *
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     */
    @TableLogic
    private Integer deleted;


    @TableField(exist = false)
    private List<OpsViewGroup> children = new ArrayList<>();

    @TableField(exist = false)
    private int count;


}