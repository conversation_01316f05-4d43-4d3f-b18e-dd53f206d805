package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 交接班，任务配置相亲表
 * @TableName OPS_SHIFT_CONF_LIST
 */
@TableName(value ="ops_shift_conf_list")
@Data
public class OpsShiftConfList implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 交接配置id
     */
    private String shiftId;

    /**
     * 类型 0是经办 1 是复核   2 是岗位信息自动转移配置
     */
    private Integer taskType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}