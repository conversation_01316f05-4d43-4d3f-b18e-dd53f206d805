package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/9/19 10:10
 */
@Data
@TableName(value = "ops_task_fund_info")
public class OpsTaskFundInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务单元复制id
     */
    private String taskReplicaId;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 产品名称
     */
    private String fundName;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 任务完成状态 1进行中  3已完成 -1弃用
     */
    private String completeStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 生成日期 yyyyMMdd
     */
    private String bizDate;

    private String dataId;

    @TableField(exist = false)
    private String taskOwnerId;

    @TableField(exist = false)
    private String taskOwnerType;

    @TableField(exist = false)
    private String taskOwnerVal;
    @TableField(exist = false)
    private String ownerOrgId;

    @TableField(exist = false)
    private String taskBindTemplateId;
}
