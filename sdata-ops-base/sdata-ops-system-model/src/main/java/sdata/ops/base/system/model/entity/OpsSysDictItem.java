package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 字典项表
 * @TableName ops_sys_dict_item
 */
@TableName(value ="ops_sys_dict_item")
@Data
public class OpsSysDictItem {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型，如：status, permission, etc
     */
    private String dictType;

    /**
     * 字典键，如：0, 1, admin
     */
    private String dictCode;

    /**
     * 字典值，如：禁用、启用、管理员
     */
    private String dictValue;

    /**
     * 状态：0-正常，1-已删除
     */
    @TableLogic
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}