package sdata.ops.base.system.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class TaskScriptResultVO implements Serializable {

    /**
     * 状态,true任务开启\false任务未开启
     */
    private Boolean status = false;

    /**
     * 任务开始时间
     */
    private String startTime;

    /**
     * 任务结束时间
     */
    private String endTime;

    /**
     * 工作量计数
     */
    private Integer count;

    //......
}
